{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: UserRole\n  redirectTo?: string\n}\n\nexport function ProtectedRoute({\n  children,\n  requiredRole,\n  redirectTo = '/auth'\n}: ProtectedRouteProps) {\n  const { user, profile, loading, hasRole } = useAuth()\n  const router = useRouter()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (mounted && !loading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && !hasRole(requiredRole)) {\n        router.push('/unauthorized')\n        return\n      }\n    }\n  }, [mounted, user, profile, loading, requiredRole, hasRole, router, redirectTo])\n\n  // Show loading until mounted and auth is resolved\n  if (!mounted || loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin text-blue-600 mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-300\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect\n  }\n\n  if (requiredRole && !hasRole(requiredRole)) {\n    return null // Will redirect\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EACZ,aAAa,OAAO,EACA;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,CAAC,SAAS;YACvB,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;gBAC1C,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAS;QAAS;QAAc;QAAS;QAAQ;KAAW;IAE/E,kDAAkD;IAClD,IAAI,CAAC,WAAW,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,IAAI,gBAAgB,CAAC,QAAQ,eAAe;QAC1C,OAAO,KAAK,gBAAgB;;IAC9B;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,kHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,eAAe;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,cAAc;YAC3D,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,8OAAC,4IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,kHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAW,kHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAhBA;;;;;;;;;AAkBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,8OAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,8OAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,8OAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEAAI,WAAU;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,8OAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;AAeO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,8OAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;AAGO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC", "debugId": null}}, {"offset": {"line": 1710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCart } from '@/contexts/CartContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const { cartCount, wishlistCount } = useCart()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)\n\n  // استخدام عناصر القائمة فقط (بدون لوحة التحكم في القائمة الرئيسية)\n  const { user, profile } = useAuth()\n  const allNavItems = navItems\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartCount > 99 ? '99+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartCount > 9 ? '9+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;;;;;AA0DO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG;QAAC;KAAS;IAIb,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,6CAA6C;gBAC7C,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,aAAa,KAAK,SAAS,IAAI,EAAE;gBACnC,OAAO;oBACL,6CAA6C;oBAC7C,QAAQ,IAAI,CAAC;oBACb,aAAa,EAAE;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,mDAAmD;gBACnD,QAAQ,IAAI,CAAC,kDAAkD;gBAC/D,aAAa,EAAE;YACjB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,WAAW,UAAU,kBAAmB,UAAU,MAAM,GAAG,IAAI,sBAAsB;IAE3F,mEAAmE;IACnE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,8OAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,8OAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,8OAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOlC,8OAAC,2JAAA,CAAA,uBAAoB;;;;;8CAGrB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;sDACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,8OAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,8OAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;0DACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 2902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 3165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/UserForm.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Switch } from '@/components/ui/switch'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  User, \n  Mail, \n  Phone, \n  Shield, \n  GraduationCap, \n  School, \n  Truck,\n  Eye,\n  EyeOff\n} from 'lucide-react'\n\ninterface UserFormProps {\n  onSubmit: (userData: any) => void\n  onCancel: () => void\n}\n\ninterface UserFormData {\n  email: string\n  full_name: string\n  phone: string\n  role: 'admin' | 'student' | 'school' | 'delivery'\n  password: string\n  confirmPassword: string\n  status: 'active' | 'inactive'\n  verified: boolean\n  // حقول إضافية حسب الدور\n  student_id?: string\n  school_name?: string\n  school_address?: string\n  delivery_company?: string\n  delivery_license?: string\n  notes?: string\n}\n\nexport function UserForm({ onSubmit, onCancel }: UserFormProps) {\n  const [formData, setFormData] = useState<UserFormData>({\n    email: '',\n    full_name: '',\n    phone: '',\n    role: 'student',\n    password: '',\n    confirmPassword: '',\n    status: 'active',\n    verified: false,\n    notes: ''\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [loading, setLoading] = useState(false)\n\n  // التحقق من صحة البيانات\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    // التحقق من البريد الإلكتروني\n    if (!formData.email) {\n      newErrors.email = 'البريد الإلكتروني مطلوب'\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'البريد الإلكتروني غير صحيح'\n    }\n\n    // التحقق من الاسم\n    if (!formData.full_name) {\n      newErrors.full_name = 'الاسم الكامل مطلوب'\n    } else if (formData.full_name.length < 2) {\n      newErrors.full_name = 'الاسم يجب أن يكون أكثر من حرفين'\n    }\n\n    // التحقق من كلمة المرور\n    if (!formData.password) {\n      newErrors.password = 'كلمة المرور مطلوبة'\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'\n    }\n\n    // التحقق من تأكيد كلمة المرور\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'كلمة المرور غير متطابقة'\n    }\n\n    // التحقق من الهاتف\n    if (formData.phone && !/^\\+?[1-9]\\d{1,14}$/.test(formData.phone)) {\n      newErrors.phone = 'رقم الهاتف غير صحيح'\n    }\n\n    // التحقق من الحقول الإضافية حسب الدور\n    if (formData.role === 'student' && formData.student_id && formData.student_id.length < 3) {\n      newErrors.student_id = 'رقم الطالب يجب أن يكون 3 أحرف على الأقل'\n    }\n\n    if (formData.role === 'school' && !formData.school_name) {\n      newErrors.school_name = 'اسم المدرسة مطلوب'\n    }\n\n    if (formData.role === 'delivery' && !formData.delivery_company) {\n      newErrors.delivery_company = 'اسم شركة التوصيل مطلوب'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  // معالجة إرسال النموذج\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    setLoading(true)\n    try {\n      // إزالة تأكيد كلمة المرور من البيانات المرسلة\n      const { confirmPassword, ...userData } = formData\n      await onSubmit(userData)\n    } catch (error) {\n      console.error('Error creating user:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // تحديث البيانات\n  const updateFormData = (field: keyof UserFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // إزالة الخطأ عند التعديل\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  // الحصول على أيقونة الدور\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'admin': return <Shield className=\"h-4 w-4\" />\n      case 'student': return <GraduationCap className=\"h-4 w-4\" />\n      case 'school': return <School className=\"h-4 w-4\" />\n      case 'delivery': return <Truck className=\"h-4 w-4\" />\n      default: return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  return (\n    <Dialog open={true} onOpenChange={onCancel}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"arabic-text\">إضافة مستخدم جديد</DialogTitle>\n          <DialogDescription className=\"arabic-text\">\n            أدخل بيانات المستخدم الجديد وحدد دوره في النظام\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* المعلومات الأساسية */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg arabic-text\">المعلومات الأساسية</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"full_name\" className=\"arabic-text\">الاسم الكامل *</Label>\n                  <Input\n                    id=\"full_name\"\n                    value={formData.full_name}\n                    onChange={(e) => updateFormData('full_name', e.target.value)}\n                    placeholder=\"أدخل الاسم الكامل\"\n                    className={errors.full_name ? 'border-red-500' : ''}\n                  />\n                  {errors.full_name && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.full_name}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"email\" className=\"arabic-text\">البريد الإلكتروني *</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => updateFormData('email', e.target.value)}\n                    placeholder=\"<EMAIL>\"\n                    className={errors.email ? 'border-red-500' : ''}\n                  />\n                  {errors.email && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.email}</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"phone\" className=\"arabic-text\">رقم الهاتف</Label>\n                  <Input\n                    id=\"phone\"\n                    value={formData.phone}\n                    onChange={(e) => updateFormData('phone', e.target.value)}\n                    placeholder=\"+971501234567\"\n                    className={errors.phone ? 'border-red-500' : ''}\n                  />\n                  {errors.phone && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.phone}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"role\" className=\"arabic-text\">الدور *</Label>\n                  <Select value={formData.role} onValueChange={(value: any) => updateFormData('role', value)}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"اختر الدور\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"student\">\n                        <div className=\"flex items-center gap-2\">\n                          <GraduationCap className=\"h-4 w-4\" />\n                          طالب\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"school\">\n                        <div className=\"flex items-center gap-2\">\n                          <School className=\"h-4 w-4\" />\n                          مدرسة\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"delivery\">\n                        <div className=\"flex items-center gap-2\">\n                          <Truck className=\"h-4 w-4\" />\n                          شركة توصيل\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"admin\">\n                        <div className=\"flex items-center gap-2\">\n                          <Shield className=\"h-4 w-4\" />\n                          مدير\n                        </div>\n                      </SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* كلمة المرور */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg arabic-text\">كلمة المرور</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"password\" className=\"arabic-text\">كلمة المرور *</Label>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={formData.password}\n                      onChange={(e) => updateFormData('password', e.target.value)}\n                      placeholder=\"أدخل كلمة المرور\"\n                      className={errors.password ? 'border-red-500' : ''}\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"absolute left-2 top-1/2 transform -translate-y-1/2\"\n                      onClick={() => setShowPassword(!showPassword)}\n                    >\n                      {showPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                    </Button>\n                  </div>\n                  {errors.password && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.password}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"confirmPassword\" className=\"arabic-text\">تأكيد كلمة المرور *</Label>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"confirmPassword\"\n                      type={showConfirmPassword ? 'text' : 'password'}\n                      value={formData.confirmPassword}\n                      onChange={(e) => updateFormData('confirmPassword', e.target.value)}\n                      placeholder=\"أعد إدخال كلمة المرور\"\n                      className={errors.confirmPassword ? 'border-red-500' : ''}\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"absolute left-2 top-1/2 transform -translate-y-1/2\"\n                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    >\n                      {showConfirmPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                    </Button>\n                  </div>\n                  {errors.confirmPassword && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.confirmPassword}</p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* الحقول الإضافية حسب الدور */}\n          {formData.role === 'student' && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg arabic-text\">معلومات الطالب</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"student_id\" className=\"arabic-text\">رقم الطالب</Label>\n                  <Input\n                    id=\"student_id\"\n                    value={formData.student_id || ''}\n                    onChange={(e) => updateFormData('student_id', e.target.value)}\n                    placeholder=\"STU2024001\"\n                    className={errors.student_id ? 'border-red-500' : ''}\n                  />\n                  {errors.student_id && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.student_id}</p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {formData.role === 'school' && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg arabic-text\">معلومات المدرسة</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"school_name\" className=\"arabic-text\">اسم المدرسة *</Label>\n                  <Input\n                    id=\"school_name\"\n                    value={formData.school_name || ''}\n                    onChange={(e) => updateFormData('school_name', e.target.value)}\n                    placeholder=\"جامعة الإمارات العربية المتحدة\"\n                    className={errors.school_name ? 'border-red-500' : ''}\n                  />\n                  {errors.school_name && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.school_name}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"school_address\" className=\"arabic-text\">عنوان المدرسة</Label>\n                  <Textarea\n                    id=\"school_address\"\n                    value={formData.school_address || ''}\n                    onChange={(e) => updateFormData('school_address', e.target.value)}\n                    placeholder=\"العنوان الكامل للمدرسة\"\n                    rows={3}\n                  />\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {formData.role === 'delivery' && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg arabic-text\">معلومات شركة التوصيل</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"delivery_company\" className=\"arabic-text\">اسم الشركة *</Label>\n                  <Input\n                    id=\"delivery_company\"\n                    value={formData.delivery_company || ''}\n                    onChange={(e) => updateFormData('delivery_company', e.target.value)}\n                    placeholder=\"شركة التوصيل السريع\"\n                    className={errors.delivery_company ? 'border-red-500' : ''}\n                  />\n                  {errors.delivery_company && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.delivery_company}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"delivery_license\" className=\"arabic-text\">رقم الترخيص</Label>\n                  <Input\n                    id=\"delivery_license\"\n                    value={formData.delivery_license || ''}\n                    onChange={(e) => updateFormData('delivery_license', e.target.value)}\n                    placeholder=\"رقم ترخيص الشركة\"\n                  />\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* الإعدادات */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg arabic-text\">إعدادات الحساب</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-0.5\">\n                  <Label className=\"arabic-text\">حالة الحساب</Label>\n                  <p className=\"text-sm text-gray-500 arabic-text\">\n                    تحديد ما إذا كان الحساب نشطاً أم لا\n                  </p>\n                </div>\n                <Switch\n                  checked={formData.status === 'active'}\n                  onCheckedChange={(checked) => updateFormData('status', checked ? 'active' : 'inactive')}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-0.5\">\n                  <Label className=\"arabic-text\">حساب محقق</Label>\n                  <p className=\"text-sm text-gray-500 arabic-text\">\n                    تحديد ما إذا كان الحساب محققاً أم لا\n                  </p>\n                </div>\n                <Switch\n                  checked={formData.verified}\n                  onCheckedChange={(checked) => updateFormData('verified', checked)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"notes\" className=\"arabic-text\">ملاحظات</Label>\n                <Textarea\n                  id=\"notes\"\n                  value={formData.notes || ''}\n                  onChange={(e) => updateFormData('notes', e.target.value)}\n                  placeholder=\"ملاحظات إضافية حول المستخدم\"\n                  rows={3}\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          <DialogFooter>\n            <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n              إلغاء\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? 'جاري الإضافة...' : 'إضافة المستخدم'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxBA;;;;;;;;;;;;AA2DO,SAAS,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,OAAO;QACP,WAAW;QACX,OAAO;QACP,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,yBAAyB;IACzB,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,8BAA8B;QAC9B,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,kBAAkB;QAClB,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,UAAU,SAAS,GAAG;QACxB,OAAO,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;YACxC,UAAU,SAAS,GAAG;QACxB;QAEA,wBAAwB;QACxB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,8BAA8B;QAC9B,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,UAAU,eAAe,GAAG;QAC9B;QAEA,mBAAmB;QACnB,IAAI,SAAS,KAAK,IAAI,CAAC,qBAAqB,IAAI,CAAC,SAAS,KAAK,GAAG;YAChE,UAAU,KAAK,GAAG;QACpB;QAEA,sCAAsC;QACtC,IAAI,SAAS,IAAI,KAAK,aAAa,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,GAAG;YACxF,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,SAAS,IAAI,KAAK,YAAY,CAAC,SAAS,WAAW,EAAE;YACvD,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,SAAS,IAAI,KAAK,cAAc,CAAC,SAAS,gBAAgB,EAAE;YAC9D,UAAU,gBAAgB,GAAG;QAC/B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,uBAAuB;IACvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,IAAI;YACF,8CAA8C;YAC9C,MAAM,EAAE,eAAe,EAAE,GAAG,UAAU,GAAG;YACzC,MAAM,SAAS;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAiB,CAAC,OAA2B;QACjD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,0BAA0B;QAC1B,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,0BAA0B;IAC1B,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAS,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACvC,KAAK;gBAAW,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAU,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAY,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACzC;gBAAS,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAc;;;;;;sCACrC,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAc;;;;;;;;;;;;8BAK7C,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAc;;;;;;sEACnD,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC3D,aAAY;4DACZ,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;wDAElD,OAAO,SAAS,kBACf,8OAAC;4DAAE,WAAU;sEAAoC,OAAO,SAAS;;;;;;;;;;;;8DAIrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAc;;;;;;sEAC/C,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wDAE9C,OAAO,KAAK,kBACX,8OAAC;4DAAE,WAAU;sEAAoC,OAAO,KAAK;;;;;;;;;;;;;;;;;;sDAKnE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAc;;;;;;sEAC/C,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wDAE9C,OAAO,KAAK,kBACX,8OAAC;4DAAE,WAAU;sEAAoC,OAAO,KAAK;;;;;;;;;;;;8DAIjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAAc;;;;;;sEAC9C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,OAAO,SAAS,IAAI;4DAAE,eAAe,CAAC,QAAe,eAAe,QAAQ;;8EAClF,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sFACZ,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;sFAIzC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;sFAIlC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;sFAIjC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAY9C,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAc;;;;;;kEAClD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,eAAe,SAAS;gEAC9B,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC1D,aAAY;gEACZ,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;0EAElD,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,gBAAgB,CAAC;0EAE/B,6BAAe,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAGnE,OAAO,QAAQ,kBACd,8OAAC;wDAAE,WAAU;kEAAoC,OAAO,QAAQ;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAkB,WAAU;kEAAc;;;;;;kEACzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,sBAAsB,SAAS;gEACrC,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,eAAe,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACjE,aAAY;gEACZ,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;0EAEzD,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,uBAAuB,CAAC;0EAEtC,oCAAsB,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAG1E,OAAO,eAAe,kBACrB,8OAAC;wDAAE,WAAU;kEAAoC,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQhF,SAAS,IAAI,KAAK,2BACjB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAa,WAAU;0DAAc;;;;;;0DACpD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,UAAU,IAAI;gDAC9B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC5D,aAAY;gDACZ,WAAW,OAAO,UAAU,GAAG,mBAAmB;;;;;;4CAEnD,OAAO,UAAU,kBAChB,8OAAC;gDAAE,WAAU;0DAAoC,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;wBAO3E,SAAS,IAAI,KAAK,0BACjB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAc,WAAU;8DAAc;;;;;;8DACrD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,WAAW,IAAI;oDAC/B,UAAU,CAAC,IAAM,eAAe,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;oDACZ,WAAW,OAAO,WAAW,GAAG,mBAAmB;;;;;;gDAEpD,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;8DAAoC,OAAO,WAAW;;;;;;;;;;;;sDAIvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAiB,WAAU;8DAAc;;;;;;8DACxD,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,cAAc,IAAI;oDAClC,UAAU,CAAC,IAAM,eAAe,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;wBAOf,SAAS,IAAI,KAAK,4BACjB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAmB,WAAU;8DAAc;;;;;;8DAC1D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,gBAAgB,IAAI;oDACpC,UAAU,CAAC,IAAM,eAAe,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDAClE,aAAY;oDACZ,WAAW,OAAO,gBAAgB,GAAG,mBAAmB;;;;;;gDAEzD,OAAO,gBAAgB,kBACtB,8OAAC;oDAAE,WAAU;8DAAoC,OAAO,gBAAgB;;;;;;;;;;;;sDAI5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAmB,WAAU;8DAAc;;;;;;8DAC1D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,gBAAgB,IAAI;oDACpC,UAAU,CAAC,IAAM,eAAe,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDAClE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAQtB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAc;;;;;;sEAC/B,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;;;;;;;8DAInD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,SAAS,MAAM,KAAK;oDAC7B,iBAAiB,CAAC,UAAY,eAAe,UAAU,UAAU,WAAW;;;;;;;;;;;;sDAIhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAc;;;;;;sEAC/B,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;;;;;;;8DAInD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,SAAS,QAAQ;oDAC1B,iBAAiB,CAAC,UAAY,eAAe,YAAY;;;;;;;;;;;;sDAI7D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAc;;;;;;8DAC/C,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,KAAK,IAAI;oDACzB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;oDACvD,aAAY;oDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAMd,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAAU;;;;;;8CAG3D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,UAAU,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 4303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/EditUserDialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Switch } from '@/components/ui/switch'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  User, \n  Mail, \n  Phone, \n  Shield, \n  GraduationCap, \n  School, \n  Truck,\n  Eye,\n  EyeOff\n} from 'lucide-react'\n\ninterface User {\n  id: string\n  email: string\n  full_name: string\n  phone?: string\n  role: 'admin' | 'student' | 'school' | 'delivery'\n  status: 'active' | 'inactive' | 'suspended'\n  created_at: string\n  last_login?: string\n  avatar_url?: string\n  school_name?: string\n  delivery_company?: string\n  student_id?: string\n  verified: boolean\n}\n\ninterface EditUserDialogProps {\n  user: User\n  onSubmit: (userData: any) => void\n  onCancel: () => void\n}\n\ninterface UserFormData {\n  email: string\n  full_name: string\n  phone: string\n  role: 'admin' | 'student' | 'school' | 'delivery'\n  status: 'active' | 'inactive' | 'suspended'\n  verified: boolean\n  // حقول إضافية حسب الدور\n  student_id?: string\n  school_name?: string\n  school_address?: string\n  delivery_company?: string\n  delivery_license?: string\n  notes?: string\n  // كلمة مرور جديدة (اختيارية)\n  new_password?: string\n  confirm_new_password?: string\n}\n\nexport function EditUserDialog({ user, onSubmit, onCancel }: EditUserDialogProps) {\n  const [formData, setFormData] = useState<UserFormData>({\n    email: user.email,\n    full_name: user.full_name,\n    phone: user.phone || '',\n    role: user.role,\n    status: user.status,\n    verified: user.verified,\n    student_id: user.student_id || '',\n    school_name: user.school_name || '',\n    delivery_company: user.delivery_company || '',\n    new_password: '',\n    confirm_new_password: ''\n  })\n  const [showNewPassword, setShowNewPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [loading, setLoading] = useState(false)\n\n  // التحقق من صحة البيانات\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    // التحقق من البريد الإلكتروني\n    if (!formData.email) {\n      newErrors.email = 'البريد الإلكتروني مطلوب'\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'البريد الإلكتروني غير صحيح'\n    }\n\n    // التحقق من الاسم\n    if (!formData.full_name) {\n      newErrors.full_name = 'الاسم الكامل مطلوب'\n    } else if (formData.full_name.length < 2) {\n      newErrors.full_name = 'الاسم يجب أن يكون أكثر من حرفين'\n    }\n\n    // التحقق من كلمة المرور الجديدة (إذا تم إدخالها)\n    if (formData.new_password) {\n      if (formData.new_password.length < 6) {\n        newErrors.new_password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'\n      }\n      \n      if (formData.new_password !== formData.confirm_new_password) {\n        newErrors.confirm_new_password = 'كلمة المرور غير متطابقة'\n      }\n    }\n\n    // التحقق من الهاتف\n    if (formData.phone && !/^\\+?[1-9]\\d{1,14}$/.test(formData.phone)) {\n      newErrors.phone = 'رقم الهاتف غير صحيح'\n    }\n\n    // التحقق من الحقول الإضافية حسب الدور\n    if (formData.role === 'student' && formData.student_id && formData.student_id.length < 3) {\n      newErrors.student_id = 'رقم الطالب يجب أن يكون 3 أحرف على الأقل'\n    }\n\n    if (formData.role === 'school' && !formData.school_name) {\n      newErrors.school_name = 'اسم المدرسة مطلوب'\n    }\n\n    if (formData.role === 'delivery' && !formData.delivery_company) {\n      newErrors.delivery_company = 'اسم شركة التوصيل مطلوب'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  // معالجة إرسال النموذج\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    setLoading(true)\n    try {\n      // إزالة كلمات المرور إذا لم يتم تغييرها\n      const { confirm_new_password, ...userData } = formData\n      if (!userData.new_password) {\n        delete userData.new_password\n      }\n      \n      await onSubmit(userData)\n    } catch (error) {\n      console.error('Error updating user:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // تحديث البيانات\n  const updateFormData = (field: keyof UserFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // إزالة الخطأ عند التعديل\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  return (\n    <Dialog open={true} onOpenChange={onCancel}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"arabic-text\">تعديل بيانات المستخدم</DialogTitle>\n          <DialogDescription className=\"arabic-text\">\n            تعديل بيانات المستخدم: {user.full_name}\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* المعلومات الأساسية */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg arabic-text\">المعلومات الأساسية</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"full_name\" className=\"arabic-text\">الاسم الكامل *</Label>\n                  <Input\n                    id=\"full_name\"\n                    value={formData.full_name}\n                    onChange={(e) => updateFormData('full_name', e.target.value)}\n                    placeholder=\"أدخل الاسم الكامل\"\n                    className={errors.full_name ? 'border-red-500' : ''}\n                  />\n                  {errors.full_name && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.full_name}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"email\" className=\"arabic-text\">البريد الإلكتروني *</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => updateFormData('email', e.target.value)}\n                    placeholder=\"<EMAIL>\"\n                    className={errors.email ? 'border-red-500' : ''}\n                  />\n                  {errors.email && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.email}</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"phone\" className=\"arabic-text\">رقم الهاتف</Label>\n                  <Input\n                    id=\"phone\"\n                    value={formData.phone}\n                    onChange={(e) => updateFormData('phone', e.target.value)}\n                    placeholder=\"+971501234567\"\n                    className={errors.phone ? 'border-red-500' : ''}\n                  />\n                  {errors.phone && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.phone}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"role\" className=\"arabic-text\">الدور *</Label>\n                  <Select value={formData.role} onValueChange={(value: any) => updateFormData('role', value)}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"اختر الدور\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"student\">\n                        <div className=\"flex items-center gap-2\">\n                          <GraduationCap className=\"h-4 w-4\" />\n                          طالب\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"school\">\n                        <div className=\"flex items-center gap-2\">\n                          <School className=\"h-4 w-4\" />\n                          مدرسة\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"delivery\">\n                        <div className=\"flex items-center gap-2\">\n                          <Truck className=\"h-4 w-4\" />\n                          شركة توصيل\n                        </div>\n                      </SelectItem>\n                      <SelectItem value=\"admin\">\n                        <div className=\"flex items-center gap-2\">\n                          <Shield className=\"h-4 w-4\" />\n                          مدير\n                        </div>\n                      </SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* تغيير كلمة المرور */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg arabic-text\">تغيير كلمة المرور</CardTitle>\n              <CardDescription className=\"arabic-text\">\n                اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"new_password\" className=\"arabic-text\">كلمة المرور الجديدة</Label>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"new_password\"\n                      type={showNewPassword ? 'text' : 'password'}\n                      value={formData.new_password || ''}\n                      onChange={(e) => updateFormData('new_password', e.target.value)}\n                      placeholder=\"كلمة المرور الجديدة\"\n                      className={errors.new_password ? 'border-red-500' : ''}\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"absolute left-2 top-1/2 transform -translate-y-1/2\"\n                      onClick={() => setShowNewPassword(!showNewPassword)}\n                    >\n                      {showNewPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                    </Button>\n                  </div>\n                  {errors.new_password && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.new_password}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"confirm_new_password\" className=\"arabic-text\">تأكيد كلمة المرور الجديدة</Label>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"confirm_new_password\"\n                      type={showConfirmPassword ? 'text' : 'password'}\n                      value={formData.confirm_new_password || ''}\n                      onChange={(e) => updateFormData('confirm_new_password', e.target.value)}\n                      placeholder=\"تأكيد كلمة المرور الجديدة\"\n                      className={errors.confirm_new_password ? 'border-red-500' : ''}\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"absolute left-2 top-1/2 transform -translate-y-1/2\"\n                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    >\n                      {showConfirmPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                    </Button>\n                  </div>\n                  {errors.confirm_new_password && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.confirm_new_password}</p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* الحقول الإضافية حسب الدور */}\n          {formData.role === 'student' && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg arabic-text\">معلومات الطالب</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"student_id\" className=\"arabic-text\">رقم الطالب</Label>\n                  <Input\n                    id=\"student_id\"\n                    value={formData.student_id || ''}\n                    onChange={(e) => updateFormData('student_id', e.target.value)}\n                    placeholder=\"STU2024001\"\n                    className={errors.student_id ? 'border-red-500' : ''}\n                  />\n                  {errors.student_id && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.student_id}</p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {formData.role === 'school' && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg arabic-text\">معلومات المدرسة</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"school_name\" className=\"arabic-text\">اسم المدرسة *</Label>\n                  <Input\n                    id=\"school_name\"\n                    value={formData.school_name || ''}\n                    onChange={(e) => updateFormData('school_name', e.target.value)}\n                    placeholder=\"جامعة الإمارات العربية المتحدة\"\n                    className={errors.school_name ? 'border-red-500' : ''}\n                  />\n                  {errors.school_name && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.school_name}</p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {formData.role === 'delivery' && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg arabic-text\">معلومات شركة التوصيل</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"delivery_company\" className=\"arabic-text\">اسم الشركة *</Label>\n                  <Input\n                    id=\"delivery_company\"\n                    value={formData.delivery_company || ''}\n                    onChange={(e) => updateFormData('delivery_company', e.target.value)}\n                    placeholder=\"شركة التوصيل السريع\"\n                    className={errors.delivery_company ? 'border-red-500' : ''}\n                  />\n                  {errors.delivery_company && (\n                    <p className=\"text-sm text-red-500 arabic-text\">{errors.delivery_company}</p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* الإعدادات */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg arabic-text\">إعدادات الحساب</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label className=\"arabic-text\">حالة الحساب</Label>\n                <Select value={formData.status} onValueChange={(value: any) => updateFormData('status', value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"اختر الحالة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"active\">نشط</SelectItem>\n                    <SelectItem value=\"inactive\">غير نشط</SelectItem>\n                    <SelectItem value=\"suspended\">معلق</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"space-y-0.5\">\n                  <Label className=\"arabic-text\">حساب محقق</Label>\n                  <p className=\"text-sm text-gray-500 arabic-text\">\n                    تحديد ما إذا كان الحساب محققاً أم لا\n                  </p>\n                </div>\n                <Switch\n                  checked={formData.verified}\n                  onCheckedChange={(checked) => updateFormData('verified', checked)}\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          <DialogFooter>\n            <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n              إلغاء\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? 'جاري التحديث...' : 'حفظ التغييرات'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAOA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxBA;;;;;;;;;;;AA6EO,SAAS,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAuB;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,SAAS;QACzB,OAAO,KAAK,KAAK,IAAI;QACrB,MAAM,KAAK,IAAI;QACf,QAAQ,KAAK,MAAM;QACnB,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU,IAAI;QAC/B,aAAa,KAAK,WAAW,IAAI;QACjC,kBAAkB,KAAK,gBAAgB,IAAI;QAC3C,cAAc;QACd,sBAAsB;IACxB;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,yBAAyB;IACzB,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,8BAA8B;QAC9B,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,kBAAkB;QAClB,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,UAAU,SAAS,GAAG;QACxB,OAAO,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;YACxC,UAAU,SAAS,GAAG;QACxB;QAEA,iDAAiD;QACjD,IAAI,SAAS,YAAY,EAAE;YACzB,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,GAAG;gBACpC,UAAU,YAAY,GAAG;YAC3B;YAEA,IAAI,SAAS,YAAY,KAAK,SAAS,oBAAoB,EAAE;gBAC3D,UAAU,oBAAoB,GAAG;YACnC;QACF;QAEA,mBAAmB;QACnB,IAAI,SAAS,KAAK,IAAI,CAAC,qBAAqB,IAAI,CAAC,SAAS,KAAK,GAAG;YAChE,UAAU,KAAK,GAAG;QACpB;QAEA,sCAAsC;QACtC,IAAI,SAAS,IAAI,KAAK,aAAa,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,GAAG;YACxF,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,SAAS,IAAI,KAAK,YAAY,CAAC,SAAS,WAAW,EAAE;YACvD,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,SAAS,IAAI,KAAK,cAAc,CAAC,SAAS,gBAAgB,EAAE;YAC9D,UAAU,gBAAgB,GAAG;QAC/B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,uBAAuB;IACvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,IAAI;YACF,wCAAwC;YACxC,MAAM,EAAE,oBAAoB,EAAE,GAAG,UAAU,GAAG;YAC9C,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC1B,OAAO,SAAS,YAAY;YAC9B;YAEA,MAAM,SAAS;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAiB,CAAC,OAA2B;QACjD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,0BAA0B;QAC1B,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAc;;;;;;sCACrC,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;;gCAAc;gCACjB,KAAK,SAAS;;;;;;;;;;;;;8BAI1C,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAc;;;;;;sEACnD,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC3D,aAAY;4DACZ,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;wDAElD,OAAO,SAAS,kBACf,8OAAC;4DAAE,WAAU;sEAAoC,OAAO,SAAS;;;;;;;;;;;;8DAIrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAc;;;;;;sEAC/C,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wDAE9C,OAAO,KAAK,kBACX,8OAAC;4DAAE,WAAU;sEAAoC,OAAO,KAAK;;;;;;;;;;;;;;;;;;sDAKnE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAc;;;;;;sEAC/C,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wDAE9C,OAAO,KAAK,kBACX,8OAAC;4DAAE,WAAU;sEAAoC,OAAO,KAAK;;;;;;;;;;;;8DAIjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAAc;;;;;;sEAC9C,8OAAC,kIAAA,CAAA,SAAM;4DAAC,OAAO,SAAS,IAAI;4DAAE,eAAe,CAAC,QAAe,eAAe,QAAQ;;8EAClF,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sFACZ,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;sFAIzC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;sFAIlC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;sFAIjC,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAChB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAY9C,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAe,WAAU;kEAAc;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,kBAAkB,SAAS;gEACjC,OAAO,SAAS,YAAY,IAAI;gEAChC,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC9D,aAAY;gEACZ,WAAW,OAAO,YAAY,GAAG,mBAAmB;;;;;;0EAEtD,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,mBAAmB,CAAC;0EAElC,gCAAkB,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAGtE,OAAO,YAAY,kBAClB,8OAAC;wDAAE,WAAU;kEAAoC,OAAO,YAAY;;;;;;;;;;;;0DAIxE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAuB,WAAU;kEAAc;;;;;;kEAC9D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,sBAAsB,SAAS;gEACrC,OAAO,SAAS,oBAAoB,IAAI;gEACxC,UAAU,CAAC,IAAM,eAAe,wBAAwB,EAAE,MAAM,CAAC,KAAK;gEACtE,aAAY;gEACZ,WAAW,OAAO,oBAAoB,GAAG,mBAAmB;;;;;;0EAE9D,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,uBAAuB,CAAC;0EAEtC,oCAAsB,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAAe,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAG1E,OAAO,oBAAoB,kBAC1B,8OAAC;wDAAE,WAAU;kEAAoC,OAAO,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQrF,SAAS,IAAI,KAAK,2BACjB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAa,WAAU;0DAAc;;;;;;0DACpD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,UAAU,IAAI;gDAC9B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC5D,aAAY;gDACZ,WAAW,OAAO,UAAU,GAAG,mBAAmB;;;;;;4CAEnD,OAAO,UAAU,kBAChB,8OAAC;gDAAE,WAAU;0DAAoC,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;wBAO3E,SAAS,IAAI,KAAK,0BACjB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;0DAAc;;;;;;0DACrD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,WAAW,IAAI;gDAC/B,UAAU,CAAC,IAAM,eAAe,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC7D,aAAY;gDACZ,WAAW,OAAO,WAAW,GAAG,mBAAmB;;;;;;4CAEpD,OAAO,WAAW,kBACjB,8OAAC;gDAAE,WAAU;0DAAoC,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;wBAO5E,SAAS,IAAI,KAAK,4BACjB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAAc;;;;;;0DAC1D,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,gBAAgB,IAAI;gDACpC,UAAU,CAAC,IAAM,eAAe,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;gDACZ,WAAW,OAAO,gBAAgB,GAAG,mBAAmB;;;;;;4CAEzD,OAAO,gBAAgB,kBACtB,8OAAC;gDAAE,WAAU;0DAAoC,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAQlF,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAc;;;;;;8DAC/B,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO,SAAS,MAAM;oDAAE,eAAe,CAAC,QAAe,eAAe,UAAU;;sEACtF,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;;;;;;;;;;;;;;;;;;;sDAKpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAc;;;;;;sEAC/B,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;;;;;;;8DAInD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,SAAS,QAAQ;oDAC1B,iBAAiB,CAAC,UAAY,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAMjE,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAAU;;;;;;8CAG3D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,UAAU,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 5350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/confirm-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\n\ninterface ConfirmDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'default' | 'destructive'\n  onConfirm: () => void\n}\n\nexport function ConfirmDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = 'تأكيد',\n  cancelText = 'إلغاء',\n  variant = 'default',\n  onConfirm\n}: ConfirmDialogProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onOpenChange(false)\n  }\n\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <AlertDialogTitle className=\"arabic-text\">{title}</AlertDialogTitle>\n          <AlertDialogDescription className=\"arabic-text\">\n            {description}\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter>\n          <AlertDialogCancel className=\"arabic-text\">{cancelText}</AlertDialogCancel>\n          <AlertDialogAction\n            onClick={handleConfirm}\n            className={variant === 'destructive' ? 'bg-red-600 hover:bg-red-700' : ''}\n          >\n            {confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAwBO,SAAS,cAAc,EAC5B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,OAAO,EACrB,aAAa,OAAO,EACpB,UAAU,SAAS,EACnB,SAAS,EACU;IACnB,MAAM,gBAAgB;QACpB;QACA,aAAa;IACf;IAEA,qBACE,8OAAC,2IAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;8BACjB,8OAAC,2IAAA,CAAA,oBAAiB;;sCAChB,8OAAC,2IAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAe;;;;;;sCAC3C,8OAAC,2IAAA,CAAA,yBAAsB;4BAAC,WAAU;sCAC/B;;;;;;;;;;;;8BAGL,8OAAC,2IAAA,CAAA,oBAAiB;;sCAChB,8OAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAe;;;;;;sCAC5C,8OAAC,2IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,WAAW,YAAY,gBAAgB,gCAAgC;sCAEtE;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 5562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'\nimport { Button } from './button'\n\nexport interface Toast {\n  id: string\n  title?: string\n  description: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  duration?: number\n}\n\ninterface ToastProps {\n  toast: Toast\n  onRemove: (id: string) => void\n}\n\nexport function ToastComponent({ toast, onRemove }: ToastProps) {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id)\n    }, toast.duration || 5000)\n\n    return () => clearTimeout(timer)\n  }, [toast.id, toast.duration, onRemove])\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5 text-red-600\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-600\" />\n    }\n  }\n\n  const getBgColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200'\n      case 'error':\n        return 'bg-red-50 border-red-200'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200'\n      case 'info':\n        return 'bg-blue-50 border-blue-200'\n    }\n  }\n\n  return (\n    <div className={`rounded-lg border p-4 shadow-lg ${getBgColor()} animate-in slide-in-from-right-full`}>\n      <div className=\"flex items-start gap-3\">\n        {getIcon()}\n        <div className=\"flex-1\">\n          {toast.title && (\n            <h4 className=\"font-medium text-gray-900 arabic-text\">{toast.title}</h4>\n          )}\n          <p className=\"text-sm text-gray-700 arabic-text\">{toast.description}</p>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-6 w-6 p-0\"\n          onClick={() => onRemove(toast.id)}\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n\ninterface ToastContainerProps {\n  toasts: Toast[]\n  onRemove: (id: string) => void\n}\n\nexport function ToastContainer({ toasts, onRemove }: ToastContainerProps) {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  )\n}\n\n// Hook لإدارة التوست\nexport function useToast() {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const addToast = (toast: Omit<Toast, 'id'>) => {\n    const id = Date.now().toString()\n    setToasts(prev => [...prev, { ...toast, id }])\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const success = (description: string, title?: string) => {\n    addToast({ type: 'success', description, title })\n  }\n\n  const error = (description: string, title?: string) => {\n    addToast({ type: 'error', description, title })\n  }\n\n  const warning = (description: string, title?: string) => {\n    addToast({ type: 'warning', description, title })\n  }\n\n  const info = (description: string, title?: string) => {\n    addToast({ type: 'info', description, title })\n  }\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success,\n    error,\n    warning,\n    info\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAmBO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAc;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,SAAS,MAAM,EAAE;QACnB,GAAG,MAAM,QAAQ,IAAI;QAErB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gCAAgC,EAAE,aAAa,oCAAoC,CAAC;kBACnG,cAAA,8OAAC;YAAI,WAAU;;gBACZ;8BACD,8OAAC;oBAAI,WAAU;;wBACZ,MAAM,KAAK,kBACV,8OAAC;4BAAG,WAAU;sCAAyC,MAAM,KAAK;;;;;;sCAEpE,8OAAC;4BAAE,WAAU;sCAAqC,MAAM,WAAW;;;;;;;;;;;;8BAErE,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,MAAM,EAAE;8BAEhC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;AAOO,SAAS,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAuB;IACtE,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;AAGO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,KAAK;oBAAE;gBAAG;aAAE;IAC/C;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,UAAU,CAAC,aAAqB;QACpC,SAAS;YAAE,MAAM;YAAW;YAAa;QAAM;IACjD;IAEA,MAAM,QAAQ,CAAC,aAAqB;QAClC,SAAS;YAAE,MAAM;YAAS;YAAa;QAAM;IAC/C;IAEA,MAAM,UAAU,CAAC,aAAqB;QACpC,SAAS;YAAE,MAAM;YAAW;YAAa;QAAM;IACjD;IAEA,MAAM,OAAO,CAAC,aAAqB;QACjC,SAAS;YAAE,MAAM;YAAQ;YAAa;QAAM;IAC9C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5775, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/users/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute'\nimport { Navigation } from '@/components/Navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { UserForm } from '@/components/admin/UserForm'\nimport { EditUserDialog } from '@/components/admin/EditUserDialog'\nimport { ConfirmDialog } from '@/components/ui/confirm-dialog'\nimport { ToastContainer, useToast } from '@/components/ui/toast'\nimport Link from 'next/link'\nimport { \n  Users, \n  Plus, \n  Search, \n  Filter, \n  MoreHorizontal,\n  Edit,\n  Trash2,\n  Eye,\n  UserCheck,\n  UserX,\n  Shield,\n  GraduationCap,\n  School,\n  Truck,\n  ArrowLeft,\n  Download,\n  Upload,\n  RefreshCw\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface User {\n  id: string\n  email: string\n  full_name: string\n  phone?: string\n  role: 'admin' | 'student' | 'school' | 'delivery'\n  status: 'active' | 'inactive' | 'suspended'\n  created_at: string\n  last_login?: string\n  avatar_url?: string\n  school_name?: string\n  delivery_company?: string\n  student_id?: string\n  verified: boolean\n}\n\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    email: '<EMAIL>',\n    full_name: 'مدير النظام',\n    phone: '+971501234567',\n    role: 'admin',\n    status: 'active',\n    created_at: '2024-01-15T10:00:00Z',\n    last_login: '2024-01-20T14:30:00Z',\n    verified: true\n  },\n  {\n    id: '2',\n    email: '<EMAIL>',\n    full_name: 'أحمد محمد علي',\n    phone: '+971507654321',\n    role: 'student',\n    status: 'active',\n    created_at: '2024-01-16T09:15:00Z',\n    last_login: '2024-01-20T12:45:00Z',\n    student_id: 'STU2024001',\n    verified: true\n  },\n  {\n    id: '3',\n    email: '<EMAIL>',\n    full_name: 'جامعة الإمارات',\n    phone: '+97126123456',\n    role: 'school',\n    status: 'active',\n    created_at: '2024-01-10T08:00:00Z',\n    last_login: '2024-01-20T11:20:00Z',\n    school_name: 'جامعة الإمارات العربية المتحدة',\n    verified: true\n  },\n  {\n    id: '4',\n    email: '<EMAIL>',\n    full_name: 'شركة التوصيل السريع',\n    phone: '+97144567890',\n    role: 'delivery',\n    status: 'active',\n    created_at: '2024-01-12T07:30:00Z',\n    last_login: '2024-01-19T16:10:00Z',\n    delivery_company: 'شركة التوصيل السريع',\n    verified: false\n  }\n]\n\nexport default function UsersManagementPage() {\n  const [users, setUsers] = useState<User[]>(mockUsers)\n  const [filteredUsers, setFilteredUsers] = useState<User[]>(mockUsers)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [roleFilter, setRoleFilter] = useState<string>('all')\n  const [statusFilter, setStatusFilter] = useState<string>('all')\n  const [showUserForm, setShowUserForm] = useState(false)\n  const [editingUser, setEditingUser] = useState<User | null>(null)\n  const [userToDelete, setUserToDelete] = useState<User | null>(null)\n  const [loading, setLoading] = useState(false)\n  const { toasts, removeToast, success: showToast } = useToast()\n\n  // تصفية المستخدمين\n  useEffect(() => {\n    let filtered = users\n\n    // تصفية بالبحث\n    if (searchTerm) {\n      filtered = filtered.filter(user => \n        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.phone?.includes(searchTerm) ||\n        user.student_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.school_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        user.delivery_company?.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // تصفية بالدور\n    if (roleFilter !== 'all') {\n      filtered = filtered.filter(user => user.role === roleFilter)\n    }\n\n    // تصفية بالحالة\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(user => user.status === statusFilter)\n    }\n\n    setFilteredUsers(filtered)\n  }, [users, searchTerm, roleFilter, statusFilter])\n\n  // إضافة مستخدم جديد\n  const handleAddUser = (userData: Partial<User>) => {\n    const newUser: User = {\n      id: Date.now().toString(),\n      email: userData.email!,\n      full_name: userData.full_name!,\n      phone: userData.phone,\n      role: userData.role!,\n      status: 'active',\n      created_at: new Date().toISOString(),\n      verified: false,\n      ...userData\n    }\n\n    setUsers(prev => [newUser, ...prev])\n    setShowUserForm(false)\n    showToast('تم إضافة المستخدم بنجاح', 'success')\n  }\n\n  // تعديل مستخدم\n  const handleEditUser = (userData: Partial<User>) => {\n    if (!editingUser) return\n\n    setUsers(prev => prev.map(user => \n      user.id === editingUser.id \n        ? { ...user, ...userData }\n        : user\n    ))\n    setEditingUser(null)\n    showToast('تم تحديث بيانات المستخدم بنجاح', 'success')\n  }\n\n  // حذف مستخدم\n  const handleDeleteUser = () => {\n    if (!userToDelete) return\n\n    setUsers(prev => prev.filter(user => user.id !== userToDelete.id))\n    setUserToDelete(null)\n    showToast('تم حذف المستخدم بنجاح', 'success')\n  }\n\n  // تغيير حالة المستخدم\n  const handleToggleUserStatus = (userId: string) => {\n    setUsers(prev => prev.map(user => \n      user.id === userId \n        ? { \n            ...user, \n            status: user.status === 'active' ? 'suspended' : 'active' \n          }\n        : user\n    ))\n    showToast('تم تحديث حالة المستخدم', 'success')\n  }\n\n  // تحديث حالة التحقق\n  const handleToggleVerification = (userId: string) => {\n    setUsers(prev => prev.map(user => \n      user.id === userId \n        ? { ...user, verified: !user.verified }\n        : user\n    ))\n    showToast('تم تحديث حالة التحقق', 'success')\n  }\n\n  // الحصول على أيقونة الدور\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'admin': return <Shield className=\"h-4 w-4\" />\n      case 'student': return <GraduationCap className=\"h-4 w-4\" />\n      case 'school': return <School className=\"h-4 w-4\" />\n      case 'delivery': return <Truck className=\"h-4 w-4\" />\n      default: return <Users className=\"h-4 w-4\" />\n    }\n  }\n\n  // الحصول على لون الحالة\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'\n      case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  // الحصول على نص الحالة\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'active': return 'نشط'\n      case 'inactive': return 'غير نشط'\n      case 'suspended': return 'معلق'\n      default: return status\n    }\n  }\n\n  // الحصول على نص الدور\n  const getRoleText = (role: string) => {\n    switch (role) {\n      case 'admin': return 'مدير'\n      case 'student': return 'طالب'\n      case 'school': return 'مدرسة'\n      case 'delivery': return 'توصيل'\n      default: return role\n    }\n  }\n\n  return (\n    <ProtectedRoute allowedRoles={['admin']}>\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n        <Navigation />\n        \n        <main className=\"container mx-auto px-4 py-8\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center gap-4 mb-4\">\n              <Button variant=\"outline\" size=\"sm\" asChild>\n                <Link href=\"/dashboard/admin\">\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  العودة للوحة التحكم\n                </Link>\n              </Button>\n            </div>\n            \n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text\">\n                  إدارة المستخدمين 👥\n                </h1>\n                <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n                  إدارة حسابات المستخدمين والصلاحيات والأدوار\n                </p>\n              </div>\n              <Button onClick={() => setShowUserForm(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                إضافة مستخدم\n              </Button>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium arabic-text\">إجمالي المستخدمين</CardTitle>\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"number text-2xl font-bold\">{users.length}</div>\n                <p className=\"text-xs text-muted-foreground arabic-text\">\n                  +{users.filter(u => u.status === 'active').length} نشط\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium arabic-text\">الطلاب</CardTitle>\n                <GraduationCap className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"number text-2xl font-bold\">\n                  {users.filter(u => u.role === 'student').length}\n                </div>\n                <p className=\"text-xs text-muted-foreground arabic-text\">\n                  مستخدم طالب\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium arabic-text\">المدارس</CardTitle>\n                <School className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"number text-2xl font-bold\">\n                  {users.filter(u => u.role === 'school').length}\n                </div>\n                <p className=\"text-xs text-muted-foreground arabic-text\">\n                  مؤسسة تعليمية\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium arabic-text\">شركات التوصيل</CardTitle>\n                <Truck className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"number text-2xl font-bold\">\n                  {users.filter(u => u.role === 'delivery').length}\n                </div>\n                <p className=\"text-xs text-muted-foreground arabic-text\">\n                  شركة توصيل\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Filters and Search */}\n          <Card className=\"mb-8\">\n            <CardHeader>\n              <CardTitle className=\"arabic-text\">البحث والتصفية</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                    <Input\n                      placeholder=\"البحث بالاسم، البريد الإلكتروني، الهاتف...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"pl-10 arabic-text\"\n                    />\n                  </div>\n                </div>\n                \n                <Select value={roleFilter} onValueChange={setRoleFilter}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"تصفية بالدور\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع الأدوار</SelectItem>\n                    <SelectItem value=\"admin\">مدير</SelectItem>\n                    <SelectItem value=\"student\">طالب</SelectItem>\n                    <SelectItem value=\"school\">مدرسة</SelectItem>\n                    <SelectItem value=\"delivery\">توصيل</SelectItem>\n                  </SelectContent>\n                </Select>\n\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\n                  <SelectTrigger className=\"w-full md:w-48\">\n                    <SelectValue placeholder=\"تصفية بالحالة\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">جميع الحالات</SelectItem>\n                    <SelectItem value=\"active\">نشط</SelectItem>\n                    <SelectItem value=\"inactive\">غير نشط</SelectItem>\n                    <SelectItem value=\"suspended\">معلق</SelectItem>\n                  </SelectContent>\n                </Select>\n\n                <div className=\"flex gap-2\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Download className=\"h-4 w-4 mr-2\" />\n                    تصدير\n                  </Button>\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Upload className=\"h-4 w-4 mr-2\" />\n                    استيراد\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Users Table */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"arabic-text\">\n                  قائمة المستخدمين ({filteredUsers.length})\n                </CardTitle>\n                <Button variant=\"outline\" size=\"sm\" onClick={() => window.location.reload()}>\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  تحديث\n                </Button>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b\">\n                      <th className=\"text-right py-3 px-4 font-medium arabic-text\">المستخدم</th>\n                      <th className=\"text-right py-3 px-4 font-medium arabic-text\">الدور</th>\n                      <th className=\"text-right py-3 px-4 font-medium arabic-text\">الحالة</th>\n                      <th className=\"text-right py-3 px-4 font-medium arabic-text\">التحقق</th>\n                      <th className=\"text-right py-3 px-4 font-medium arabic-text\">آخر دخول</th>\n                      <th className=\"text-right py-3 px-4 font-medium arabic-text\">الإجراءات</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {filteredUsers.map((user) => (\n                      <tr key={user.id} className=\"border-b hover:bg-gray-50 dark:hover:bg-gray-800\">\n                        <td className=\"py-4 px-4\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                              <span className=\"text-blue-600 dark:text-blue-400 font-medium\">\n                                {user.full_name.charAt(0)}\n                              </span>\n                            </div>\n                            <div>\n                              <p className=\"font-medium text-gray-900 dark:text-white arabic-text\">\n                                {user.full_name}\n                              </p>\n                              <p className=\"text-sm text-gray-500\">{user.email}</p>\n                              {user.phone && (\n                                <p className=\"text-xs text-gray-400\">{user.phone}</p>\n                              )}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <div className=\"flex items-center gap-2\">\n                            {getRoleIcon(user.role)}\n                            <span className=\"arabic-text\">{getRoleText(user.role)}</span>\n                          </div>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <Badge className={getStatusColor(user.status)}>\n                            {getStatusText(user.status)}\n                          </Badge>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          {user.verified ? (\n                            <Badge className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\">\n                              محقق\n                            </Badge>\n                          ) : (\n                            <Badge variant=\"outline\">\n                              غير محقق\n                            </Badge>\n                          )}\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <span className=\"text-sm text-gray-500\">\n                            {user.last_login \n                              ? new Date(user.last_login).toLocaleDateString('ar-AE')\n                              : 'لم يدخل بعد'\n                            }\n                          </span>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <div className=\"actions flex items-center gap-2\">\n                            <DropdownMenu>\n                              <DropdownMenuTrigger asChild>\n                                <Button variant=\"ghost\" size=\"sm\">\n                                  <MoreHorizontal className=\"h-4 w-4\" />\n                                </Button>\n                              </DropdownMenuTrigger>\n                              <DropdownMenuContent align=\"end\">\n                                <DropdownMenuLabel className=\"arabic-text\">الإجراءات</DropdownMenuLabel>\n                                <DropdownMenuSeparator />\n                                <DropdownMenuItem onClick={() => setEditingUser(user)}>\n                                  <Edit className=\"h-4 w-4 mr-2\" />\n                                  تعديل\n                                </DropdownMenuItem>\n                                <DropdownMenuItem onClick={() => handleToggleUserStatus(user.id)}>\n                                  {user.status === 'active' ? (\n                                    <>\n                                      <UserX className=\"h-4 w-4 mr-2\" />\n                                      تعليق\n                                    </>\n                                  ) : (\n                                    <>\n                                      <UserCheck className=\"h-4 w-4 mr-2\" />\n                                      تفعيل\n                                    </>\n                                  )}\n                                </DropdownMenuItem>\n                                <DropdownMenuItem onClick={() => handleToggleVerification(user.id)}>\n                                  {user.verified ? 'إلغاء التحقق' : 'تحقق'}\n                                </DropdownMenuItem>\n                                <DropdownMenuSeparator />\n                                <DropdownMenuItem \n                                  onClick={() => setUserToDelete(user)}\n                                  className=\"text-red-600\"\n                                >\n                                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                                  حذف\n                                </DropdownMenuItem>\n                              </DropdownMenuContent>\n                            </DropdownMenu>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n\n                {filteredUsers.length === 0 && (\n                  <div className=\"text-center py-8\">\n                    <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                    <p className=\"text-gray-500 arabic-text\">لا توجد مستخدمين</p>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </main>\n\n        {/* User Form Dialog */}\n        {showUserForm && (\n          <UserForm\n            onSubmit={handleAddUser}\n            onCancel={() => setShowUserForm(false)}\n          />\n        )}\n\n        {/* Edit User Dialog */}\n        {editingUser && (\n          <EditUserDialog\n            user={editingUser}\n            onSubmit={handleEditUser}\n            onCancel={() => setEditingUser(null)}\n          />\n        )}\n\n        {/* Delete Confirmation */}\n        {userToDelete && (\n          <ConfirmDialog\n            title=\"حذف المستخدم\"\n            message={`هل أنت متأكد من حذف المستخدم \"${userToDelete.full_name}\"؟ هذا الإجراء لا يمكن التراجع عنه.`}\n            onConfirm={handleDeleteUser}\n            onCancel={() => setUserToDelete(null)}\n            confirmText=\"حذف\"\n            cancelText=\"إلغاء\"\n            variant=\"destructive\"\n          />\n        )}\n\n        <ToastContainer toasts={toasts} onRemove={removeToast} />\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA7BA;;;;;;;;;;;;;;;;;AAmEA,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO;QACP,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,kBAAkB;QAClB,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAE3D,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,eAAe;QACf,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,KAAK,EAAE,SAAS,eACrB,KAAK,UAAU,EAAE,cAAc,SAAS,WAAW,WAAW,OAC9D,KAAK,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,KAAK,gBAAgB,EAAE,cAAc,SAAS,WAAW,WAAW;QAExE;QAEA,eAAe;QACf,IAAI,eAAe,OAAO;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACnD;QAEA,gBAAgB;QAChB,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACrD;QAEA,iBAAiB;IACnB,GAAG;QAAC;QAAO;QAAY;QAAY;KAAa;IAEhD,oBAAoB;IACpB,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAgB;YACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,OAAO,SAAS,KAAK;YACrB,WAAW,SAAS,SAAS;YAC7B,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;YACnB,QAAQ;YACR,YAAY,IAAI,OAAO,WAAW;YAClC,UAAU;YACV,GAAG,QAAQ;QACb;QAEA,SAAS,CAAA,OAAQ;gBAAC;mBAAY;aAAK;QACnC,gBAAgB;QAChB,UAAU,2BAA2B;IACvC;IAEA,eAAe;IACf,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,aAAa;QAElB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC,IACvB;QAEN,eAAe;QACf,UAAU,kCAAkC;IAC9C;IAEA,aAAa;IACb,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;QAEnB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa,EAAE;QAChE,gBAAgB;QAChB,UAAU,yBAAyB;IACrC;IAEA,sBAAsB;IACtB,MAAM,yBAAyB,CAAC;QAC9B,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,KAAK,WAAW,cAAc;gBACnD,IACA;QAEN,UAAU,0BAA0B;IACtC;IAEA,oBAAoB;IACpB,MAAM,2BAA2B,CAAC;QAChC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,UAAU,CAAC,KAAK,QAAQ;gBAAC,IACpC;QAEN,UAAU,wBAAwB;IACpC;IAEA,0BAA0B;IAC1B,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAS,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACvC,KAAK;gBAAW,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAU,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAY,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACzC;gBAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,iBAAc;QAAC,cAAc;YAAC;SAAQ;kBACrC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;8BAEX,8OAAC;oBAAK,WAAU;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,OAAO;kDACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;8CAM5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAG7E,8OAAC;oDAAE,WAAU;8DAAoD;;;;;;;;;;;;sDAInE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,gBAAgB;;8DACrC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAkC;;;;;;8DACvD,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAA6B,MAAM,MAAM;;;;;;8DACxD,8OAAC;oDAAE,WAAU;;wDAA4C;wDACrD,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;8CAKxD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAkC;;;;;;8DACvD,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;sDAE3B,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;8DAEjD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;8CAM7D,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAkC;;;;;;8DACvD,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;sDAEpB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;8CAM7D,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAkC;;;;;;8DACvD,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;;;;;;8DAElD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;sCAQ/D,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAc;;;;;;;;;;;8CAErC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAU;;;;;;;;;;;;;;;;;0DAKhB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe;;kEACxC,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;;;;;;;;;;;;;0DAIjC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAc,eAAe;;kEAC1C,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;;0EAC7B,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS7C,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;oDAAc;oDACd,cAAc,MAAM;oDAAC;;;;;;;0DAE1C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;;kEACvE,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;8CAK5C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;kEACC,cAAA,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAA+C;;;;;;8EAC7D,8OAAC;oEAAG,WAAU;8EAA+C;;;;;;8EAC7D,8OAAC;oEAAG,WAAU;8EAA+C;;;;;;8EAC7D,8OAAC;oEAAG,WAAU;8EAA+C;;;;;;8EAC7D,8OAAC;oEAAG,WAAU;8EAA+C;;;;;;8EAC7D,8OAAC;oEAAG,WAAU;8EAA+C;;;;;;;;;;;;;;;;;kEAGjE,8OAAC;kEACE,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gEAAiB,WAAU;;kFAC1B,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFAAK,WAAU;kGACb,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;8FAG3B,8OAAC;;sGACC,8OAAC;4FAAE,WAAU;sGACV,KAAK,SAAS;;;;;;sGAEjB,8OAAC;4FAAE,WAAU;sGAAyB,KAAK,KAAK;;;;;;wFAC/C,KAAK,KAAK,kBACT,8OAAC;4FAAE,WAAU;sGAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;kFAKxD,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAI,WAAU;;gFACZ,YAAY,KAAK,IAAI;8FACtB,8OAAC;oFAAK,WAAU;8FAAe,YAAY,KAAK,IAAI;;;;;;;;;;;;;;;;;kFAGxD,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAW,eAAe,KAAK,MAAM;sFACzC,cAAc,KAAK,MAAM;;;;;;;;;;;kFAG9B,8OAAC;wEAAG,WAAU;kFACX,KAAK,QAAQ,iBACZ,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAoE;;;;;iGAIrF,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAU;;;;;;;;;;;kFAK7B,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAK,WAAU;sFACb,KAAK,UAAU,GACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,WAC7C;;;;;;;;;;;kFAIR,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kGACX,8OAAC,4IAAA,CAAA,sBAAmB;wFAAC,OAAO;kGAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4FAAC,SAAQ;4FAAQ,MAAK;sGAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gGAAC,WAAU;;;;;;;;;;;;;;;;kGAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wFAAC,OAAM;;0GACzB,8OAAC,4IAAA,CAAA,oBAAiB;gGAAC,WAAU;0GAAc;;;;;;0GAC3C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0GACtB,8OAAC,4IAAA,CAAA,mBAAgB;gGAAC,SAAS,IAAM,eAAe;;kHAC9C,8OAAC,2MAAA,CAAA,OAAI;wGAAC,WAAU;;;;;;oGAAiB;;;;;;;0GAGnC,8OAAC,4IAAA,CAAA,mBAAgB;gGAAC,SAAS,IAAM,uBAAuB,KAAK,EAAE;0GAC5D,KAAK,MAAM,KAAK,yBACf;;sHACE,8OAAC,wMAAA,CAAA,QAAK;4GAAC,WAAU;;;;;;wGAAiB;;iIAIpC;;sHACE,8OAAC,gNAAA,CAAA,YAAS;4GAAC,WAAU;;;;;;wGAAiB;;;;;;;;0GAK5C,8OAAC,4IAAA,CAAA,mBAAgB;gGAAC,SAAS,IAAM,yBAAyB,KAAK,EAAE;0GAC9D,KAAK,QAAQ,GAAG,iBAAiB;;;;;;0GAEpC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0GACtB,8OAAC,4IAAA,CAAA,mBAAgB;gGACf,SAAS,IAAM,gBAAgB;gGAC/B,WAAU;;kHAEV,8OAAC,0MAAA,CAAA,SAAM;wGAAC,WAAU;;;;;;oGAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DArFtC,KAAK,EAAE;;;;;;;;;;;;;;;;4CAiGrB,cAAc,MAAM,KAAK,mBACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASpD,8BACC,8OAAC,uIAAA,CAAA,WAAQ;oBACP,UAAU;oBACV,UAAU,IAAM,gBAAgB;;;;;;gBAKnC,6BACC,8OAAC,6IAAA,CAAA,iBAAc;oBACb,MAAM;oBACN,UAAU;oBACV,UAAU,IAAM,eAAe;;;;;;gBAKlC,8BACC,8OAAC,6IAAA,CAAA,gBAAa;oBACZ,OAAM;oBACN,SAAS,CAAC,8BAA8B,EAAE,aAAa,SAAS,CAAC,mCAAmC,CAAC;oBACrG,WAAW;oBACX,UAAU,IAAM,gBAAgB;oBAChC,aAAY;oBACZ,YAAW;oBACX,SAAQ;;;;;;8BAIZ,8OAAC,iIAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAQ,UAAU;;;;;;;;;;;;;;;;;AAIlD", "debugId": null}}]}