globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/faq/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/src/components/theme-provider.tsx <module evaluation>":{"id":"[project]/src/components/theme-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/theme-provider.tsx":{"id":"[project]/src/components/theme-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/AuthContext.tsx <module evaluation>":{"id":"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/AuthContext.tsx":{"id":"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/NotificationContext.tsx <module evaluation>":{"id":"[project]/src/contexts/NotificationContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/NotificationContext.tsx":{"id":"[project]/src/contexts/NotificationContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/CartContext.tsx <module evaluation>":{"id":"[project]/src/contexts/CartContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/CartContext.tsx":{"id":"[project]/src/contexts/CartContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/chat/LiveChat.tsx <module evaluation>":{"id":"[project]/src/components/chat/LiveChat.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/chat/LiveChat.tsx":{"id":"[project]/src/components/chat/LiveChat.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/sonner/dist/index.mjs <module evaluation>":{"id":"[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/sonner/dist/index.mjs":{"id":"[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/app/error.tsx <module evaluation>":{"id":"[project]/src/app/error.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_87301fee._.js","/_next/static/chunks/src_c8284737._.js","/_next/static/chunks/src_app_error_tsx_cab46378._.js"],"async":false},"[project]/src/app/error.tsx":{"id":"[project]/src/app/error.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_87301fee._.js","/_next/static/chunks/src_c8284737._.js","/_next/static/chunks/src_app_error_tsx_cab46378._.js"],"async":false},"[project]/src/app/not-found.tsx <module evaluation>":{"id":"[project]/src/app/not-found.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_c9bb48c4._.js","/_next/static/chunks/src_249f1615._.js","/_next/static/chunks/src_app_not-found_tsx_cab46378._.js"],"async":false},"[project]/src/app/not-found.tsx":{"id":"[project]/src/app/not-found.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_c9bb48c4._.js","/_next/static/chunks/src_249f1615._.js","/_next/static/chunks/src_app_not-found_tsx_cab46378._.js"],"async":false},"[project]/src/app/faq/page.tsx <module evaluation>":{"id":"[project]/src/app/faq/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/src_6af744e0._.js","/_next/static/chunks/node_modules_02324c22._.js","/_next/static/chunks/src_app_faq_page_tsx_cab46378._.js"],"async":false},"[project]/src/app/faq/page.tsx":{"id":"[project]/src/app/faq/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_c36e6d01._.js","/_next/static/chunks/node_modules_44941a11._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/src_6af744e0._.js","/_next/static/chunks/node_modules_02324c22._.js","/_next/static/chunks/src_app_faq_page_tsx_cab46378._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/components/theme-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/theme-provider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js"],"async":false}},"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js"],"async":false}},"[project]/src/contexts/NotificationContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/NotificationContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js"],"async":false}},"[project]/src/contexts/CartContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/CartContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js"],"async":false}},"[project]/src/components/chat/LiveChat.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/chat/LiveChat.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js"],"async":false}},"[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js"],"async":false}},"[project]/src/app/error.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/error.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js","server/chunks/ssr/node_modules_5ab001e7._.js","server/chunks/ssr/src_41172e7c._.js"],"async":false}},"[project]/src/app/not-found.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/not-found.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_fa0a3fbf._.js","server/chunks/ssr/node_modules_next_a9a5f5cd._.js","server/chunks/ssr/node_modules_2e8cb4ca._.js","server/chunks/ssr/[root-of-the-server]__a6c5c0cb._.js"],"async":false}},"[project]/src/app/faq/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/faq/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_f36cb84e._.js","server/chunks/ssr/[root-of-the-server]__fa5a6036._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_7c7fe7fd._.js","server/chunks/ssr/[root-of-the-server]__a618b463._.js","server/chunks/ssr/node_modules_next_a9a5f5cd._.js","server/chunks/ssr/node_modules_3052632d._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/components/theme-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/theme-provider.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/AuthContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/contexts/NotificationContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/NotificationContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/contexts/CartContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/CartContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/components/chat/LiveChat.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/chat/LiveChat.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/sonner/dist/index.mjs (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/app/error.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/error.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/app/not-found.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/not-found.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}},"[project]/src/app/faq/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/faq/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/faq/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__3c1ed9db._.css","inlined":false}],"[project]/src/app/error":[{"path":"static/chunks/[root-of-the-server]__3c1ed9db._.css","inlined":false}],"[project]/src/app/not-found":[{"path":"static/chunks/[root-of-the-server]__3c1ed9db._.css","inlined":false}],"[project]/src/app/faq/page":[{"path":"static/chunks/[root-of-the-server]__3c1ed9db._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"[project]/src/app/layout":["static/chunks/src_c36e6d01._.js","static/chunks/node_modules_44941a11._.js","static/chunks/src_app_layout_tsx_007ca514._.js"],"[project]/src/app/error":["static/chunks/src_c36e6d01._.js","static/chunks/node_modules_44941a11._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/node_modules_87301fee._.js","static/chunks/src_c8284737._.js","static/chunks/src_app_error_tsx_cab46378._.js"],"[project]/src/app/not-found":["static/chunks/src_c36e6d01._.js","static/chunks/node_modules_44941a11._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/node_modules_c9bb48c4._.js","static/chunks/src_249f1615._.js","static/chunks/src_app_not-found_tsx_cab46378._.js"],"[project]/src/app/faq/page":["static/chunks/src_c36e6d01._.js","static/chunks/node_modules_44941a11._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/src_6af744e0._.js","static/chunks/node_modules_02324c22._.js","static/chunks/src_app_faq_page_tsx_cab46378._.js"]}}
