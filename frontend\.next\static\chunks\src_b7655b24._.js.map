{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,qHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,8CAA8C;YAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,eAAe;gBAAC;gBAAM;gBAAM;aAAK,CAAC,QAAQ,CAAC,cAAc;gBAC3D,UAAU;YACZ;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;;;AAPA;;;;;AAcO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D;GAzBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,6LAAC,+IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,qHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,6LAAC;oCAAK,WAAU;8CAAW,qHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB;GAnEgB;;QACmB,iIAAA,CAAA,iBAAc;;;KADjC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAhBA;;;;;;;;AAkBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,6LAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,6LAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9HgB;;QACqB,kIAAA,CAAA,UAAO;QAC5B,iIAAA,CAAA,iBAAc;;;KAFd", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;AAuBO,SAAS;;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,6LAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEAAI,WAAU;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,6LAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,wIAAA,CAAA,YAAS;;;;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAjMgB;;QAQV,0IAAA,CAAA,mBAAgB;;;KARN;AAgNT,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,6LAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,6LAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;MA5BgB;AA+BT,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC;IAbgB;;QACU,0IAAA,CAAA,mBAAgB;;;MAD1B", "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [cartItemsCount, setCartItemsCount] = useState(0)\n  const [wishlistCount, setWishlistCount] = useState(0)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Mock cart and wishlist counts - replace with actual data\n  useEffect(() => {\n    // Simulate getting cart items from localStorage or API\n    const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]')\n    setCartItemsCount(cartItems.length)\n\n    const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]')\n    setWishlistCount(wishlistItems.length)\n  }, [])\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)\n\n  // استخدام عناصر القائمة فقط (بدون لوحة التحكم في القائمة الرئيسية)\n  const { user, profile } = useAuth()\n  const allNavItems = navItems\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartItemsCount > 99 ? '99+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartItemsCount > 9 ? '9+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAyDO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,oBAAoB;QACtB;+BAAG;QAAC;KAAS;IAEb,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,uDAAuD;YACvD,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAClE,kBAAkB,UAAU,MAAM;YAElC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;YAC1E,iBAAiB,cAAc,MAAM;QACvC;+BAAG,EAAE;IAEL,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;uDAAiB;oBACrB,IAAI;wBACF,WAAW;wBACX,6CAA6C;wBAC7C,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,aAAa,KAAK,SAAS,IAAI,EAAE;wBACnC,OAAO;4BACL,6CAA6C;4BAC7C,QAAQ,IAAI,CAAC;4BACb,aAAa,EAAE;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,mDAAmD;wBACnD,QAAQ,IAAI,CAAC,kDAAkD;wBAC/D,aAAa,EAAE;oBACjB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,WAAW,UAAU,kBAAmB,UAAU,MAAM,GAAG,IAAI,sBAAsB;IAE3F,mEAAmE;IACnE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,6LAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,6LAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,6LAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOvC,6LAAC,8JAAA,CAAA,uBAAoB;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;sDACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOrC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,6LAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,6LAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;0DACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GArgBgB;;QACQ,iIAAA,CAAA,iBAAc;QACnB,qIAAA,CAAA,cAAW;QA4KF,kIAAA,CAAA,UAAO;;;KA9KnB;uCAugBD", "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,qKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 3556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 3631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/ImageUploader.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useCallback, useMemo } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport {\n  Upload,\n  X,\n  FileImage,\n  AlertCircle,\n  Check,\n  RotateCw,\n  Trash2,\n  WifiOff,\n  Brain\n} from 'lucide-react'\n\ninterface ImageFile {\n  file: File\n  preview: string\n  id: string\n  uploading?: boolean\n  uploaded?: boolean\n  error?: string\n  fallbackUrl?: string // للصور المحفوظة في localStorage\n}\n\ninterface ImageUploaderProps {\n  images: ImageFile[]\n  onImagesChange: (images: ImageFile[]) => void\n  maxImages?: number\n  maxSize?: number // بالبايت\n  acceptedTypes?: string[]\n  className?: string\n}\n\nexport function ImageUploader({\n  images,\n  onImagesChange,\n  maxImages = 10,\n  maxSize = 5 * 1024 * 1024, // 5MB\n  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],\n  className = \"\"\n}: ImageUploaderProps) {\n  const [dragActive, setDragActive] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})\n  const [isOnline, setIsOnline] = useState(true)\n  const [uploadErrors, setUploadErrors] = useState<string[]>([])\n  const [compressing, setCompressing] = useState<{ [key: string]: boolean }>({})\n  const [aiEnhancing, setAiEnhancing] = useState<{ [key: string]: boolean }>({})\n\n  // التأكد من أن images هو مصفوفة دائماً\n  const safeImages = useMemo(() => Array.isArray(images) ? images : [], [images])\n\n  // التحقق من حالة الاتصال\n  const checkConnection = useCallback(async (): Promise<boolean> => {\n    try {\n      const response = await fetch('/api/health', { method: 'HEAD' })\n      const online = response.ok\n      setIsOnline(online)\n      return online\n    } catch {\n      setIsOnline(false)\n      return false\n    }\n  }, [])\n\n  // التحقق من صحة الملف\n  const validateFile = useCallback((file: File): string | null => {\n    if (!acceptedTypes.includes(file.type)) {\n      return 'نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP'\n    }\n\n    if (file.size > maxSize) {\n      return `حجم الملف كبير جداً. الحد الأقصى ${(maxSize / 1024 / 1024).toFixed(1)} ميجابايت`\n    }\n\n    return null\n  }, [acceptedTypes, maxSize])\n\n  // حفظ الصورة في localStorage كنسخة احتياطية\n  const saveFallbackImage = useCallback((file: File, id: string): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      reader.onload = () => {\n        try {\n          const base64 = reader.result as string\n          localStorage.setItem(`fallback_image_${id}`, base64)\n          resolve(base64)\n        } catch (error) {\n          reject(error)\n        }\n      }\n      reader.onerror = reject\n      reader.readAsDataURL(file)\n    })\n  }, [])\n\n  // ضغط الصورة قبل الرفع\n  const compressImage = useCallback(async (file: File): Promise<File> => {\n    return new Promise((resolve) => {\n      const canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      const img = new Image()\n\n      img.onload = () => {\n        // تحديد الأبعاد الجديدة (أقصى عرض/ارتفاع 1200px)\n        const maxSize = 1200\n        let { width, height } = img\n\n        if (width > height) {\n          if (width > maxSize) {\n            height = (height * maxSize) / width\n            width = maxSize\n          }\n        } else {\n          if (height > maxSize) {\n            width = (width * maxSize) / height\n            height = maxSize\n          }\n        }\n\n        canvas.width = width\n        canvas.height = height\n\n        // رسم الصورة المضغوطة\n        ctx?.drawImage(img, 0, 0, width, height)\n\n        // تحويل إلى blob مع جودة 0.8\n        canvas.toBlob((blob) => {\n          if (blob) {\n            const compressedFile = new File([blob], file.name, {\n              type: 'image/jpeg',\n              lastModified: Date.now()\n            })\n            resolve(compressedFile)\n          } else {\n            resolve(file) // في حالة فشل الضغط، استخدم الملف الأصلي\n          }\n        }, 'image/jpeg', 0.8)\n      }\n\n      img.src = URL.createObjectURL(file)\n    })\n  }, [])\n\n  // تحسين الصورة باستخدام الذكاء الاصطناعي\n  const enhanceImageWithAI = useCallback(async (imageId: string): Promise<void> => {\n    setAiEnhancing(prev => ({ ...prev, [imageId]: true }))\n\n    try {\n      // محاكاة تحسين الصورة باستخدام الذكاء الاصطناعي\n      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))\n\n      // في التطبيق الحقيقي، ستكون هناك استدعاءات API للذكاء الاصطناعي\n      // مثل تحسين الجودة، إزالة الخلفية، تحسين الألوان، إلخ\n\n      console.log(`تم تحسين الصورة ${imageId} باستخدام الذكاء الاصطناعي`)\n\n    } catch (error) {\n      console.error('خطأ في تحسين الصورة:', error)\n    } finally {\n      setAiEnhancing(prev => ({ ...prev, [imageId]: false }))\n    }\n  }, [])\n\n  // ضغط متقدم للصورة مع تحسين الجودة\n  const advancedCompressImage = useCallback(async (file: File, id: string): Promise<File> => {\n    setCompressing(prev => ({ ...prev, [id]: true }))\n\n    try {\n      const compressedFile = await compressImage(file)\n\n      // إضافة تأخير لمحاكاة المعالجة\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      return compressedFile\n    } finally {\n      setCompressing(prev => ({ ...prev, [id]: false }))\n    }\n  }, [compressImage])\n\n  // رفع الصورة إلى الخادم مع إعادة المحاولة\n  const uploadImageWithRetry = useCallback(async (file: File, id: string, retries = 2): Promise<string> => {\n    // ضغط الصورة أولاً\n    const compressedFile = await compressImage(file)\n\n    for (let attempt = 1; attempt <= retries; attempt++) {\n      try {\n        setUploadProgress(prev => ({ ...prev, [id]: (attempt - 1) * 40 }))\n\n        const formData = new FormData()\n        formData.append('files', compressedFile)\n        formData.append('folder', 'products')\n\n        const response = await fetch('/api/upload', {\n          method: 'POST',\n          body: formData\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}`)\n        }\n\n        const data = await response.json()\n        setUploadProgress(prev => ({ ...prev, [id]: 100 }))\n\n        if (data.uploadedFiles && data.uploadedFiles.length > 0) {\n          return data.uploadedFiles[0].url\n        } else {\n          throw new Error('لم يتم إرجاع رابط الصورة')\n        }\n      } catch (error) {\n        console.error(`Upload attempt ${attempt} failed:`, error)\n\n        if (attempt === retries) {\n          // إذا فشلت جميع المحاولات، استخدم النسخة الاحتياطية\n          const fallbackUrl = await saveFallbackImage(file, id)\n          setUploadProgress(prev => ({ ...prev, [id]: 100 }))\n          return fallbackUrl\n        }\n\n        // انتظار قبل إعادة المحاولة\n        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))\n      }\n    }\n    throw new Error('فشل في رفع الصورة')\n  }, [compressImage, saveFallbackImage])\n\n  // معالجة رفع الملفات\n  const handleFiles = useCallback(async (files: FileList) => {\n    const newImages: ImageFile[] = []\n    const currentImages = Array.isArray(images) ? images : []\n    const currentCount = currentImages.length\n    const errors: string[] = []\n\n    // التحقق من الاتصال أولاً\n    await checkConnection()\n\n    for (let i = 0; i < files.length && currentCount + newImages.length < maxImages; i++) {\n      const file = files[i]\n      const error = validateFile(file)\n      const id = `${Date.now()}-${i}`\n\n      if (error) {\n        errors.push(`${file.name}: ${error}`)\n        continue\n      }\n\n      try {\n        // إنشاء معاينة للصورة\n        const preview = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader()\n          reader.onload = (e) => resolve(e.target?.result as string)\n          reader.onerror = reject\n          reader.readAsDataURL(file)\n        })\n\n        const newImage: ImageFile = {\n          file,\n          preview,\n          id,\n          uploading: true,\n          uploaded: false\n        }\n\n        newImages.push(newImage)\n      } catch (previewError) {\n        console.error('Preview creation failed:', previewError)\n        errors.push(`${file.name}: فشل في إنشاء المعاينة`)\n      }\n    }\n\n    // تحديث الحالة فوراً لإظهار جميع الصور الجديدة\n    if (newImages.length > 0) {\n      const updatedImages = [...images, ...newImages]\n      onImagesChange(updatedImages)\n\n      // رفع الصور بشكل متوازي مع ضغط متقدم\n      const uploadPromises = newImages.map(async (newImage) => {\n        try {\n          // ضغط الصورة أولاً\n          const compressedFile = await advancedCompressImage(newImage.file, newImage.id)\n\n          // رفع الصورة المضغوطة\n          const uploadedUrl = await uploadImageWithRetry(compressedFile, newImage.id)\n\n          // تحديث الصورة بالرابط المرفوع\n          onImagesChange(prev => prev.map(img =>\n            img.id === newImage.id\n              ? { ...img, uploading: false, uploaded: true, fallbackUrl: uploadedUrl }\n              : img\n          ))\n        } catch (uploadError) {\n          console.error('Upload failed:', uploadError)\n          errors.push(`${newImage.file.name}: فشل في الرفع`)\n\n          // تحديث حالة الصورة لإظهار الخطأ\n          onImagesChange(prev => prev.map(img =>\n            img.id === newImage.id\n              ? { ...img, uploading: false, uploaded: false, error: 'فشل في الرفع' }\n              : img\n          ))\n        }\n      })\n\n      // انتظار رفع جميع الصور\n      await Promise.allSettled(uploadPromises)\n    }\n\n    if (errors.length > 0) {\n      setUploadErrors(errors)\n      setTimeout(() => setUploadErrors([]), 5000) // إخفاء الأخطاء بعد 5 ثوان\n    }\n  }, [images, maxImages, onImagesChange, checkConnection, validateFile, uploadImageWithRetry, advancedCompressImage])\n\n  // معالجة السحب والإفلات\n  const handleDrag = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    if (e.type === \"dragenter\" || e.type === \"dragover\") {\n      setDragActive(true)\n    } else if (e.type === \"dragleave\") {\n      setDragActive(false)\n    }\n  }, [])\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setDragActive(false)\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFiles(e.dataTransfer.files)\n    }\n  }, [handleFiles])\n\n  // معالجة اختيار الملفات\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files.length > 0) {\n      handleFiles(e.target.files)\n      // إعادة تعيين قيمة الـ input لضمان إمكانية اختيار نفس الملف مرة أخرى\n      e.target.value = ''\n    }\n  }\n\n  // حذف صورة\n  const removeImage = (id: string) => {\n    const updatedImages = safeImages.filter(img => img.id !== id)\n    onImagesChange(updatedImages)\n  }\n\n  // تحريك صورة (ترتيب)\n  const moveImage = (fromIndex: number, toIndex: number) => {\n    const updatedImages = [...safeImages]\n    const [movedImage] = updatedImages.splice(fromIndex, 1)\n    updatedImages.splice(toIndex, 0, movedImage)\n    onImagesChange(updatedImages)\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* حالة الاتصال */}\n      {!isOnline && (\n        <Alert className=\"border-orange-200 bg-orange-50\">\n          <WifiOff className=\"h-4 w-4\" />\n          <AlertDescription className=\"arabic-text\">\n            لا يوجد اتصال بالإنترنت. سيتم حفظ الصور محلياً كنسخة احتياطية.\n          </AlertDescription>\n        </Alert>\n      )}\n\n      {/* رسائل الخطأ */}\n      {uploadErrors.length > 0 && (\n        <Alert className=\"border-red-200 bg-red-50\">\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>\n            <div className=\"space-y-1\">\n              {uploadErrors.map((error, index) => (\n                <div key={index} className=\"text-sm arabic-text\">{error}</div>\n              ))}\n            </div>\n          </AlertDescription>\n        </Alert>\n      )}\n      {/* منطقة رفع الصور */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n          dragActive\n            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'\n        } ${images.length >= maxImages ? 'opacity-50 pointer-events-none' : ''}`}\n        onDragEnter={handleDrag}\n        onDragLeave={handleDrag}\n        onDragOver={handleDrag}\n        onDrop={handleDrop}\n      >\n        <Upload className={`h-12 w-12 mx-auto mb-4 ${\n          dragActive ? 'text-blue-500' : 'text-gray-400'\n        }`} />\n        \n        <div className=\"space-y-2\">\n          <p className=\"text-lg font-medium arabic-text\">\n            {dragActive ? 'أفلت الصور هنا' : 'اسحب الصور هنا أو'}\n          </p>\n          \n          {safeImages.length < maxImages && (\n            <>\n              <input\n                ref={(input) => {\n                  if (input) {\n                    (window as any).fileInput = input\n                  }\n                }}\n                type=\"file\"\n                multiple\n                accept={acceptedTypes.join(',')}\n                className=\"hidden\"\n                onChange={handleInputChange}\n                disabled={safeImages.length >= maxImages}\n              />\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                size=\"sm\"\n                disabled={safeImages.length >= maxImages}\n                onClick={() => {\n                  const input = (window as any).fileInput\n                  if (input) {\n                    input.click()\n                  }\n                }}\n              >\n                <FileImage className=\"h-4 w-4 mr-2\" />\n                اختر الصور\n              </Button>\n            </>\n          )}\n        </div>\n\n        <div className=\"mt-4 space-y-1\">\n          <p className=\"text-sm text-gray-500 arabic-text\">\n            يمكنك رفع حتى {maxImages} صور بحجم أقصى {(maxSize / 1024 / 1024).toFixed(1)} ميجابايت لكل صورة\n          </p>\n          <p className=\"text-xs text-gray-400\">\n            الصيغ المدعومة: {acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}\n          </p>\n        </div>\n\n        {safeImages.length >= maxImages && (\n          <div className=\"mt-4 flex items-center justify-center gap-2 text-orange-600\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <span className=\"text-sm arabic-text\">تم الوصول للحد الأقصى من الصور</span>\n          </div>\n        )}\n      </div>\n\n      {/* عرض الصور المرفوعة */}\n      {safeImages.length > 0 && (\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium arabic-text\">\n              الصور المرفوعة ({safeImages.length}/{maxImages})\n            </h3>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onImagesChange([])}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <Trash2 className=\"h-4 w-4 mr-2\" />\n              حذف الكل\n            </Button>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n            {safeImages.map((image, index) => (\n              <Card key={image.id} className=\"relative group overflow-hidden\">\n                <CardContent className=\"p-0\">\n                  {image.error ? (\n                    <div className=\"aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20\">\n                      <div className=\"text-center p-4\">\n                        <AlertCircle className=\"h-8 w-8 text-red-500 mx-auto mb-2\" />\n                        <p className=\"text-xs text-red-600 arabic-text\">{image.error}</p>\n                      </div>\n                    </div>\n                  ) : (\n                    <>\n                      <img\n                        src={image.preview}\n                        alt={`صورة ${index + 1}`}\n                        className=\"w-full aspect-square object-cover\"\n                      />\n                      \n                      {/* شريط التقدم */}\n                      {image.uploading && (\n                        <div className=\"absolute inset-0 bg-black/60 flex items-center justify-center\">\n                          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[140px] shadow-lg\">\n                            <div className=\"flex items-center gap-2 mb-3\">\n                              <RotateCw className=\"h-5 w-5 animate-spin text-blue-500\" />\n                              <span className=\"text-sm font-medium arabic-text\">جاري الرفع...</span>\n                            </div>\n                            <Progress value={uploadProgress[image.id] || 0} className=\"h-3 mb-2\" />\n                            <div className=\"text-xs text-center text-gray-600 dark:text-gray-400\">\n                              {uploadProgress[image.id] || 0}%\n                            </div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* مؤشر الانتهاء */}\n                      {image.uploaded && (\n                        <div className=\"absolute top-2 left-2\">\n                          <Badge className={image.fallbackUrl?.startsWith('data:') ? 'bg-orange-600' : 'bg-green-600'}>\n                            <Check className=\"h-3 w-3 mr-1\" />\n                            {image.fallbackUrl?.startsWith('data:') ? 'محفوظ محلياً' : 'تم الرفع'}\n                          </Badge>\n                        </div>\n                      )}\n\n                      {/* مؤشر عدم الاتصال */}\n                      {!isOnline && image.uploaded && image.fallbackUrl?.startsWith('data:') && (\n                        <div className=\"absolute bottom-2 left-2\">\n                          <Badge variant=\"outline\" className=\"bg-white/90\">\n                            <WifiOff className=\"h-3 w-3 mr-1\" />\n                            غير متصل\n                          </Badge>\n                        </div>\n                      )}\n\n                      {/* أزرار التحكم */}\n                      <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <div className=\"flex gap-1\">\n                          {/* زر تحسين بالذكاء الاصطناعي */}\n                          {image.uploaded && !aiEnhancing[image.id] && (\n                            <Button\n                              type=\"button\"\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => enhanceImageWithAI(image.id)}\n                              className=\"h-8 w-8 p-0 bg-blue-600 text-white hover:bg-blue-700\"\n                              title=\"تحسين بالذكاء الاصطناعي\"\n                            >\n                              <Brain className=\"h-3 w-3\" />\n                            </Button>\n                          )}\n\n                          {/* مؤشر تحسين الذكاء الاصطناعي */}\n                          {aiEnhancing[image.id] && (\n                            <Button\n                              type=\"button\"\n                              size=\"sm\"\n                              variant=\"outline\"\n                              disabled\n                              className=\"h-8 w-8 p-0 bg-blue-600 text-white\"\n                            >\n                              <RotateCw className=\"h-3 w-3 animate-spin\" />\n                            </Button>\n                          )}\n\n                          {/* مؤشر الضغط */}\n                          {compressing[image.id] && (\n                            <Button\n                              type=\"button\"\n                              size=\"sm\"\n                              variant=\"outline\"\n                              disabled\n                              className=\"h-8 w-8 p-0 bg-orange-600 text-white\"\n                            >\n                              <RotateCw className=\"h-3 w-3 animate-spin\" />\n                            </Button>\n                          )}\n\n                          <Button\n                            type=\"button\"\n                            size=\"sm\"\n                            variant=\"destructive\"\n                            onClick={() => removeImage(image.id)}\n                            className=\"h-8 w-8 p-0\"\n                            disabled={image.uploading || compressing[image.id] || aiEnhancing[image.id]}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      {/* مؤشر الصورة الرئيسية */}\n                      {index === 0 && (\n                        <Badge className=\"absolute bottom-2 left-2 bg-blue-600\">\n                          الصورة الرئيسية\n                        </Badge>\n                      )}\n\n                      {/* أزرار الترتيب */}\n                      <div className=\"absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <div className=\"flex gap-1\">\n                          {index > 0 && (\n                            <Button\n                              type=\"button\"\n                              size=\"sm\"\n                              variant=\"secondary\"\n                              onClick={() => moveImage(index, index - 1)}\n                              className=\"h-6 w-6 p-0\"\n                            >\n                              ←\n                            </Button>\n                          )}\n                          {index < images.length - 1 && (\n                            <Button\n                              type=\"button\"\n                              size=\"sm\"\n                              variant=\"secondary\"\n                              onClick={() => moveImage(index, index + 1)}\n                              className=\"h-6 w-6 p-0\"\n                            >\n                              →\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </>\n                  )}\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAuCO,SAAS,cAAc,EAC5B,MAAM,EACN,cAAc,EACd,YAAY,EAAE,EACd,UAAU,IAAI,OAAO,IAAI,EACzB,gBAAgB;IAAC;IAAc;IAAa;CAAa,EACzD,YAAY,EAAE,EACK;;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACjF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAE5E,uCAAuC;IACvC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE,IAAM,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;4CAAE;QAAC;KAAO;IAE9E,yBAAyB;IACzB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAClC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,eAAe;oBAAE,QAAQ;gBAAO;gBAC7D,MAAM,SAAS,SAAS,EAAE;gBAC1B,YAAY;gBACZ,OAAO;YACT,EAAE,OAAM;gBACN,YAAY;gBACZ,OAAO;YACT;QACF;qDAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YAChC,IAAI,CAAC,cAAc,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACtC,OAAO;YACT;YAEA,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,OAAO,CAAC,iCAAiC,EAAE,CAAC,UAAU,OAAO,IAAI,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;YAC1F;YAEA,OAAO;QACT;kDAAG;QAAC;QAAe;KAAQ;IAE3B,4CAA4C;IAC5C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,MAAY;YACjD,OAAO,IAAI;gEAAQ,CAAC,SAAS;oBAC3B,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM;wEAAG;4BACd,IAAI;gCACF,MAAM,SAAS,OAAO,MAAM;gCAC5B,aAAa,OAAO,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;gCAC7C,QAAQ;4BACV,EAAE,OAAO,OAAO;gCACd,OAAO;4BACT;wBACF;;oBACA,OAAO,OAAO,GAAG;oBACjB,OAAO,aAAa,CAAC;gBACvB;;QACF;uDAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACvC,OAAO,IAAI;4DAAQ,CAAC;oBAClB,MAAM,SAAS,SAAS,aAAa,CAAC;oBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;oBAC9B,MAAM,MAAM,IAAI;oBAEhB,IAAI,MAAM;oEAAG;4BACX,iDAAiD;4BACjD,MAAM,UAAU;4BAChB,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;4BAExB,IAAI,QAAQ,QAAQ;gCAClB,IAAI,QAAQ,SAAS;oCACnB,SAAS,AAAC,SAAS,UAAW;oCAC9B,QAAQ;gCACV;4BACF,OAAO;gCACL,IAAI,SAAS,SAAS;oCACpB,QAAQ,AAAC,QAAQ,UAAW;oCAC5B,SAAS;gCACX;4BACF;4BAEA,OAAO,KAAK,GAAG;4BACf,OAAO,MAAM,GAAG;4BAEhB,sBAAsB;4BACtB,KAAK,UAAU,KAAK,GAAG,GAAG,OAAO;4BAEjC,6BAA6B;4BAC7B,OAAO,MAAM;4EAAC,CAAC;oCACb,IAAI,MAAM;wCACR,MAAM,iBAAiB,IAAI,KAAK;4CAAC;yCAAK,EAAE,KAAK,IAAI,EAAE;4CACjD,MAAM;4CACN,cAAc,KAAK,GAAG;wCACxB;wCACA,QAAQ;oCACV,OAAO;wCACL,QAAQ,MAAM,yCAAyC;;oCACzD;gCACF;2EAAG,cAAc;wBACnB;;oBAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;gBAChC;;QACF;mDAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YAC5C;iEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,QAAQ,EAAE;oBAAK,CAAC;;YAEpD,IAAI;gBACF,gDAAgD;gBAChD,MAAM,IAAI;qEAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,KAAK,MAAM,KAAK;;gBAExE,gEAAgE;gBAChE,sDAAsD;gBAEtD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ,0BAA0B,CAAC;YAEpE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR;qEAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,QAAQ,EAAE;wBAAM,CAAC;;YACvD;QACF;wDAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,OAAO,MAAY;YAC3D;oEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,GAAG,EAAE;oBAAK,CAAC;;YAE/C,IAAI;gBACF,MAAM,iBAAiB,MAAM,cAAc;gBAE3C,+BAA+B;gBAC/B,MAAM,IAAI;wEAAQ,CAAA,UAAW,WAAW,SAAS;;gBAEjD,OAAO;YACT,SAAU;gBACR;wEAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,GAAG,EAAE;wBAAM,CAAC;;YAClD;QACF;2DAAG;QAAC;KAAc;IAElB,0CAA0C;IAC1C,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO,MAAY,IAAY,UAAU,CAAC;YACjF,mBAAmB;YACnB,MAAM,iBAAiB,MAAM,cAAc;YAE3C,IAAK,IAAI,UAAU,GAAG,WAAW,SAAS,UAAW;gBACnD,IAAI;oBACF;2EAAkB,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI;4BAAG,CAAC;;oBAEhE,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,SAAS;oBACzB,SAAS,MAAM,CAAC,UAAU;oBAE1B,MAAM,WAAW,MAAM,MAAM,eAAe;wBAC1C,QAAQ;wBACR,MAAM;oBACR;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;oBAC3C;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC;2EAAkB,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,CAAC,GAAG,EAAE;4BAAI,CAAC;;oBAEjD,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,MAAM,GAAG,GAAG;wBACvD,OAAO,KAAK,aAAa,CAAC,EAAE,CAAC,GAAG;oBAClC,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,QAAQ,CAAC,EAAE;oBAEnD,IAAI,YAAY,SAAS;wBACvB,oDAAoD;wBACpD,MAAM,cAAc,MAAM,kBAAkB,MAAM;wBAClD;+EAAkB,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,CAAC,GAAG,EAAE;gCAAI,CAAC;;wBACjD,OAAO;oBACT;oBAEA,4BAA4B;oBAC5B,MAAM,IAAI;2EAAQ,CAAA,UAAW,WAAW,SAAS,OAAO;;gBAC1D;YACF;YACA,MAAM,IAAI,MAAM;QAClB;0DAAG;QAAC;QAAe;KAAkB;IAErC,qBAAqB;IACrB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YACrC,MAAM,YAAyB,EAAE;YACjC,MAAM,gBAAgB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACzD,MAAM,eAAe,cAAc,MAAM;YACzC,MAAM,SAAmB,EAAE;YAE3B,0BAA0B;YAC1B,MAAM;YAEN,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,IAAI,eAAe,UAAU,MAAM,GAAG,WAAW,IAAK;gBACpF,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,QAAQ,aAAa;gBAC3B,MAAM,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG;gBAE/B,IAAI,OAAO;oBACT,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,OAAO;oBACpC;gBACF;gBAEA,IAAI;oBACF,sBAAsB;oBACtB,MAAM,UAAU,MAAM,IAAI;kEAAgB,CAAC,SAAS;4BAClD,MAAM,SAAS,IAAI;4BACnB,OAAO,MAAM;0EAAG,CAAC,IAAM,QAAQ,EAAE,MAAM,EAAE;;4BACzC,OAAO,OAAO,GAAG;4BACjB,OAAO,aAAa,CAAC;wBACvB;;oBAEA,MAAM,WAAsB;wBAC1B;wBACA;wBACA;wBACA,WAAW;wBACX,UAAU;oBACZ;oBAEA,UAAU,IAAI,CAAC;gBACjB,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,uBAAuB,CAAC;gBACnD;YACF;YAEA,+CAA+C;YAC/C,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,gBAAgB;uBAAI;uBAAW;iBAAU;gBAC/C,eAAe;gBAEf,qCAAqC;gBACrC,MAAM,iBAAiB,UAAU,GAAG;6EAAC,OAAO;wBAC1C,IAAI;4BACF,mBAAmB;4BACnB,MAAM,iBAAiB,MAAM,sBAAsB,SAAS,IAAI,EAAE,SAAS,EAAE;4BAE7E,sBAAsB;4BACtB,MAAM,cAAc,MAAM,qBAAqB,gBAAgB,SAAS,EAAE;4BAE1E,+BAA+B;4BAC/B;yFAAe,CAAA,OAAQ,KAAK,GAAG;iGAAC,CAAA,MAC9B,IAAI,EAAE,KAAK,SAAS,EAAE,GAClB;gDAAE,GAAG,GAAG;gDAAE,WAAW;gDAAO,UAAU;gDAAM,aAAa;4CAAY,IACrE;;;wBAER,EAAE,OAAO,aAAa;4BACpB,QAAQ,KAAK,CAAC,kBAAkB;4BAChC,OAAO,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;4BAEjD,iCAAiC;4BACjC;yFAAe,CAAA,OAAQ,KAAK,GAAG;iGAAC,CAAA,MAC9B,IAAI,EAAE,KAAK,SAAS,EAAE,GAClB;gDAAE,GAAG,GAAG;gDAAE,WAAW;gDAAO,UAAU;gDAAO,OAAO;4CAAe,IACnE;;;wBAER;oBACF;;gBAEA,wBAAwB;gBACxB,MAAM,QAAQ,UAAU,CAAC;YAC3B;YAEA,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,gBAAgB;gBAChB;8DAAW,IAAM,gBAAgB,EAAE;6DAAG,MAAM,2BAA2B;;YACzE;QACF;iDAAG;QAAC;QAAQ;QAAW;QAAgB;QAAiB;QAAc;QAAsB;KAAsB;IAElH,wBAAwB;IACxB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;gBACnD,cAAc;YAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;gBACjC,cAAc;YAChB;QACF;gDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;YAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;gBACnD,YAAY,EAAE,YAAY,CAAC,KAAK;YAClC;QACF;gDAAG;QAAC;KAAY;IAEhB,wBAAwB;IACxB,MAAM,oBAAoB,CAAC;QACzB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC/C,YAAY,EAAE,MAAM,CAAC,KAAK;YAC1B,qEAAqE;YACrE,EAAE,MAAM,CAAC,KAAK,GAAG;QACnB;IACF;IAEA,WAAW;IACX,MAAM,cAAc,CAAC;QACnB,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC1D,eAAe;IACjB;IAEA,qBAAqB;IACrB,MAAM,YAAY,CAAC,WAAmB;QACpC,MAAM,gBAAgB;eAAI;SAAW;QACrC,MAAM,CAAC,WAAW,GAAG,cAAc,MAAM,CAAC,WAAW;QACrD,cAAc,MAAM,CAAC,SAAS,GAAG;QACjC,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YAErC,CAAC,0BACA,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,6LAAC,+MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC,oIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAAc;;;;;;;;;;;;YAO7C,aAAa,MAAM,GAAG,mBACrB,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,oIAAA,CAAA,mBAAgB;kCACf,cAAA,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;oCAAgB,WAAU;8CAAuB;mCAAxC;;;;;;;;;;;;;;;;;;;;;0BAOpB,6LAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,mDACA,6DACL,CAAC,EAAE,OAAO,MAAM,IAAI,YAAY,mCAAmC,IAAI;gBACxE,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;;kCAER,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAW,CAAC,uBAAuB,EACzC,aAAa,kBAAkB,iBAC/B;;;;;;kCAEF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,aAAa,mBAAmB;;;;;;4BAGlC,WAAW,MAAM,GAAG,2BACnB;;kDACE,6LAAC;wCACC,KAAK,CAAC;4CACJ,IAAI,OAAO;gDACR,OAAe,SAAS,GAAG;4CAC9B;wCACF;wCACA,MAAK;wCACL,QAAQ;wCACR,QAAQ,cAAc,IAAI,CAAC;wCAC3B,WAAU;wCACV,UAAU;wCACV,UAAU,WAAW,MAAM,IAAI;;;;;;kDAEjC,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,UAAU,WAAW,MAAM,IAAI;wCAC/B,SAAS;4CACP,MAAM,QAAQ,AAAC,OAAe,SAAS;4CACvC,IAAI,OAAO;gDACT,MAAM,KAAK;4CACb;wCACF;;0DAEA,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;kCAO9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAoC;oCAChC;oCAAU;oCAAgB,CAAC,UAAU,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAE9E,6LAAC;gCAAE,WAAU;;oCAAwB;oCAClB,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,IAAI,IAAI,CAAC;;;;;;;;;;;;;oBAIrF,WAAW,MAAM,IAAI,2BACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;YAM3C,WAAW,MAAM,GAAG,mBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAkC;oCAC7B,WAAW,MAAM;oCAAC;oCAAE;oCAAU;;;;;;;0CAEjD,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe,EAAE;gCAChC,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC,mIAAA,CAAA,OAAI;gCAAgB,WAAU;0CAC7B,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,MAAM,KAAK,iBACV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,KAAK;;;;;;;;;;;;;;;;6DAIhE;;0DACE,6LAAC;gDACC,KAAK,MAAM,OAAO;gDAClB,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;gDACxB,WAAU;;;;;;4CAIX,MAAM,SAAS,kBACd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,OAAO,cAAc,CAAC,MAAM,EAAE,CAAC,IAAI;4DAAG,WAAU;;;;;;sEAC1D,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,CAAC,MAAM,EAAE,CAAC,IAAI;gEAAE;;;;;;;;;;;;;;;;;;4CAOtC,MAAM,QAAQ,kBACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAW,MAAM,WAAW,EAAE,WAAW,WAAW,kBAAkB;;sEAC3E,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,MAAM,WAAW,EAAE,WAAW,WAAW,iBAAiB;;;;;;;;;;;;4CAMhE,CAAC,YAAY,MAAM,QAAQ,IAAI,MAAM,WAAW,EAAE,WAAW,0BAC5D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;sEACjC,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAO1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDAEZ,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,kBACvC,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,MAAM,EAAE;4DAC1C,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;wDAKpB,WAAW,CAAC,MAAM,EAAE,CAAC,kBACpB,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,SAAQ;4DACR,QAAQ;4DACR,WAAU;sEAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;wDAKvB,WAAW,CAAC,MAAM,EAAE,CAAC,kBACpB,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,SAAQ;4DACR,QAAQ;4DACR,WAAU;sEAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAIxB,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,YAAY,MAAM,EAAE;4DACnC,WAAU;4DACV,UAAU,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;sEAE3E,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;4CAMlB,UAAU,mBACT,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAuC;;;;;;0DAM1D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,mBACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,UAAU,OAAO,QAAQ;4DACxC,WAAU;sEACX;;;;;;wDAIF,QAAQ,OAAO,MAAM,GAAG,mBACvB,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,UAAU,OAAO,QAAQ;4DACxC,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;+BAzIJ,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;AAyJjC;GAnlBgB;KAAA", "debugId": null}}, {"offset": {"line": 4641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useAIModels.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\nimport { AIModel } from '@/types/ai-models'\n\ninterface UseAIModelsReturn {\n  models: AIModel[]\n  activeModels: AIModel[]\n  loading: boolean\n  error: string | null\n  refetch: () => void\n}\n\nexport function useAIModels(): UseAIModelsReturn {\n  const [models, setModels] = useState<AIModel[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchModels = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n      \n      const response = await fetch('/api/ai-models?include_inactive=true')\n      \n      if (!response.ok) {\n        throw new Error('فشل في جلب نماذج الذكاء الاصطناعي')\n      }\n      \n      const data = await response.json()\n      setModels(data.models || [])\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'خطأ غير معروف')\n      console.error('Error fetching AI models:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchModels()\n  }, [])\n\n  // فلترة النماذج النشطة فقط\n  const activeModels = models.filter(model => \n    model.isActive && \n    model.status === 'active' && \n    model.apiKey && \n    model.baseUrl\n  )\n\n  return {\n    models,\n    activeModels,\n    loading,\n    error,\n    refetch: fetchModels\n  }\n}\n\n// Hook مخصص للحصول على نموذج محدد للمهام النصية\nexport function useTextAIModel() {\n  const { activeModels, loading, error } = useAIModels()\n  \n  // البحث عن أفضل نموذج نصي متاح\n  const textModel = activeModels.find(model => \n    model.type === 'text' || \n    model.type === 'multimodal' ||\n    model.provider === 'openai' ||\n    model.provider === 'anthropic'\n  )\n\n  return {\n    model: textModel,\n    available: !!textModel,\n    loading,\n    error\n  }\n}\n\n// Hook مخصص للحصول على نموذج محدد للصور\nexport function useImageAIModel() {\n  const { activeModels, loading, error } = useAIModels()\n  \n  // البحث عن أفضل نموذج للصور متاح\n  const imageModel = activeModels.find(model => \n    model.type === 'image' || \n    model.type === 'multimodal' ||\n    model.provider === 'openai' ||\n    model.provider === 'stability'\n  )\n\n  return {\n    model: imageModel,\n    available: !!imageModel,\n    loading,\n    error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAWO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,KAAK,MAAM,IAAI,EAAE;QAC7B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QACjC,MAAM,QAAQ,IACd,MAAM,MAAM,KAAK,YACjB,MAAM,MAAM,IACZ,MAAM,OAAO;IAGf,OAAO;QACL;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;GA7CgB;AAgDT,SAAS;;IACd,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAEzC,+BAA+B;IAC/B,MAAM,YAAY,aAAa,IAAI,CAAC,CAAA,QAClC,MAAM,IAAI,KAAK,UACf,MAAM,IAAI,KAAK,gBACf,MAAM,QAAQ,KAAK,YACnB,MAAM,QAAQ,KAAK;IAGrB,OAAO;QACL,OAAO;QACP,WAAW,CAAC,CAAC;QACb;QACA;IACF;AACF;IAjBgB;;QAC2B;;;AAmBpC,SAAS;;IACd,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAEzC,iCAAiC;IACjC,MAAM,aAAa,aAAa,IAAI,CAAC,CAAA,QACnC,MAAM,IAAI,KAAK,WACf,MAAM,IAAI,KAAK,gBACf,MAAM,QAAQ,KAAK,YACnB,MAAM,QAAQ,KAAK;IAGrB,OAAO;QACL,OAAO;QACP,WAAW,CAAC,CAAC;QACb;QACA;IACF;AACF;IAjBgB;;QAC2B", "debugId": null}}, {"offset": {"line": 4730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AIProductEnhancer.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useTextAIModel } from '@/hooks/useAIModels'\nimport { \n  Brain, \n  Wand2, \n  FileText, \n  Tag, \n  List, \n  Settings, \n  Search,\n  Loader2,\n  CheckCircle,\n  AlertCircle,\n  Copy,\n  RefreshCw\n} from 'lucide-react'\n\ninterface ProductData {\n  name: string\n  description: string\n  category: string\n  price: number\n  features: string[]\n  specifications: { [key: string]: string }\n}\n\ninterface AIProductEnhancerProps {\n  productData: ProductData\n  onUpdate: (field: string, value: any) => void\n  className?: string\n}\n\ntype AIAction = 'generate_description' | 'generate_title' | 'generate_features' | 'generate_specifications' | 'suggest_category' | 'optimize_seo'\n\ninterface AIResult {\n  description?: string\n  title?: string\n  features?: string[]\n  specifications?: { [key: string]: string }\n  suggestedCategory?: string\n  confidence?: number\n  keywords?: string[]\n  metaDescription?: string\n}\n\nexport function AIProductEnhancer({ productData, onUpdate, className = \"\" }: AIProductEnhancerProps) {\n  const { model, available, loading: modelLoading } = useTextAIModel()\n  const [activeAction, setActiveAction] = useState<AIAction | null>(null)\n  const [results, setResults] = useState<{ [key in AIAction]?: AIResult }>({})\n  const [selectedModel, setSelectedModel] = useState<string>('')\n\n  const handleAIAction = async (action: AIAction) => {\n    if (!available || !model) {\n      alert('لا توجد نماذج ذكاء اصطناعي نشطة')\n      return\n    }\n\n    setActiveAction(action)\n    \n    try {\n      const response = await fetch('/api/ai/product-enhancement', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action,\n          productData,\n          modelId: selectedModel || model.id,\n          language: 'ar'\n        })\n      })\n\n      if (!response.ok) {\n        throw new Error('فشل في تحسين المنتج')\n      }\n\n      const data = await response.json()\n      \n      if (data.success) {\n        setResults(prev => ({\n          ...prev,\n          [action]: data.result\n        }))\n      } else {\n        throw new Error(data.error || 'خطأ غير معروف')\n      }\n    } catch (error) {\n      console.error('AI Enhancement Error:', error)\n      alert(error instanceof Error ? error.message : 'خطأ في تحسين المنتج')\n    } finally {\n      setActiveAction(null)\n    }\n  }\n\n  const applyResult = (action: AIAction, field: string, value: any) => {\n    onUpdate(field, value)\n    // إزالة النتيجة بعد التطبيق\n    setResults(prev => {\n      const newResults = { ...prev }\n      delete newResults[action]\n      return newResults\n    })\n  }\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text)\n    // يمكن إضافة toast notification هنا\n  }\n\n  if (modelLoading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"flex items-center justify-center p-6\">\n          <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n          <span className=\"arabic-text\">جاري تحميل نماذج الذكاء الاصطناعي...</span>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  if (!available) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"flex items-center justify-center p-6 text-center\">\n          <div>\n            <AlertCircle className=\"h-12 w-12 text-orange-500 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2 arabic-text\">لا توجد نماذج ذكاء اصطناعي نشطة</h3>\n            <p className=\"text-gray-600 dark:text-gray-400 arabic-text\">\n              يرجى تفعيل نموذج ذكاء اصطناعي واحد على الأقل لاستخدام هذه الميزة\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2 arabic-text\">\n          <Brain className=\"h-5 w-5 text-blue-600\" />\n          تحسين المنتج بالذكاء الاصطناعي\n        </CardTitle>\n        <CardDescription className=\"arabic-text\">\n          استخدم الذكاء الاصطناعي لتحسين بيانات المنتج وتوليد محتوى احترافي\n        </CardDescription>\n        <div className=\"flex items-center gap-2\">\n          <Badge variant=\"outline\" className=\"text-green-600\">\n            <CheckCircle className=\"h-3 w-3 mr-1\" />\n            {model.name}\n          </Badge>\n          <Badge variant=\"secondary\">\n            {model.provider}\n          </Badge>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-4\">\n        {/* أزرار الإجراءات السريعة */}\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleAIAction('generate_description')}\n            disabled={activeAction === 'generate_description'}\n            className=\"arabic-text\"\n          >\n            {activeAction === 'generate_description' ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <FileText className=\"h-4 w-4 mr-2\" />\n            )}\n            توليد وصف\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleAIAction('generate_title')}\n            disabled={activeAction === 'generate_title'}\n            className=\"arabic-text\"\n          >\n            {activeAction === 'generate_title' ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <Tag className=\"h-4 w-4 mr-2\" />\n            )}\n            توليد عنوان\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleAIAction('generate_features')}\n            disabled={activeAction === 'generate_features'}\n            className=\"arabic-text\"\n          >\n            {activeAction === 'generate_features' ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <List className=\"h-4 w-4 mr-2\" />\n            )}\n            توليد ميزات\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleAIAction('generate_specifications')}\n            disabled={activeAction === 'generate_specifications'}\n            className=\"arabic-text\"\n          >\n            {activeAction === 'generate_specifications' ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <Settings className=\"h-4 w-4 mr-2\" />\n            )}\n            توليد مواصفات\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleAIAction('suggest_category')}\n            disabled={activeAction === 'suggest_category'}\n            className=\"arabic-text\"\n          >\n            {activeAction === 'suggest_category' ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <Wand2 className=\"h-4 w-4 mr-2\" />\n            )}\n            اقتراح فئة\n          </Button>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handleAIAction('optimize_seo')}\n            disabled={activeAction === 'optimize_seo'}\n            className=\"arabic-text\"\n          >\n            {activeAction === 'optimize_seo' ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <Search className=\"h-4 w-4 mr-2\" />\n            )}\n            تحسين SEO\n          </Button>\n        </div>\n\n        {/* عرض النتائج */}\n        {Object.entries(results).map(([action, result]) => (\n          <Card key={action} className=\"border-green-200 bg-green-50 dark:bg-green-900/20\">\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-sm text-green-700 dark:text-green-300 arabic-text\">\n                نتيجة {getActionName(action as AIAction)}\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              {result.description && (\n                <div>\n                  <Textarea\n                    value={result.description}\n                    readOnly\n                    className=\"min-h-[100px] arabic-text\"\n                  />\n                  <div className=\"flex gap-2 mt-2\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => applyResult(action as AIAction, 'description', result.description)}\n                    >\n                      تطبيق\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => copyToClipboard(result.description!)}\n                    >\n                      <Copy className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              )}\n\n              {result.title && (\n                <div>\n                  <input\n                    type=\"text\"\n                    value={result.title}\n                    readOnly\n                    className=\"w-full p-2 border rounded arabic-text\"\n                  />\n                  <div className=\"flex gap-2 mt-2\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => applyResult(action as AIAction, 'name', result.title)}\n                    >\n                      تطبيق\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => copyToClipboard(result.title!)}\n                    >\n                      <Copy className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              )}\n\n              {result.features && (\n                <div>\n                  <div className=\"space-y-1\">\n                    {result.features.map((feature, index) => (\n                      <div key={index} className=\"p-2 bg-white dark:bg-gray-800 rounded border arabic-text\">\n                        {feature}\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"flex gap-2 mt-2\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => applyResult(action as AIAction, 'features', result.features)}\n                    >\n                      تطبيق الكل\n                    </Button>\n                  </div>\n                </div>\n              )}\n\n              {result.specifications && (\n                <div>\n                  <div className=\"space-y-1\">\n                    {Object.entries(result.specifications).map(([key, value]) => (\n                      <div key={key} className=\"flex gap-2 p-2 bg-white dark:bg-gray-800 rounded border\">\n                        <span className=\"font-medium arabic-text\">{key}:</span>\n                        <span className=\"arabic-text\">{value}</span>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"flex gap-2 mt-2\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => applyResult(action as AIAction, 'specifications', result.specifications)}\n                    >\n                      تطبيق الكل\n                    </Button>\n                  </div>\n                </div>\n              )}\n\n              {result.suggestedCategory && (\n                <div>\n                  <div className=\"p-2 bg-white dark:bg-gray-800 rounded border arabic-text\">\n                    الفئة المقترحة: <strong>{getCategoryName(result.suggestedCategory)}</strong>\n                    {result.confidence && (\n                      <Badge variant=\"secondary\" className=\"mr-2\">\n                        {Math.round(result.confidence * 100)}% ثقة\n                      </Badge>\n                    )}\n                  </div>\n                  <div className=\"flex gap-2 mt-2\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => applyResult(action as AIAction, 'category', result.suggestedCategory)}\n                    >\n                      تطبيق\n                    </Button>\n                  </div>\n                </div>\n              )}\n\n              {result.keywords && (\n                <div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {result.keywords.map((keyword, index) => (\n                      <Badge key={index} variant=\"outline\" className=\"arabic-text\">\n                        {keyword}\n                      </Badge>\n                    ))}\n                  </div>\n                  {result.metaDescription && (\n                    <Textarea\n                      value={result.metaDescription}\n                      readOnly\n                      className=\"mt-2 arabic-text\"\n                      placeholder=\"وصف SEO\"\n                    />\n                  )}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </CardContent>\n    </Card>\n  )\n}\n\nfunction getActionName(action: AIAction): string {\n  const actionNames: { [key in AIAction]: string } = {\n    'generate_description': 'توليد الوصف',\n    'generate_title': 'توليد العنوان',\n    'generate_features': 'توليد الميزات',\n    'generate_specifications': 'توليد المواصفات',\n    'suggest_category': 'اقتراح الفئة',\n    'optimize_seo': 'تحسين SEO'\n  }\n  \n  return actionNames[action]\n}\n\nfunction getCategoryName(category: string): string {\n  const categoryNames: { [key: string]: string } = {\n    'gown': 'ثوب التخرج',\n    'cap': 'قبعة التخرج',\n    'stole': 'وشاح التخرج',\n    'tassel': 'شرابة التخرج',\n    'hood': 'قلنسوة التخرج'\n  }\n  \n  return categoryNames[category] || category\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAoDO,SAAS,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,EAA0B;;IACjG,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,aAAa,CAAC,OAAO;YACxB,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,SAAS,iBAAiB,MAAM,EAAE;oBAClC,UAAU;gBACZ;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,CAAA,OAAQ,CAAC;wBAClB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE,KAAK,MAAM;oBACvB,CAAC;YACH,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACjD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC,QAAkB,OAAe;QACpD,SAAS,OAAO;QAChB,4BAA4B;QAC5B,WAAW,CAAA;YACT,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,OAAO;YACzB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,oCAAoC;IACtC;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAc;;;;;;;;;;;;;;;;;IAItC;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;;sCACC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;;;;;;;;;;;IAOtE;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;kCAG7C,6LAAC,mIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAc;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,MAAM,IAAI;;;;;;;0CAEb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CACZ,MAAM,QAAQ;;;;;;;;;;;;;;;;;;0BAKrB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,UAAU,iBAAiB;gCAC3B,WAAU;;oCAET,iBAAiB,uCAChB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACpB;;;;;;;0CAIJ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,UAAU,iBAAiB;gCAC3B,WAAU;;oCAET,iBAAiB,iCAChB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCACf;;;;;;;0CAIJ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,UAAU,iBAAiB;gCAC3B,WAAU;;oCAET,iBAAiB,oCAChB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAChB;;;;;;;0CAIJ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,UAAU,iBAAiB;gCAC3B,WAAU;;oCAET,iBAAiB,0CAChB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACpB;;;;;;;0CAIJ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,UAAU,iBAAiB;gCAC3B,WAAU;;oCAET,iBAAiB,mCAChB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,kNAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCACjB;;;;;;;0CAIJ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,UAAU,iBAAiB;gCAC3B,WAAU;;oCAET,iBAAiB,+BAChB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAClB;;;;;;;;;;;;;oBAML,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,OAAO,iBAC5C,6LAAC,mIAAA,CAAA,OAAI;4BAAc,WAAU;;8CAC3B,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;4CAAyD;4CACrE,cAAc;;;;;;;;;;;;8CAGzB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;wCACpB,OAAO,WAAW,kBACjB,6LAAC;;8DACC,6LAAC,uIAAA,CAAA,WAAQ;oDACP,OAAO,OAAO,WAAW;oDACzB,QAAQ;oDACR,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,IAAM,YAAY,QAAoB,eAAe,OAAO,WAAW;sEACjF;;;;;;sEAGD,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,OAAO,WAAW;sEAEjD,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAMvB,OAAO,KAAK,kBACX,6LAAC;;8DACC,6LAAC;oDACC,MAAK;oDACL,OAAO,OAAO,KAAK;oDACnB,QAAQ;oDACR,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,IAAM,YAAY,QAAoB,QAAQ,OAAO,KAAK;sEACpE;;;;;;sEAGD,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,OAAO,KAAK;sEAE3C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAMvB,OAAO,QAAQ,kBACd,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC;4DAAgB,WAAU;sEACxB;2DADO;;;;;;;;;;8DAKd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,YAAY,QAAoB,YAAY,OAAO,QAAQ;kEAC3E;;;;;;;;;;;;;;;;;wCAON,OAAO,cAAc,kBACpB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,CAAC,OAAO,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACtD,6LAAC;4DAAc,WAAU;;8EACvB,6LAAC;oEAAK,WAAU;;wEAA2B;wEAAI;;;;;;;8EAC/C,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;2DAFvB;;;;;;;;;;8DAMd,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,YAAY,QAAoB,kBAAkB,OAAO,cAAc;kEACvF;;;;;;;;;;;;;;;;;wCAON,OAAO,iBAAiB,kBACvB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;wDAA2D;sEACxD,6LAAC;sEAAQ,gBAAgB,OAAO,iBAAiB;;;;;;wDAChE,OAAO,UAAU,kBAChB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAClC,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;gEAAK;;;;;;;;;;;;;8DAI3C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,YAAY,QAAoB,YAAY,OAAO,iBAAiB;kEACpF;;;;;;;;;;;;;;;;;wCAON,OAAO,QAAQ,kBACd,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC,oIAAA,CAAA,QAAK;4DAAa,SAAQ;4DAAU,WAAU;sEAC5C;2DADS;;;;;;;;;;gDAKf,OAAO,eAAe,kBACrB,6LAAC,uIAAA,CAAA,WAAQ;oDACP,OAAO,OAAO,eAAe;oDAC7B,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;2BAtIb;;;;;;;;;;;;;;;;;AAiJrB;GAlWgB;;QACsC,8HAAA,CAAA,iBAAc;;;KADpD;AAoWhB,SAAS,cAAc,MAAgB;IACrC,MAAM,cAA6C;QACjD,wBAAwB;QACxB,kBAAkB;QAClB,qBAAqB;QACrB,2BAA2B;QAC3B,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,OAAO,WAAW,CAAC,OAAO;AAC5B;AAEA,SAAS,gBAAgB,QAAgB;IACvC,MAAM,gBAA2C;QAC/C,QAAQ;QACR,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;IACV;IAEA,OAAO,aAAa,CAAC,SAAS,IAAI;AACpC", "debugId": null}}, {"offset": {"line": 5520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/ProductForm.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Switch } from '@/components/ui/switch'\nimport {\n  Upload,\n  X,\n  Plus,\n  Image as ImageIcon,\n  Save,\n  Eye,\n  AlertCircle,\n  Package,\n  Palette,\n  Ruler,\n  DollarSign,\n  Star,\n  Brain\n} from 'lucide-react'\nimport { ImageUploader } from './ImageUploader'\nimport { AIProductEnhancer } from './AIProductEnhancer'\n\n// أنواع البيانات\ninterface Category {\n  id: string\n  name_ar: string\n  name_en?: string\n  slug: string\n  icon?: string\n  is_active: boolean\n}\n\ninterface ProductFormData {\n  name: string\n  description: string\n  category: string\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: { file: File; preview: string; id: string }[]\n  stock_quantity: number\n  is_available: boolean\n  is_published: boolean\n  features: string[]\n  specifications: { [key: string]: string }\n}\n\ninterface ProductFormProps {\n  onSubmit: (data: ProductFormData) => void\n  onCancel: () => void\n  initialData?: Partial<ProductFormData>\n  isEditing?: boolean\n}\n\nconst predefinedColors = [\n  'أسود', 'أزرق داكن', 'بورجوندي', 'ذهبي', 'فضي', 'أبيض', \n  'أحمر', 'أخضر', 'بنفسجي', 'وردي', 'برتقالي', 'بني'\n]\n\nconst predefinedSizes = [\n  'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'واحد'\n]\n\nexport function ProductForm({\n  onSubmit,\n  onCancel,\n  initialData = {},\n  isEditing = false\n}: ProductFormProps) {\n  const [formData, setFormData] = useState<ProductFormData>({\n    name: initialData.name || '',\n    description: initialData.description || '',\n    category: initialData.category || '',\n    price: initialData.price || 0,\n    rental_price: initialData.rental_price || 0,\n    colors: initialData.colors || [],\n    sizes: initialData.sizes || [],\n    images: initialData.images || [],\n    stock_quantity: initialData.stock_quantity || 0,\n    is_available: initialData.is_available ?? true,\n    is_published: initialData.is_published ?? true,\n    features: initialData.features || [],\n    specifications: initialData.specifications || {}\n  })\n\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loadingCategories, setLoadingCategories] = useState(true)\n  const [newColor, setNewColor] = useState('')\n  const [newSize, setNewSize] = useState('')\n  const [newFeature, setNewFeature] = useState('')\n  const [newSpecKey, setNewSpecKey] = useState('')\n  const [newSpecValue, setNewSpecValue] = useState('')\n  const [errors, setErrors] = useState<{ [key: string]: string }>({})\n\n  // جلب الفئات\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await fetch('/api/categories')\n        if (response.ok) {\n          const data = await response.json()\n          setCategories(data.categories)\n        }\n      } catch (error) {\n        console.error('Error fetching categories:', error)\n      } finally {\n        setLoadingCategories(false)\n      }\n    }\n\n    fetchCategories()\n  }, [])\n\n  // التحقق من صحة البيانات\n  const validateForm = (): boolean => {\n    const newErrors: { [key: string]: string } = {}\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم المنتج مطلوب'\n    }\n\n    if (!formData.description.trim()) {\n      newErrors.description = 'وصف المنتج مطلوب'\n    }\n\n    if (!formData.category) {\n      newErrors.category = 'فئة المنتج مطلوبة'\n    }\n\n    if (formData.price <= 0) {\n      newErrors.price = 'السعر يجب أن يكون أكبر من صفر'\n    }\n\n    if (formData.colors.length === 0) {\n      newErrors.colors = 'يجب إضافة لون واحد على الأقل'\n    }\n\n    if (formData.sizes.length === 0) {\n      newErrors.sizes = 'يجب إضافة مقاس واحد على الأقل'\n    }\n\n    if (formData.stock_quantity < 0) {\n      newErrors.stock_quantity = 'كمية المخزون لا يمكن أن تكون سالبة'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  // إضافة لون جديد\n  const addColor = () => {\n    if (newColor.trim() && !formData.colors.includes(newColor.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        colors: [...prev.colors, newColor.trim()]\n      }))\n      setNewColor('')\n    }\n  }\n\n  // حذف لون\n  const removeColor = (color: string) => {\n    setFormData(prev => ({\n      ...prev,\n      colors: prev.colors.filter(c => c !== color)\n    }))\n  }\n\n  // إضافة مقاس جديد\n  const addSize = () => {\n    if (newSize.trim() && !formData.sizes.includes(newSize.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        sizes: [...prev.sizes, newSize.trim()]\n      }))\n      setNewSize('')\n    }\n  }\n\n  // حذف مقاس\n  const removeSize = (size: string) => {\n    setFormData(prev => ({\n      ...prev,\n      sizes: prev.sizes.filter(s => s !== size)\n    }))\n  }\n\n  // إضافة ميزة جديدة\n  const addFeature = () => {\n    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        features: [...prev.features, newFeature.trim()]\n      }))\n      setNewFeature('')\n    }\n  }\n\n  // حذف ميزة\n  const removeFeature = (feature: string) => {\n    setFormData(prev => ({\n      ...prev,\n      features: prev.features.filter(f => f !== feature)\n    }))\n  }\n\n  // إضافة مواصفة جديدة\n  const addSpecification = () => {\n    if (newSpecKey.trim() && newSpecValue.trim()) {\n      setFormData(prev => ({\n        ...prev,\n        specifications: {\n          ...prev.specifications,\n          [newSpecKey.trim()]: newSpecValue.trim()\n        }\n      }))\n      setNewSpecKey('')\n      setNewSpecValue('')\n    }\n  }\n\n  // حذف مواصفة\n  const removeSpecification = (key: string) => {\n    setFormData(prev => {\n      const newSpecs = { ...prev.specifications }\n      delete newSpecs[key]\n      return {\n        ...prev,\n        specifications: newSpecs\n      }\n    })\n  }\n\n  // تحديث البيانات من الذكاء الاصطناعي\n  const handleAIUpdate = (field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  // إرسال النموذج\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (validateForm()) {\n      onSubmit(formData)\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <Tabs defaultValue=\"basic\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-5\">\n          <TabsTrigger value=\"basic\" className=\"arabic-text\">\n            <Package className=\"h-4 w-4 mr-2\" />\n            المعلومات الأساسية\n          </TabsTrigger>\n          <TabsTrigger value=\"details\" className=\"arabic-text\">\n            <Palette className=\"h-4 w-4 mr-2\" />\n            التفاصيل والألوان\n          </TabsTrigger>\n          <TabsTrigger value=\"images\" className=\"arabic-text\">\n            <ImageIcon className=\"h-4 w-4 mr-2\" />\n            الصور\n          </TabsTrigger>\n          <TabsTrigger value=\"features\" className=\"arabic-text\">\n            <Star className=\"h-4 w-4 mr-2\" />\n            المميزات والمواصفات\n          </TabsTrigger>\n          <TabsTrigger value=\"ai\" className=\"arabic-text\">\n            <Brain className=\"h-4 w-4 mr-2\" />\n            الذكاء الاصطناعي\n          </TabsTrigger>\n        </TabsList>\n\n        {/* المعلومات الأساسية */}\n        <TabsContent value=\"basic\" className=\"space-y-6 mt-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* اسم المنتج */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\" className=\"arabic-text\">اسم المنتج *</Label>\n              <Input\n                id=\"name\"\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                placeholder=\"أدخل اسم المنتج\"\n                className=\"arabic-text\"\n              />\n              {errors.name && (\n                <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.name}\n                </p>\n              )}\n            </div>\n\n            {/* فئة المنتج */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\" className=\"arabic-text\">فئة المنتج *</Label>\n              <Select\n                value={formData.category}\n                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}\n                disabled={loadingCategories}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder={loadingCategories ? \"جاري تحميل الفئات...\" : \"اختر فئة المنتج\"} />\n                </SelectTrigger>\n                <SelectContent>\n                  {categories.map(category => (\n                    <SelectItem key={category.slug} value={category.slug}>\n                      <div className=\"flex items-center gap-2\">\n                        {category.icon && <span>{category.icon}</span>}\n                        <span className=\"arabic-text\">{category.name_ar}</span>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.category && (\n                <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.category}\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* وصف المنتج */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"description\" className=\"arabic-text\">وصف المنتج *</Label>\n            <Textarea\n              id=\"description\"\n              value={formData.description}\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              placeholder=\"أدخل وصف مفصل للمنتج\"\n              className=\"arabic-text min-h-[100px]\"\n            />\n            {errors.description && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.description}\n              </p>\n            )}\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {/* السعر */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"price\" className=\"arabic-text\">السعر (درهم) *</Label>\n              <Input\n                id=\"price\"\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                value={formData.price}\n                onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}\n                placeholder=\"0.00\"\n              />\n              {errors.price && (\n                <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.price}\n                </p>\n              )}\n            </div>\n\n            {/* سعر الإيجار */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"rental_price\" className=\"arabic-text\">سعر الإيجار (درهم)</Label>\n              <Input\n                id=\"rental_price\"\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                value={formData.rental_price}\n                onChange={(e) => setFormData(prev => ({ ...prev, rental_price: parseFloat(e.target.value) || 0 }))}\n                placeholder=\"0.00\"\n              />\n            </div>\n\n            {/* كمية المخزون */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"stock_quantity\" className=\"arabic-text\">كمية المخزون *</Label>\n              <Input\n                id=\"stock_quantity\"\n                type=\"number\"\n                min=\"0\"\n                value={formData.stock_quantity}\n                onChange={(e) => setFormData(prev => ({ ...prev, stock_quantity: parseInt(e.target.value) || 0 }))}\n                placeholder=\"0\"\n              />\n              {errors.stock_quantity && (\n                <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.stock_quantity}\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* متاح للبيع */}\n          <div className=\"flex items-center space-x-2\">\n            <Switch\n              id=\"is_available\"\n              checked={formData.is_available}\n              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_available: checked }))}\n            />\n            <Label htmlFor=\"is_available\" className=\"arabic-text\">\n              متاح للبيع\n            </Label>\n          </div>\n\n          {/* نشر المنتج */}\n          <div className=\"flex items-center space-x-2\">\n            <Switch\n              id=\"is_published\"\n              checked={formData.is_published}\n              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}\n            />\n            <Label htmlFor=\"is_published\" className=\"arabic-text\">\n              نشر المنتج في الكتالوج\n            </Label>\n          </div>\n        </TabsContent>\n\n        {/* التفاصيل والألوان */}\n        <TabsContent value=\"details\" className=\"space-y-6 mt-6\">\n          {/* الألوان */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text\">الألوان المتاحة</CardTitle>\n              <CardDescription className=\"arabic-text\">\n                أضف الألوان المتاحة للمنتج\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* إضافة لون جديد */}\n              <div className=\"flex gap-2\">\n                <Select value={newColor} onValueChange={setNewColor}>\n                  <SelectTrigger className=\"flex-1\">\n                    <SelectValue placeholder=\"اختر لون\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {predefinedColors.map(color => (\n                      <SelectItem key={color} value={color}>{color}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <Input\n                  value={newColor}\n                  onChange={(e) => setNewColor(e.target.value)}\n                  placeholder=\"أو أدخل لون مخصص\"\n                  className=\"flex-1 arabic-text\"\n                />\n                <Button type=\"button\" onClick={addColor} size=\"sm\">\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              {/* عرض الألوان المضافة */}\n              <div className=\"flex flex-wrap gap-2\">\n                {formData.colors.map((color, index) => (\n                  <Badge key={index} variant=\"secondary\" className=\"arabic-text\">\n                    {color}\n                    <button\n                      type=\"button\"\n                      onClick={() => removeColor(color)}\n                      className=\"ml-2 hover:text-red-600\"\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </button>\n                  </Badge>\n                ))}\n              </div>\n\n              {errors.colors && (\n                <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.colors}\n                </p>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* المقاسات */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text\">المقاسات المتاحة</CardTitle>\n              <CardDescription className=\"arabic-text\">\n                أضف المقاسات المتاحة للمنتج\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* إضافة مقاس جديد */}\n              <div className=\"flex gap-2\">\n                <Select value={newSize} onValueChange={setNewSize}>\n                  <SelectTrigger className=\"flex-1\">\n                    <SelectValue placeholder=\"اختر مقاس\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {predefinedSizes.map(size => (\n                      <SelectItem key={size} value={size}>{size}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <Input\n                  value={newSize}\n                  onChange={(e) => setNewSize(e.target.value)}\n                  placeholder=\"أو أدخل مقاس مخصص\"\n                  className=\"flex-1 arabic-text\"\n                />\n                <Button type=\"button\" onClick={addSize} size=\"sm\">\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              {/* عرض المقاسات المضافة */}\n              <div className=\"flex flex-wrap gap-2\">\n                {formData.sizes.map((size, index) => (\n                  <Badge key={index} variant=\"secondary\" className=\"arabic-text\">\n                    {size}\n                    <button\n                      type=\"button\"\n                      onClick={() => removeSize(size)}\n                      className=\"ml-2 hover:text-red-600\"\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </button>\n                  </Badge>\n                ))}\n              </div>\n\n              {errors.sizes && (\n                <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.sizes}\n                </p>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* الصور */}\n        <TabsContent value=\"images\" className=\"space-y-6 mt-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text\">صور المنتج</CardTitle>\n              <CardDescription className=\"arabic-text\">\n                أضف صور عالية الجودة للمنتج (يُفضل 500x600 بكسل أو أكبر)\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ImageUploader\n                images={formData.images}\n                onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}\n                maxImages={8}\n                maxSize={5 * 1024 * 1024} // 5MB\n                acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* المميزات والمواصفات */}\n        <TabsContent value=\"features\" className=\"space-y-6 mt-6\">\n          {/* المميزات */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text\">مميزات المنتج</CardTitle>\n              <CardDescription className=\"arabic-text\">\n                أضف المميزات الرئيسية للمنتج\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* إضافة ميزة جديدة */}\n              <div className=\"flex gap-2\">\n                <Input\n                  value={newFeature}\n                  onChange={(e) => setNewFeature(e.target.value)}\n                  placeholder=\"أدخل ميزة جديدة\"\n                  className=\"flex-1 arabic-text\"\n                />\n                <Button type=\"button\" onClick={addFeature} size=\"sm\">\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              {/* عرض المميزات */}\n              <div className=\"space-y-2\">\n                {formData.features.map((feature, index) => (\n                  <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                    <span className=\"arabic-text\">{feature}</span>\n                    <button\n                      type=\"button\"\n                      onClick={() => removeFeature(feature)}\n                      className=\"text-red-600 hover:text-red-700\"\n                    >\n                      <X className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              {formData.features.length === 0 && (\n                <p className=\"text-gray-500 text-center py-4 arabic-text\">\n                  لم يتم إضافة أي مميزات بعد\n                </p>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* المواصفات */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"arabic-text\">مواصفات المنتج</CardTitle>\n              <CardDescription className=\"arabic-text\">\n                أضف المواصفات التقنية للمنتج\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* إضافة مواصفة جديدة */}\n              <div className=\"grid grid-cols-2 gap-2\">\n                <Input\n                  value={newSpecKey}\n                  onChange={(e) => setNewSpecKey(e.target.value)}\n                  placeholder=\"اسم المواصفة (مثل: المادة)\"\n                  className=\"arabic-text\"\n                />\n                <div className=\"flex gap-2\">\n                  <Input\n                    value={newSpecValue}\n                    onChange={(e) => setNewSpecValue(e.target.value)}\n                    placeholder=\"قيمة المواصفة\"\n                    className=\"flex-1 arabic-text\"\n                  />\n                  <Button type=\"button\" onClick={addSpecification} size=\"sm\">\n                    <Plus className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n\n              {/* عرض المواصفات */}\n              <div className=\"space-y-2\">\n                {Object.entries(formData.specifications).map(([key, value]) => (\n                  <div key={key} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                    <div className=\"arabic-text\">\n                      <span className=\"font-medium\">{key}:</span> {value}\n                    </div>\n                    <button\n                      type=\"button\"\n                      onClick={() => removeSpecification(key)}\n                      className=\"text-red-600 hover:text-red-700\"\n                    >\n                      <X className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              {Object.keys(formData.specifications).length === 0 && (\n                <p className=\"text-gray-500 text-center py-4 arabic-text\">\n                  لم يتم إضافة أي مواصفات بعد\n                </p>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* الذكاء الاصطناعي */}\n        <TabsContent value=\"ai\" className=\"space-y-6 mt-6\">\n          <AIProductEnhancer\n            productData={{\n              name: formData.name,\n              description: formData.description,\n              category: formData.category,\n              price: formData.price,\n              features: formData.features,\n              specifications: formData.specifications\n            }}\n            onUpdate={handleAIUpdate}\n          />\n        </TabsContent>\n      </Tabs>\n\n      {/* أزرار الإجراءات */}\n      <div className=\"flex justify-end gap-4 pt-6 border-t\">\n        <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n          إلغاء\n        </Button>\n        <Button type=\"button\" variant=\"outline\">\n          <Eye className=\"h-4 w-4 mr-2\" />\n          معاينة\n        </Button>\n        <Button type=\"submit\">\n          <Save className=\"h-4 w-4 mr-2\" />\n          {isEditing ? 'تحديث المنتج' : 'إضافة المنتج'}\n        </Button>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;;;AA5BA;;;;;;;;;;;;;;AA+DA,MAAM,mBAAmB;IACvB;IAAQ;IAAa;IAAY;IAAQ;IAAO;IAChD;IAAQ;IAAQ;IAAU;IAAQ;IAAW;CAC9C;AAED,MAAM,kBAAkB;IACtB;IAAM;IAAK;IAAK;IAAK;IAAM;IAAO;IAAQ;CAC3C;AAEM,SAAS,YAAY,EAC1B,QAAQ,EACR,QAAQ,EACR,cAAc,CAAC,CAAC,EAChB,YAAY,KAAK,EACA;;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,MAAM,YAAY,IAAI,IAAI;QAC1B,aAAa,YAAY,WAAW,IAAI;QACxC,UAAU,YAAY,QAAQ,IAAI;QAClC,OAAO,YAAY,KAAK,IAAI;QAC5B,cAAc,YAAY,YAAY,IAAI;QAC1C,QAAQ,YAAY,MAAM,IAAI,EAAE;QAChC,OAAO,YAAY,KAAK,IAAI,EAAE;QAC9B,QAAQ,YAAY,MAAM,IAAI,EAAE;QAChC,gBAAgB,YAAY,cAAc,IAAI;QAC9C,cAAc,YAAY,YAAY,IAAI;QAC1C,cAAc,YAAY,YAAY,IAAI;QAC1C,UAAU,YAAY,QAAQ,IAAI,EAAE;QACpC,gBAAgB,YAAY,cAAc,IAAI,CAAC;IACjD;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAEjE,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;yDAAkB;oBACtB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,cAAc,KAAK,UAAU;wBAC/B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C,SAAU;wBACR,qBAAqB;oBACvB;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,eAAe;QACnB,MAAM,YAAuC,CAAC;QAE9C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,KAAK,IAAI,GAAG;YACvB,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;YAChC,UAAU,MAAM,GAAG;QACrB;QAEA,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAAG;YAC/B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,cAAc,GAAG,GAAG;YAC/B,UAAU,cAAc,GAAG;QAC7B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,iBAAiB;IACjB,MAAM,WAAW;QACf,IAAI,SAAS,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK;YACjE,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;wBAAE,SAAS,IAAI;qBAAG;gBAC3C,CAAC;YACD,YAAY;QACd;IACF;IAEA,UAAU;IACV,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YACxC,CAAC;IACH;IAEA,kBAAkB;IAClB,MAAM,UAAU;QACd,IAAI,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,CAAC,QAAQ,CAAC,QAAQ,IAAI,KAAK;YAC9D,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,OAAO;2BAAI,KAAK,KAAK;wBAAE,QAAQ,IAAI;qBAAG;gBACxC,CAAC;YACD,WAAW;QACb;IACF;IAEA,WAAW;IACX,MAAM,aAAa,CAAC;QAClB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YACtC,CAAC;IACH;IAEA,mBAAmB;IACnB,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK;YACvE,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;2BAAI,KAAK,QAAQ;wBAAE,WAAW,IAAI;qBAAG;gBACjD,CAAC;YACD,cAAc;QAChB;IACF;IAEA,WAAW;IACX,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YAC5C,CAAC;IACH;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI,WAAW,IAAI,MAAM,aAAa,IAAI,IAAI;YAC5C,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,gBAAgB;wBACd,GAAG,KAAK,cAAc;wBACtB,CAAC,WAAW,IAAI,GAAG,EAAE,aAAa,IAAI;oBACxC;gBACF,CAAC;YACD,cAAc;YACd,gBAAgB;QAClB;IACF;IAEA,aAAa;IACb,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA;YACV,MAAM,WAAW;gBAAE,GAAG,KAAK,cAAc;YAAC;YAC1C,OAAO,QAAQ,CAAC,IAAI;YACpB,OAAO;gBACL,GAAG,IAAI;gBACP,gBAAgB;YAClB;QACF;IACF;IAEA,qCAAqC;IACrC,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,gBAAgB;YAClB,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAQ,WAAU;;kCACnC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;;kDACnC,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGtC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;;kDACrC,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGtC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;;kDACpC,6LAAC,uMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAK,WAAU;;kDAChC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMtC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;;0CACnC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,WAAU;0DAAc;;;;;;0DAC9C,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACvE,aAAY;gDACZ,WAAU;;;;;;4CAEX,OAAO,IAAI,kBACV,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,IAAI;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAW,WAAU;0DAAc;;;;;;0DAClD,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,QAAQ;gDACxB,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU;wDAAM,CAAC;gDAC3E,UAAU;;kEAEV,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAa,oBAAoB,yBAAyB;;;;;;;;;;;kEAEzE,6LAAC,qIAAA,CAAA,gBAAa;kEACX,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,aAAU;gEAAqB,OAAO,SAAS,IAAI;0EAClD,cAAA,6LAAC;oEAAI,WAAU;;wEACZ,SAAS,IAAI,kBAAI,6LAAC;sFAAM,SAAS,IAAI;;;;;;sFACtC,6LAAC;4EAAK,WAAU;sFAAe,SAAS,OAAO;;;;;;;;;;;;+DAHlC,SAAS,IAAI;;;;;;;;;;;;;;;;4CASnC,OAAO,QAAQ,kBACd,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;0CAOxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAc;;;;;;kDACrD,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,aAAY;wCACZ,WAAU;;;;;;oCAEX,OAAO,WAAW,kBACjB,6LAAC;wCAAE,WAAU;;0DACX,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,WAAW;;;;;;;;;;;;;0CAKzB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,WAAU;0DAAc;;;;;;0DAC/C,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDAAE,CAAC;gDACzF,aAAY;;;;;;4CAEb,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,KAAK;;;;;;;;;;;;;kDAMnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;0DAAc;;;;;;0DACtD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,cAAc,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDAAE,CAAC;gDAChG,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAiB,WAAU;0DAAc;;;;;;0DACxD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,cAAc;gDAC9B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDAAE,CAAC;gDAChG,aAAY;;;;;;4CAEb,OAAO,cAAc,kBACpB,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,cAAc;;;;;;;;;;;;;;;;;;;0CAO9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,SAAS,YAAY;wCAC9B,iBAAiB,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc;gDAAQ,CAAC;;;;;;kDAEvF,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;kDAAc;;;;;;;;;;;;0CAMxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,SAAS,YAAY;wCAC9B,iBAAiB,CAAC,UAAY,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc;gDAAQ,CAAC;;;;;;kDAEvF,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;kDAAc;;;;;;;;;;;;;;;;;;kCAO1D,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;;0CAErC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc;;;;;;0DACnC,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAI3C,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAU,eAAe;;0EACtC,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;0EACX,iBAAiB,GAAG,CAAC,CAAA,sBACpB,6LAAC,qIAAA,CAAA,aAAU;wEAAa,OAAO;kFAAQ;uEAAtB;;;;;;;;;;;;;;;;kEAIvB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAS;wDAAU,MAAK;kEAC5C,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKpB,6LAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC,oIAAA,CAAA,QAAK;wDAAa,SAAQ;wDAAY,WAAU;;4DAC9C;0EACD,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY;gEAC3B,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDAPL;;;;;;;;;;4CAaf,OAAO,MAAM,kBACZ,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,MAAM;;;;;;;;;;;;;;;;;;;0CAOtB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc;;;;;;0DACnC,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAI3C,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAS,eAAe;;0EACrC,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;0EACX,gBAAgB,GAAG,CAAC,CAAA,qBACnB,6LAAC,qIAAA,CAAA,aAAU;wEAAY,OAAO;kFAAO;uEAApB;;;;;;;;;;;;;;;;kEAIvB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC1C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAS;wDAAS,MAAK;kEAC3C,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKpB,6LAAC;gDAAI,WAAU;0DACZ,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC,oIAAA,CAAA,QAAK;wDAAa,SAAQ;wDAAY,WAAU;;4DAC9C;0EACD,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,WAAW;gEAC1B,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDAPL;;;;;;;;;;4CAaf,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,+IAAA,CAAA,gBAAa;wCACZ,QAAQ,SAAS,MAAM;wCACvB,gBAAgB,CAAC,SAAW,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE;gDAAO,CAAC;wCACpE,WAAW;wCACX,SAAS,IAAI,OAAO;wCACpB,eAAe;4CAAC;4CAAc;4CAAa;yCAAa;;;;;;;;;;;;;;;;;;;;;;kCAOhE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CAEtC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc;;;;;;0DACnC,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAI3C,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAS;wDAAY,MAAK;kEAC9C,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKpB,6LAAC;gDAAI,WAAU;0DACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAK,WAAU;0EAAe;;;;;;0EAC/B,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,cAAc;gEAC7B,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDAPP;;;;;;;;;;4CAab,SAAS,QAAQ,CAAC,MAAM,KAAK,mBAC5B,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;0CAQhE,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc;;;;;;0DACnC,6LAAC,mIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAc;;;;;;;;;;;;kDAI3C,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC/C,aAAY;gEACZ,WAAU;;;;;;0EAEZ,6LAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAS,SAAS;gEAAkB,MAAK;0EACpD,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAMtB,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,SAAS,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACxD,6LAAC;wDAAc,WAAU;;0EACvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EAAe;4EAAI;;;;;;;oEAAQ;oEAAE;;;;;;;0EAE/C,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,oBAAoB;gEACnC,WAAU;0EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDATP;;;;;;;;;;4CAeb,OAAO,IAAI,CAAC,SAAS,cAAc,EAAE,MAAM,KAAK,mBAC/C,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;;kCASlE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAK,WAAU;kCAChC,cAAA,6LAAC,mJAAA,CAAA,oBAAiB;4BAChB,aAAa;gCACX,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,UAAU,SAAS,QAAQ;gCAC3B,OAAO,SAAS,KAAK;gCACrB,UAAU,SAAS,QAAQ;gCAC3B,gBAAgB,SAAS,cAAc;4BACzC;4BACA,UAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAQ;wBAAU,SAAS;kCAAU;;;;;;kCAG3D,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAQ;;0CAC5B,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGlC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;;0CACX,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;AAKxC;GA9nBgB;KAAA", "debugId": null}}, {"offset": {"line": 7085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/EditProductDialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alog<PERSON>ontent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { ProductForm } from './ProductForm'\n\ninterface Product {\n  id: string\n  name: string\n  description: string\n  category: string\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\ninterface EditProductDialogProps {\n  product: Product | null\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSave: (productData: any) => Promise<void>\n}\n\nexport function EditProductDialog({\n  product,\n  open,\n  onOpenChange,\n  onSave\n}: EditProductDialogProps) {\n  const [loading, setLoading] = useState(false)\n\n  // تحويل بيانات المنتج إلى تنسيق النموذج\n  const convertProductToFormData = (product: Product) => {\n    return {\n      name: product.name,\n      description: product.description,\n      category: product.category,\n      price: product.price,\n      rental_price: product.rental_price,\n      colors: product.colors,\n      sizes: product.sizes,\n      images: product.images.map((url, index) => ({\n        id: `existing-${index}`,\n        file: new File([], 'existing-image'),\n        preview: url,\n        uploaded: true,\n        fallbackUrl: url\n      })),\n      stock_quantity: product.stock_quantity,\n      is_available: product.is_available,\n      features: product.features || [],\n      specifications: product.specifications || {}\n    }\n  }\n\n  const handleSubmit = async (formData: any) => {\n    try {\n      setLoading(true)\n      await onSave({\n        ...formData,\n        id: product?.id\n      })\n      onOpenChange(false)\n    } catch (error) {\n      console.error('Error updating product:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCancel = () => {\n    onOpenChange(false)\n  }\n\n  if (!product) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"arabic-text\">\n            تعديل المنتج: {product.name}\n          </DialogTitle>\n        </DialogHeader>\n        \n        <ProductForm\n          initialData={convertProductToFormData(product)}\n          onSubmit={handleSubmit}\n          onCancel={handleCancel}\n          isEditing={true}\n        />\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;;;AATA;;;;AAsCO,SAAS,kBAAkB,EAChC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,MAAM,EACiB;;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,MAAM,2BAA2B,CAAC;QAChC,OAAO;YACL,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,UAAU,QAAQ,QAAQ;YAC1B,OAAO,QAAQ,KAAK;YACpB,cAAc,QAAQ,YAAY;YAClC,QAAQ,QAAQ,MAAM;YACtB,OAAO,QAAQ,KAAK;YACpB,QAAQ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;oBAC1C,IAAI,CAAC,SAAS,EAAE,OAAO;oBACvB,MAAM,IAAI,KAAK,EAAE,EAAE;oBACnB,SAAS;oBACT,UAAU;oBACV,aAAa;gBACf,CAAC;YACD,gBAAgB,QAAQ,cAAc;YACtC,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ,IAAI,EAAE;YAChC,gBAAgB,QAAQ,cAAc,IAAI,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,WAAW;YACX,MAAM,OAAO;gBACX,GAAG,QAAQ;gBACX,IAAI,SAAS;YACf;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;IACf;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;4BAAc;4BACpB,QAAQ,IAAI;;;;;;;;;;;;8BAI/B,6LAAC,6IAAA,CAAA,cAAW;oBACV,aAAa,yBAAyB;oBACtC,UAAU;oBACV,UAAU;oBACV,WAAW;;;;;;;;;;;;;;;;;AAKrB;GAvEgB;KAAA", "debugId": null}}, {"offset": {"line": 7200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 7352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/confirm-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\n\ninterface ConfirmDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'default' | 'destructive'\n  onConfirm: () => void\n}\n\nexport function ConfirmDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = 'تأكيد',\n  cancelText = 'إلغاء',\n  variant = 'default',\n  onConfirm\n}: ConfirmDialogProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onOpenChange(false)\n  }\n\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <AlertDialogTitle className=\"arabic-text\">{title}</AlertDialogTitle>\n          <AlertDialogDescription className=\"arabic-text\">\n            {description}\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter>\n          <AlertDialogCancel className=\"arabic-text\">{cancelText}</AlertDialogCancel>\n          <AlertDialogAction\n            onClick={handleConfirm}\n            className={variant === 'destructive' ? 'bg-red-600 hover:bg-red-700' : ''}\n          >\n            {confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAwBO,SAAS,cAAc,EAC5B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,OAAO,EACrB,aAAa,OAAO,EACpB,UAAU,SAAS,EACnB,SAAS,EACU;IACnB,MAAM,gBAAgB;QACpB;QACA,aAAa;IACf;IAEA,qBACE,6LAAC,8IAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;8BACjB,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAe;;;;;;sCAC3C,6LAAC,8IAAA,CAAA,yBAAsB;4BAAC,WAAU;sCAC/B;;;;;;;;;;;;8BAGL,6LAAC,8IAAA,CAAA,oBAAiB;;sCAChB,6LAAC,8IAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAe;;;;;;sCAC5C,6LAAC,8IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,WAAW,YAAY,gBAAgB,gCAAgC;sCAEtE;;;;;;;;;;;;;;;;;;;;;;;AAMb;KApCgB", "debugId": null}}, {"offset": {"line": 7443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'\nimport { Button } from './button'\n\nexport interface Toast {\n  id: string\n  title?: string\n  description: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  duration?: number\n}\n\ninterface ToastProps {\n  toast: Toast\n  onRemove: (id: string) => void\n}\n\nexport function ToastComponent({ toast, onRemove }: ToastProps) {\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onRemove(toast.id)\n    }, toast.duration || 5000)\n\n    return () => clearTimeout(timer)\n  }, [toast.id, toast.duration, onRemove])\n\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5 text-red-600\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-600\" />\n    }\n  }\n\n  const getBgColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200'\n      case 'error':\n        return 'bg-red-50 border-red-200'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200'\n      case 'info':\n        return 'bg-blue-50 border-blue-200'\n    }\n  }\n\n  return (\n    <div className={`rounded-lg border p-4 shadow-lg ${getBgColor()} animate-in slide-in-from-right-full`}>\n      <div className=\"flex items-start gap-3\">\n        {getIcon()}\n        <div className=\"flex-1\">\n          {toast.title && (\n            <h4 className=\"font-medium text-gray-900 arabic-text\">{toast.title}</h4>\n          )}\n          <p className=\"text-sm text-gray-700 arabic-text\">{toast.description}</p>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-6 w-6 p-0\"\n          onClick={() => onRemove(toast.id)}\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n\ninterface ToastContainerProps {\n  toasts: Toast[]\n  onRemove: (id: string) => void\n}\n\nexport function ToastContainer({ toasts, onRemove }: ToastContainerProps) {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {toasts.map((toast) => (\n        <ToastComponent key={toast.id} toast={toast} onRemove={onRemove} />\n      ))}\n    </div>\n  )\n}\n\n// Hook لإدارة التوست\nexport function useToast() {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const addToast = (toast: Omit<Toast, 'id'>) => {\n    const id = Date.now().toString()\n    setToasts(prev => [...prev, { ...toast, id }])\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const success = (description: string, title?: string) => {\n    addToast({ type: 'success', description, title })\n  }\n\n  const error = (description: string, title?: string) => {\n    addToast({ type: 'error', description, title })\n  }\n\n  const warning = (description: string, title?: string) => {\n    addToast({ type: 'warning', description, title })\n  }\n\n  const info = (description: string, title?: string) => {\n    addToast({ type: 'info', description, title })\n  }\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success,\n    error,\n    warning,\n    info\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAmBO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAc;;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,SAAS,MAAM,EAAE;gBACnB;iDAAG,MAAM,QAAQ,IAAI;YAErB;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAS;IAEvC,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,gCAAgC,EAAE,aAAa,oCAAoC,CAAC;kBACnG,cAAA,6LAAC;YAAI,WAAU;;gBACZ;8BACD,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,KAAK,kBACV,6LAAC;4BAAG,WAAU;sCAAyC,MAAM,KAAK;;;;;;sCAEpE,6LAAC;4BAAE,WAAU;sCAAqC,MAAM,WAAW;;;;;;;;;;;;8BAErE,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,MAAM,EAAE;8BAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;GAxDgB;KAAA;AA+DT,SAAS,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAuB;IACtE,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAA8B,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIrC;MARgB;AAWT,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,KAAK;oBAAE;gBAAG;aAAE;IAC/C;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,UAAU,CAAC,aAAqB;QACpC,SAAS;YAAE,MAAM;YAAW;YAAa;QAAM;IACjD;IAEA,MAAM,QAAQ,CAAC,aAAqB;QAClC,SAAS;YAAE,MAAM;YAAS;YAAa;QAAM;IAC/C;IAEA,MAAM,UAAU,CAAC,aAAqB;QACpC,SAAS;YAAE,MAAM;YAAW;YAAa;QAAM;IACjD;IAEA,MAAM,OAAO,CAAC,aAAqB;QACjC,SAAS;YAAE,MAAM;YAAQ;YAAa;QAAM;IAC9C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IArCgB", "debugId": null}}, {"offset": {"line": 7675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/products/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Navigation } from '@/components/Navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\nimport { ProductForm } from '@/components/admin/ProductForm'\nimport { EditProductDialog } from '@/components/admin/EditProductDialog'\nimport { ConfirmDialog } from '@/components/ui/confirm-dialog'\nimport { ToastContainer, useToast } from '@/components/ui/toast'\nimport Link from 'next/link'\nimport {\n  Package,\n  Plus,\n  Search,\n  Filter,\n  Edit,\n  Trash2,\n  Eye,\n  Upload,\n  Download,\n  MoreHorizontal,\n  Star,\n  TrendingUp,\n  AlertTriangle,\n  ArrowLeft,\n  Brain,\n  FileText,\n  Tag,\n  List,\n  Settings,\n  Wand2\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface Product {\n  id: string\n  name: string\n  description: string\n  category: 'gown' | 'cap' | 'tassel' | 'stole' | 'hood'\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  is_published: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n}\n\ninterface Category {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  description?: string\n  icon?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nconst categoryNames = {\n  gown: 'ثوب التخرج',\n  cap: 'قبعة التخرج',\n  tassel: 'الشرابة',\n  stole: 'الوشاح',\n  hood: 'القلنسوة'\n}\n\nexport default function ProductsManagement() {\n  const { user, profile } = useAuth()\n  const toast = useToast()\n  const [products, setProducts] = useState<Product[]>([])\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])\n  const [searchTerm, setSearchTerm] = useState('')\n  const [categoryFilter, setCategoryFilter] = useState<string>('all')\n  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all')\n  const [publishedFilter, setPublishedFilter] = useState<string>('all')\n  const [sortBy, setSortBy] = useState<string>('created_at')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [loading, setLoading] = useState(true)\n  const [showAddProduct, setShowAddProduct] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [deleteConfirm, setDeleteConfirm] = useState<{\n    open: boolean\n    productId: string\n    productName: string\n  }>({ open: false, productId: '', productName: '' })\n\n  // State للفئات\n  const [categories, setCategories] = useState<Category[]>([])\n  const [showAddCategory, setShowAddCategory] = useState(false)\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null)\n  const [categoryFormData, setCategoryFormData] = useState({\n    name_ar: '',\n    slug: '',\n    description: '',\n    icon: '',\n    is_active: true,\n    order_index: 0\n  })\n  const [activeTab, setActiveTab] = useState('products')\n\n  // جلب المنتجات من API\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/products')\n\n      if (!response.ok) {\n        throw new Error('فشل في جلب المنتجات')\n      }\n\n      const data = await response.json()\n      console.log('Fetched products response:', data)\n      console.log('Products loaded:', data.products?.length || 0)\n      setProducts(data.products || [])\n      setFilteredProducts(data.products || [])\n    } catch (error) {\n      console.error('Error fetching products:', error)\n      toast.error('فشل في جلب المنتجات')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // تطبيق الفلاتر والبحث\n  useEffect(() => {\n    let filtered = [...products]\n\n    // البحث\n    if (searchTerm) {\n      filtered = filtered.filter(product =>\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // فلتر الفئة\n    if (categoryFilter !== 'all') {\n      filtered = filtered.filter(product => product.category === categoryFilter)\n    }\n\n    // فلتر التوفر\n    if (availabilityFilter !== 'all') {\n      filtered = filtered.filter(product =>\n        availabilityFilter === 'available' ? product.is_available : !product.is_available\n      )\n    }\n\n    // فلتر النشر\n    if (publishedFilter !== 'all') {\n      filtered = filtered.filter(product =>\n        publishedFilter === 'published' ? product.is_published : !product.is_published\n      )\n    }\n\n    // الترتيب\n    filtered.sort((a, b) => {\n      let aValue: any = a[sortBy as keyof Product]\n      let bValue: any = b[sortBy as keyof Product]\n\n      if (sortBy === 'price' || sortBy === 'stock_quantity' || sortBy === 'rating') {\n        aValue = Number(aValue) || 0\n        bValue = Number(bValue) || 0\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1\n      } else {\n        return aValue < bValue ? 1 : -1\n      }\n    })\n\n    setFilteredProducts(filtered)\n  }, [products, searchTerm, categoryFilter, availabilityFilter, publishedFilter, sortBy, sortOrder])\n\n  // فتح حوار تأكيد الحذف\n  const openDeleteConfirm = (productId: string, productName: string) => {\n    setDeleteConfirm({\n      open: true,\n      productId,\n      productName\n    })\n  }\n\n  // تنفيذ حذف المنتج\n  const handleDeleteProduct = async () => {\n    try {\n      console.log('Attempting to delete product with ID:', deleteConfirm.productId)\n      console.log('Delete confirm state:', deleteConfirm)\n\n      const response = await fetch(`/api/products/${deleteConfirm.productId}`, {\n        method: 'DELETE'\n      })\n\n      console.log('Delete response status:', response.status)\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        console.log('Delete error response:', errorData)\n        throw new Error(errorData.error || 'فشل في حذف المنتج')\n      }\n\n      const successData = await response.json()\n      console.log('Delete success response:', successData)\n\n      // إغلاق نافذة التأكيد\n      setDeleteConfirm({ open: false, productId: '', productName: '' })\n\n      // تحديث قائمة المنتجات\n      await fetchProducts()\n      toast.success('تم حذف المنتج بنجاح!')\n    } catch (error) {\n      console.error('Error deleting product:', error)\n      toast.error(error instanceof Error ? error.message : 'فشل في حذف المنتج')\n      // إغلاق نافذة التأكيد حتى في حالة الخطأ\n      setDeleteConfirm({ open: false, productId: '', productName: '' })\n    }\n  }\n\n  const handleToggleAvailability = async (productId: string) => {\n    try {\n      const product = products.find(p => p.id === productId)\n      if (!product) return\n\n      const response = await fetch(`/api/products/${productId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          is_available: !product.is_available\n        })\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في تحديث حالة المنتج')\n      }\n\n      // تحديث قائمة المنتجات\n      await fetchProducts()\n      toast.success(`تم ${!product.is_available ? 'تفعيل' : 'إلغاء تفعيل'} المنتج بنجاح`)\n    } catch (error) {\n      console.error('Error toggling availability:', error)\n      toast.error(error instanceof Error ? error.message : 'فشل في تحديث حالة المنتج')\n    }\n  }\n\n  const handleTogglePublished = async (productId: string) => {\n    try {\n      const product = products.find(p => p.id === productId)\n      if (!product) return\n\n      const response = await fetch(`/api/products/${productId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          is_published: !product.is_published\n        })\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في تحديث حالة النشر')\n      }\n\n      // تحديث قائمة المنتجات\n      await fetchProducts()\n      toast.success(`تم ${!product.is_published ? 'نشر' : 'إلغاء نشر'} المنتج بنجاح`)\n    } catch (error) {\n      console.error('Error toggling published status:', error)\n      toast.error(error instanceof Error ? error.message : 'فشل في تحديث حالة النشر')\n    }\n  }\n\n  const handleAddProduct = async (productData: any) => {\n    try {\n      setLoading(true)\n\n      // معالجة الصور\n      let imageUrls: string[] = []\n      if (productData.images && productData.images.length > 0) {\n        imageUrls = productData.images.map((img: any) => {\n          // إذا كانت الصورة مرفوعة بالفعل، استخدم الرابط المحفوظ\n          if (img.uploaded && img.fallbackUrl) {\n            return img.fallbackUrl\n          }\n          // إذا لم تكن مرفوعة، استخدم المعاينة كنسخة احتياطية\n          return img.preview\n        }).filter(Boolean)\n      }\n\n      // إضافة المنتج\n      const response = await fetch('/api/products', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          ...productData,\n          images: imageUrls\n        })\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في إضافة المنتج')\n      }\n\n      const data = await response.json()\n\n      // تحديث قائمة المنتجات\n      await fetchProducts()\n\n      setShowAddProduct(false)\n      toast.success('تم إضافة المنتج بنجاح!')\n    } catch (error) {\n      console.error('Error adding product:', error)\n      toast.error(error instanceof Error ? error.message : 'فشل في إضافة المنتج')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // فتح حوار التعديل\n  const openEditDialog = (product: Product) => {\n    setEditingProduct(product)\n    setShowEditDialog(true)\n  }\n\n  // تحديث المنتج\n  const handleUpdateProduct = async (productData: any) => {\n    try {\n      setLoading(true)\n\n      // معالجة الصور\n      let imageUrls: string[] = []\n      if (productData.images && productData.images.length > 0) {\n        imageUrls = productData.images.map((img: any) => {\n          if (img.uploaded && img.fallbackUrl) {\n            return img.fallbackUrl\n          }\n          return img.preview\n        }).filter(Boolean)\n      }\n\n      const response = await fetch(`/api/products/${productData.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          ...productData,\n          images: imageUrls\n        })\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'فشل في تحديث المنتج')\n      }\n\n      await fetchProducts()\n      toast.success('تم تحديث المنتج بنجاح!')\n    } catch (error) {\n      console.error('Error updating product:', error)\n      toast.error(error instanceof Error ? error.message : 'فشل في تحديث المنتج')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // جلب الفئات\n  const fetchCategories = async () => {\n    try {\n      const response = await fetch('/api/categories')\n      if (response.ok) {\n        const data = await response.json()\n        setCategories(data.categories || [])\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error)\n    }\n  }\n\n  useEffect(() => {\n    fetchCategories()\n  }, [])\n\n  // إضافة/تحديث فئة\n  const handleSaveCategory = async () => {\n    try {\n      const url = editingCategory ? `/api/categories/${editingCategory.id}` : '/api/categories'\n      const method = editingCategory ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(categoryFormData)\n      })\n\n      if (response.ok) {\n        await fetchCategories()\n        resetCategoryForm()\n        toast.success(editingCategory ? 'تم تحديث الفئة بنجاح!' : 'تم إضافة الفئة بنجاح!')\n      } else {\n        const errorData = await response.json()\n        toast.error(errorData.error || 'فشل في حفظ الفئة')\n      }\n    } catch (error) {\n      console.error('Error saving category:', error)\n      toast.error('فشل في حفظ الفئة')\n    }\n  }\n\n  // إعادة تعيين نموذج الفئة\n  const resetCategoryForm = () => {\n    setCategoryFormData({\n      name_ar: '',\n      slug: '',\n      description: '',\n      icon: '',\n      is_active: true,\n      order_index: 0\n    })\n    setEditingCategory(null)\n    setShowAddCategory(false)\n  }\n\n  // تحضير تعديل فئة\n  const prepareEditCategory = (category: any) => {\n    setCategoryFormData({\n      name_ar: category.name_ar,\n      slug: category.slug,\n      description: category.description || '',\n      icon: category.icon || '',\n      is_active: category.is_active,\n      order_index: category.order_index\n    })\n    setEditingCategory(category)\n    setShowAddCategory(true)\n  }\n\n  // حذف فئة\n  const handleDeleteCategory = async (categoryId: string) => {\n    try {\n      const response = await fetch(`/api/categories/${categoryId}`, {\n        method: 'DELETE'\n      })\n\n      if (response.ok) {\n        await fetchCategories()\n        toast.success('تم حذف الفئة بنجاح!')\n      } else {\n        const errorData = await response.json()\n        toast.error(errorData.error || 'فشل في حذف الفئة')\n      }\n    } catch (error) {\n      console.error('Error deleting category:', error)\n      toast.error('فشل في حذف الفئة')\n    }\n  }\n\n  // تشخيص للتطوير\n  console.log('User:', user)\n  console.log('Profile:', profile)\n  console.log('Profile role:', profile?.role)\n\n  if (!user || profile?.role !== 'admin') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-red-600 arabic-text\">غير مصرح لك بالوصول</h1>\n          <p className=\"text-gray-600 mt-2 arabic-text\">هذه الصفحة مخصصة للمديرين فقط</p>\n          <p className=\"text-sm text-gray-500 mt-2\">User: {user ? 'موجود' : 'غير موجود'}</p>\n          <p className=\"text-sm text-gray-500\">Role: {profile?.role || 'غير محدد'}</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <Navigation />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center gap-4 mb-4\">\n            <Button variant=\"outline\" size=\"sm\" asChild>\n              <Link href=\"/dashboard/admin\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                العودة للوحة التحكم\n              </Link>\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text\">\n                إدارة المنتجات والفئات 📦\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n                إضافة وتعديل وإدارة منتجات وفئات المنصة\n              </p>\n            </div>\n            <div className=\"flex gap-3\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Download className=\"h-4 w-4 mr-2\" />\n                تصدير\n              </Button>\n              {activeTab === 'products' && (\n                <Button size=\"sm\" onClick={() => setShowAddProduct(true)}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  إضافة منتج جديد\n                </Button>\n              )}\n              {activeTab === 'categories' && (\n                <Button size=\"sm\" onClick={() => { resetCategoryForm(); setShowAddCategory(true); }}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  إضافة فئة جديدة\n                </Button>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* AI Enhancement Stats */}\n        <Card className=\"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-3 bg-blue-600 rounded-lg\">\n                  <Brain className=\"h-6 w-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white arabic-text\">\n                    ميزات الذكاء الاصطناعي المتاحة\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 arabic-text\">\n                    استخدم الذكاء الاصطناعي لتحسين منتجاتك تلقائياً\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">6</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">ميزات متاحة</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">نشط</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">حالة النظام</div>\n                </div>\n              </div>\n            </div>\n            <div className=\"mt-4 flex flex-wrap gap-2\">\n              <Badge variant=\"outline\" className=\"bg-white dark:bg-gray-800\">\n                <FileText className=\"h-3 w-3 mr-1\" />\n                توليد الأوصاف\n              </Badge>\n              <Badge variant=\"outline\" className=\"bg-white dark:bg-gray-800\">\n                <Tag className=\"h-3 w-3 mr-1\" />\n                توليد العناوين\n              </Badge>\n              <Badge variant=\"outline\" className=\"bg-white dark:bg-gray-800\">\n                <List className=\"h-3 w-3 mr-1\" />\n                توليد الميزات\n              </Badge>\n              <Badge variant=\"outline\" className=\"bg-white dark:bg-gray-800\">\n                <Settings className=\"h-3 w-3 mr-1\" />\n                توليد المواصفات\n              </Badge>\n              <Badge variant=\"outline\" className=\"bg-white dark:bg-gray-800\">\n                <Wand2 className=\"h-3 w-3 mr-1\" />\n                اقتراح الفئات\n              </Badge>\n              <Badge variant=\"outline\" className=\"bg-white dark:bg-gray-800\">\n                <Search className=\"h-3 w-3 mr-1\" />\n                تحسين SEO\n              </Badge>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Tabs */}\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"products\" className=\"arabic-text\">المنتجات</TabsTrigger>\n            <TabsTrigger value=\"categories\" className=\"arabic-text\">الفئات</TabsTrigger>\n          </TabsList>\n\n          {/* Products Tab */}\n          <TabsContent value=\"products\" className=\"space-y-6\">\n\n        {/* Stats Cards */}\n        <div className=\"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                    إجمالي المنتجات\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {products.length}\n                  </p>\n                </div>\n                <Package className=\"h-8 w-8 text-blue-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                    المنتجات المتاحة\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {products.filter(p => p.is_available).length}\n                  </p>\n                </div>\n                <Star className=\"h-8 w-8 text-green-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                    المنتجات المنشورة\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {products.filter(p => p.is_published).length}\n                  </p>\n                </div>\n                <Eye className=\"h-8 w-8 text-blue-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                    مخزون منخفض\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {products.filter(p => p.stock_quantity < 20).length}\n                  </p>\n                </div>\n                <AlertTriangle className=\"h-8 w-8 text-orange-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                    متوسط التقييم\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {(products.reduce((acc, p) => acc + (p.rating || 0), 0) / products.length).toFixed(1)}\n                  </p>\n                </div>\n                <TrendingUp className=\"h-8 w-8 text-purple-600\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters and Search */}\n        <Card className=\"mb-6\">\n          <CardContent className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n              {/* البحث */}\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"البحث في المنتجات...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 arabic-text\"\n                />\n              </div>\n\n              {/* فلتر الفئة */}\n              <Select value={categoryFilter} onValueChange={setCategoryFilter}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"جميع الفئات\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">جميع الفئات</SelectItem>\n                  {Object.entries(categoryNames).map(([key, name]) => (\n                    <SelectItem key={key} value={key}>{name}</SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n\n              {/* فلتر التوفر */}\n              <Select value={availabilityFilter} onValueChange={setAvailabilityFilter}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"جميع المنتجات\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">جميع المنتجات</SelectItem>\n                  <SelectItem value=\"available\">متاح</SelectItem>\n                  <SelectItem value=\"unavailable\">غير متاح</SelectItem>\n                </SelectContent>\n              </Select>\n\n              {/* فلتر النشر */}\n              <Select value={publishedFilter} onValueChange={setPublishedFilter}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"حالة النشر\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">جميع المنتجات</SelectItem>\n                  <SelectItem value=\"published\">منشور</SelectItem>\n                  <SelectItem value=\"unpublished\">غير منشور</SelectItem>\n                </SelectContent>\n              </Select>\n\n              {/* ترتيب حسب */}\n              <Select value={sortBy} onValueChange={setSortBy}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"ترتيب حسب\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"created_at\">تاريخ الإنشاء</SelectItem>\n                  <SelectItem value=\"name\">الاسم</SelectItem>\n                  <SelectItem value=\"price\">السعر</SelectItem>\n                  <SelectItem value=\"stock_quantity\">المخزون</SelectItem>\n                  <SelectItem value=\"rating\">التقييم</SelectItem>\n                </SelectContent>\n              </Select>\n\n              {/* اتجاه الترتيب */}\n              <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"desc\">تنازلي</SelectItem>\n                  <SelectItem value=\"asc\">تصاعدي</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Products Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"arabic-text\">قائمة المنتجات ({filteredProducts.length})</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                <p className=\"text-gray-500 mt-2 arabic-text\">جاري التحميل...</p>\n              </div>\n            ) : filteredProducts.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-500 arabic-text\">لا توجد منتجات</p>\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead>\n                    <tr className=\"border-b\">\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">المنتج</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">الفئة</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">السعر</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">المخزون</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">التقييم</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">الحالة</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">النشر</th>\n                      <th className=\"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text\">الإجراءات</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {filteredProducts.map((product) => (\n                      <tr key={product.id} className=\"border-b hover:bg-gray-50 dark:hover:bg-gray-800\">\n                        <td className=\"py-4 px-4\">\n                          <div className=\"flex items-center gap-4\">\n                            <div className=\"relative\">\n                              <img\n                                src={product.images[0] || '/api/placeholder/80/80'}\n                                alt={product.name}\n                                className=\"w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-700\"\n                              />\n                              {product.images.length > 1 && (\n                                <div className=\"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                                  {product.images.length}\n                                </div>\n                              )}\n                            </div>\n                            <div className=\"flex-1 min-w-0\">\n                              <p className=\"font-medium text-gray-900 dark:text-white arabic-text line-clamp-1\">\n                                {product.name}\n                              </p>\n                              <p className=\"text-sm text-gray-500 arabic-text line-clamp-2 mt-1\">\n                                {product.description}\n                              </p>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <Badge variant=\"outline\" className=\"arabic-text\">\n                            {categoryNames[product.category]}\n                          </Badge>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <div className=\"text-sm\">\n                            <p className=\"price font-medium text-gray-900 dark:text-white\">\n                              {product.price} Dhs\n                            </p>\n                            {product.rental_price && (\n                              <p className=\"price text-gray-500\">\n                                إيجار: {product.rental_price} Dhs\n                              </p>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <div className=\"flex items-center gap-2\">\n                            <span className={`font-medium ${\n                              product.stock_quantity < 20\n                                ? 'text-red-600'\n                                : product.stock_quantity < 50\n                                  ? 'text-orange-600'\n                                  : 'text-green-600'\n                            }`}>\n                              {product.stock_quantity}\n                            </span>\n                            {product.stock_quantity < 20 && (\n                              <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <div className=\"rating flex items-center gap-1\">\n                            <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            <span className=\"number text-sm font-medium\">\n                              {product.rating?.toFixed(1) || 'N/A'}\n                            </span>\n                            <span className=\"number text-xs text-gray-500\">\n                              ({product.reviews_count || 0})\n                            </span>\n                          </div>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <Badge\n                            variant={product.is_available ? \"default\" : \"secondary\"}\n                            className=\"arabic-text\"\n                          >\n                            {product.is_available ? 'متاح' : 'غير متاح'}\n                          </Badge>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <Badge\n                            variant={product.is_published ? \"default\" : \"destructive\"}\n                            className=\"arabic-text\"\n                          >\n                            {product.is_published ? 'منشور' : 'غير منشور'}\n                          </Badge>\n                        </td>\n                        <td className=\"py-4 px-4\">\n                          <TooltipProvider>\n                            <div className=\"actions flex items-center gap-2\">\n                              <Tooltip>\n                                <TooltipTrigger asChild>\n                                  <Button size=\"sm\" variant=\"outline\" asChild>\n                                    <a href={`/product/${product.id}`} target=\"_blank\">\n                                      <Eye className=\"h-4 w-4\" />\n                                    </a>\n                                  </Button>\n                                </TooltipTrigger>\n                                <TooltipContent>\n                                  <p>عرض المنتج</p>\n                                </TooltipContent>\n                              </Tooltip>\n\n                              <Tooltip>\n                                <TooltipTrigger asChild>\n                                  <Button\n                                    size=\"sm\"\n                                    variant=\"outline\"\n                                    onClick={() => openEditDialog(product)}\n                                  >\n                                    <Edit className=\"h-4 w-4\" />\n                                  </Button>\n                                </TooltipTrigger>\n                                <TooltipContent>\n                                  <p>تعديل المنتج</p>\n                                </TooltipContent>\n                              </Tooltip>\n\n                              <Tooltip>\n                                <TooltipTrigger asChild>\n                                  <Button\n                                    size=\"sm\"\n                                    variant=\"outline\"\n                                    onClick={() => handleToggleAvailability(product.id)}\n                                  >\n                                    {product.is_available ? '🔒' : '🔓'}\n                                  </Button>\n                                </TooltipTrigger>\n                                <TooltipContent>\n                                  <p>{product.is_available ? 'إلغاء التوفر' : 'تفعيل التوفر'}</p>\n                                </TooltipContent>\n                              </Tooltip>\n\n                              <Tooltip>\n                                <TooltipTrigger asChild>\n                                  <Button\n                                    size=\"sm\"\n                                    variant=\"outline\"\n                                    onClick={() => handleTogglePublished(product.id)}\n                                  >\n                                    {product.is_published ? '👁️' : '🙈'}\n                                  </Button>\n                                </TooltipTrigger>\n                                <TooltipContent>\n                                  <p>{product.is_published ? 'إلغاء النشر' : 'نشر المنتج'}</p>\n                                </TooltipContent>\n                              </Tooltip>\n\n                              <Tooltip>\n                                <TooltipTrigger asChild>\n                                  <Button\n                                    size=\"sm\"\n                                    variant=\"outline\"\n                                    onClick={() => openDeleteConfirm(product.id, product.name)}\n                                    className=\"text-red-600 hover:text-red-700\"\n                                  >\n                                    <Trash2 className=\"h-4 w-4\" />\n                                  </Button>\n                                </TooltipTrigger>\n                                <TooltipContent>\n                                  <p>حذف المنتج</p>\n                                </TooltipContent>\n                              </Tooltip>\n                            </div>\n                          </TooltipProvider>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n          </TabsContent>\n\n          {/* Categories Tab */}\n          <TabsContent value=\"categories\" className=\"space-y-6\">\n            {/* Categories Management */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"arabic-text\">إدارة الفئات</CardTitle>\n                <CardDescription className=\"arabic-text\">\n                  إضافة وتعديل وإدارة فئات المنتجات\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Table>\n                  <TableHeader>\n                    <TableRow>\n                      <TableHead className=\"arabic-text\">الاسم</TableHead>\n                      <TableHead className=\"arabic-text\">الرابط المختصر</TableHead>\n                      <TableHead className=\"arabic-text\">الحالة</TableHead>\n                      <TableHead className=\"arabic-text\">الترتيب</TableHead>\n                      <TableHead className=\"arabic-text\">تاريخ الإنشاء</TableHead>\n                      <TableHead className=\"arabic-text\">الإجراءات</TableHead>\n                    </TableRow>\n                  </TableHeader>\n                  <TableBody>\n                    {categories.map((category) => (\n                      <TableRow key={category.id}>\n                        <TableCell>\n                          <div className=\"flex items-center gap-2\">\n                            {category.icon && <span className=\"text-lg\">{category.icon}</span>}\n                            <div>\n                              <div className=\"font-medium arabic-text\">{category.name_ar}</div>\n                              {category.description && (\n                                <div className=\"text-sm text-gray-500 arabic-text\">{category.description}</div>\n                              )}\n                            </div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <code className=\"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm\">\n                            {category.slug}\n                          </code>\n                        </TableCell>\n                        <TableCell>\n                          <Badge variant={category.is_active ? \"default\" : \"secondary\"}>\n                            {category.is_active ? 'نشط' : 'غير نشط'}\n                          </Badge>\n                        </TableCell>\n                        <TableCell>{category.order_index}</TableCell>\n                        <TableCell>\n                          {new Date(category.created_at).toLocaleDateString('en-US')}\n                        </TableCell>\n                        <TableCell>\n                          <DropdownMenu>\n                            <DropdownMenuTrigger asChild>\n                              <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n                                <MoreHorizontal className=\"h-4 w-4\" />\n                              </Button>\n                            </DropdownMenuTrigger>\n                            <DropdownMenuContent align=\"end\">\n                              <DropdownMenuItem onClick={() => prepareEditCategory(category)}>\n                                <Edit className=\"h-4 w-4 mr-2\" />\n                                تعديل\n                              </DropdownMenuItem>\n                              <DropdownMenuItem\n                                onClick={() => handleDeleteCategory(category.id)}\n                                className=\"text-red-600\"\n                              >\n                                <Trash2 className=\"h-4 w-4 mr-2\" />\n                                حذف\n                              </DropdownMenuItem>\n                            </DropdownMenuContent>\n                          </DropdownMenu>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </main>\n\n      {/* Add/Edit Category Dialog */}\n      <Dialog open={showAddCategory} onOpenChange={setShowAddCategory}>\n        <DialogContent className=\"max-w-2xl\">\n          <DialogHeader>\n            <DialogTitle className=\"arabic-text\">\n              {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}\n            </DialogTitle>\n            <DialogDescription className=\"arabic-text\">\n              {editingCategory ? 'تعديل بيانات الفئة' : 'أدخل بيانات الفئة الجديدة'}\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"category-name\" className=\"arabic-text\">اسم الفئة (مطلوب)</Label>\n              <Input\n                id=\"category-name\"\n                value={categoryFormData.name_ar}\n                onChange={(e) => setCategoryFormData(prev => ({ ...prev, name_ar: e.target.value }))}\n                placeholder=\"أدخل اسم الفئة\"\n                className=\"arabic-text\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"category-slug\" className=\"arabic-text\">الرابط المختصر (مطلوب)</Label>\n              <Input\n                id=\"category-slug\"\n                value={categoryFormData.slug}\n                onChange={(e) => setCategoryFormData(prev => ({ ...prev, slug: e.target.value }))}\n                placeholder=\"category-slug\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"category-description\" className=\"arabic-text\">الوصف</Label>\n              <Textarea\n                id=\"category-description\"\n                value={categoryFormData.description}\n                onChange={(e) => setCategoryFormData(prev => ({ ...prev, description: e.target.value }))}\n                placeholder=\"وصف الفئة\"\n                className=\"arabic-text\"\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"category-icon\" className=\"arabic-text\">الأيقونة</Label>\n              <Input\n                id=\"category-icon\"\n                value={categoryFormData.icon}\n                onChange={(e) => setCategoryFormData(prev => ({ ...prev, icon: e.target.value }))}\n                placeholder=\"🏷️\"\n              />\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                id=\"category-active\"\n                checked={categoryFormData.is_active}\n                onChange={(e) => setCategoryFormData(prev => ({ ...prev, is_active: e.target.checked }))}\n              />\n              <Label htmlFor=\"category-active\" className=\"arabic-text\">فئة نشطة</Label>\n            </div>\n            <div className=\"flex justify-end gap-3\">\n              <Button variant=\"outline\" onClick={resetCategoryForm}>\n                إلغاء\n              </Button>\n              <Button onClick={handleSaveCategory}>\n                {editingCategory ? 'تحديث' : 'إضافة'}\n              </Button>\n            </div>\n          </div>\n        </DialogContent>\n      </Dialog>\n\n      {/* Add Product Dialog */}\n      <Dialog open={showAddProduct} onOpenChange={setShowAddProduct}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle className=\"arabic-text\">إضافة منتج جديد</DialogTitle>\n            <DialogDescription className=\"arabic-text\">\n              أدخل تفاصيل المنتج الجديد\n            </DialogDescription>\n          </DialogHeader>\n          <ProductForm\n            onSubmit={handleAddProduct}\n            onCancel={() => setShowAddProduct(false)}\n          />\n        </DialogContent>\n      </Dialog>\n\n      {/* Edit Product Dialog */}\n      <EditProductDialog\n        product={editingProduct}\n        open={showEditDialog}\n        onOpenChange={setShowEditDialog}\n        onSave={handleUpdateProduct}\n      />\n\n      {/* Delete Confirmation Dialog */}\n      <ConfirmDialog\n        open={deleteConfirm.open}\n        onOpenChange={(open) => setDeleteConfirm(prev => ({ ...prev, open }))}\n        title=\"تأكيد حذف المنتج\"\n        description={`هل أنت متأكد من حذف المنتج \"${deleteConfirm.productName}\"؟ هذا الإجراء لا يمكن التراجع عنه.`}\n        confirmText=\"حذف\"\n        cancelText=\"إلغاء\"\n        variant=\"destructive\"\n        onConfirm={handleDeleteProduct}\n      />\n\n      {/* Toast Container */}\n      <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAtBA;;;;;;;;;;;;;;;;;;;;;;AA+EA,MAAM,gBAAgB;IACpB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,OAAO;IACP,MAAM;AACR;AAEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,QAAQ,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI9C;QAAE,MAAM;QAAO,WAAW;QAAI,aAAa;IAAG;IAEjD,eAAe;IACf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,SAAS;QACT,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW;QACX,aAAa;IACf;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,QAAQ,GAAG,CAAC,oBAAoB,KAAK,QAAQ,EAAE,UAAU;YACzD,YAAY,KAAK,QAAQ,IAAI,EAAE;YAC/B,oBAAoB,KAAK,QAAQ,IAAI,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,WAAW;mBAAI;aAAS;YAE5B,QAAQ;YACR,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;oDAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAErE;YAEA,aAAa;YACb,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,SAAS,MAAM;oDAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;;YAC7D;YAEA,cAAc;YACd,IAAI,uBAAuB,OAAO;gBAChC,WAAW,SAAS,MAAM;oDAAC,CAAA,UACzB,uBAAuB,cAAc,QAAQ,YAAY,GAAG,CAAC,QAAQ,YAAY;;YAErF;YAEA,aAAa;YACb,IAAI,oBAAoB,OAAO;gBAC7B,WAAW,SAAS,MAAM;oDAAC,CAAA,UACzB,oBAAoB,cAAc,QAAQ,YAAY,GAAG,CAAC,QAAQ,YAAY;;YAElF;YAEA,UAAU;YACV,SAAS,IAAI;gDAAC,CAAC,GAAG;oBAChB,IAAI,SAAc,CAAC,CAAC,OAAwB;oBAC5C,IAAI,SAAc,CAAC,CAAC,OAAwB;oBAE5C,IAAI,WAAW,WAAW,WAAW,oBAAoB,WAAW,UAAU;wBAC5E,SAAS,OAAO,WAAW;wBAC3B,SAAS,OAAO,WAAW;oBAC7B;oBAEA,IAAI,cAAc,OAAO;wBACvB,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC,OAAO;wBACL,OAAO,SAAS,SAAS,IAAI,CAAC;oBAChC;gBACF;;YAEA,oBAAoB;QACtB;uCAAG;QAAC;QAAU;QAAY;QAAgB;QAAoB;QAAiB;QAAQ;KAAU;IAEjG,uBAAuB;IACvB,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,iBAAiB;YACf,MAAM;YACN;YACA;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,sBAAsB;QAC1B,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC,cAAc,SAAS;YAC5E,QAAQ,GAAG,CAAC,yBAAyB;YAErC,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,cAAc,SAAS,EAAE,EAAE;gBACvE,QAAQ;YACV;YAEA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,MAAM;YAEtD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,GAAG,CAAC,0BAA0B;gBACtC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,cAAc,MAAM,SAAS,IAAI;YACvC,QAAQ,GAAG,CAAC,4BAA4B;YAExC,sBAAsB;YACtB,iBAAiB;gBAAE,MAAM;gBAAO,WAAW;gBAAI,aAAa;YAAG;YAE/D,uBAAuB;YACvB,MAAM;YACN,MAAM,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACrD,wCAAwC;YACxC,iBAAiB;gBAAE,MAAM;gBAAO,WAAW;gBAAI,aAAa;YAAG;QACjE;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,cAAc,CAAC,QAAQ,YAAY;gBACrC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,uBAAuB;YACvB,MAAM;YACN,MAAM,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,YAAY,GAAG,UAAU,cAAc,aAAa,CAAC;QACpF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,cAAc,CAAC,QAAQ,YAAY;gBACrC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,uBAAuB;YACvB,MAAM;YACN,MAAM,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,YAAY,GAAG,QAAQ,YAAY,aAAa,CAAC;QAChF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,WAAW;YAEX,eAAe;YACf,IAAI,YAAsB,EAAE;YAC5B,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,MAAM,GAAG,GAAG;gBACvD,YAAY,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC;oBAClC,uDAAuD;oBACvD,IAAI,IAAI,QAAQ,IAAI,IAAI,WAAW,EAAE;wBACnC,OAAO,IAAI,WAAW;oBACxB;oBACA,oDAAoD;oBACpD,OAAO,IAAI,OAAO;gBACpB,GAAG,MAAM,CAAC;YACZ;YAEA,eAAe;YACf,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,WAAW;oBACd,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,uBAAuB;YACvB,MAAM;YAEN,kBAAkB;YAClB,MAAM,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,WAAW;QACb;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,eAAe;IACf,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,WAAW;YAEX,eAAe;YACf,IAAI,YAAsB,EAAE;YAC5B,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,MAAM,GAAG,GAAG;gBACvD,YAAY,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,IAAI,QAAQ,IAAI,IAAI,WAAW,EAAE;wBACnC,OAAO,IAAI,WAAW;oBACxB;oBACA,OAAO,IAAI,OAAO;gBACpB,GAAG,MAAM,CAAC;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,YAAY,EAAE,EAAE,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,WAAW;oBACd,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM;YACN,MAAM,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,WAAW;QACb;IACF;IAEA,aAAa;IACb,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc,KAAK,UAAU,IAAI,EAAE;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,MAAM,kBAAkB,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,EAAE,GAAG;YACxE,MAAM,SAAS,kBAAkB,QAAQ;YAEzC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN;gBACA,MAAM,OAAO,CAAC,kBAAkB,0BAA0B;YAC5D,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,KAAK,CAAC,UAAU,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,KAAK,CAAC;QACd;IACF;IAEA,0BAA0B;IAC1B,MAAM,oBAAoB;QACxB,oBAAoB;YAClB,SAAS;YACT,MAAM;YACN,aAAa;YACb,MAAM;YACN,WAAW;YACX,aAAa;QACf;QACA,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,kBAAkB;IAClB,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;YAClB,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW,IAAI;YACrC,MAAM,SAAS,IAAI,IAAI;YACvB,WAAW,SAAS,SAAS;YAC7B,aAAa,SAAS,WAAW;QACnC;QACA,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,UAAU;IACV,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,YAAY,EAAE;gBAC5D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,MAAM,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,KAAK,CAAC,UAAU,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,KAAK,CAAC;QACd;IACF;IAEA,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,YAAY;IACxB,QAAQ,GAAG,CAAC,iBAAiB,SAAS;IAEtC,IAAI,CAAC,QAAQ,SAAS,SAAS,SAAS;QACtC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8C;;;;;;kCAC5D,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;kCAC9C,6LAAC;wBAAE,WAAU;;4BAA6B;4BAAO,OAAO,UAAU;;;;;;;kCAClE,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAO,SAAS,QAAQ;;;;;;;;;;;;;;;;;;IAIrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,OAAO;8CACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAM5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,6LAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;kDAInE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGtC,cAAc,4BACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS,IAAM,kBAAkB;;kEACjD,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIpC,cAAc,8BACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;oDAAQ;oDAAqB,mBAAmB;gDAAO;;kEAChF,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3C,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAkE;;;;;;sEAGhF,6LAAC;4DAAE,WAAU;sEAA+C;;;;;;;;;;;;;;;;;;sDAKhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,6LAAC;4DAAI,WAAU;sEAAuD;;;;;;;;;;;;8DAExE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEAAuD;;;;;;;;;;;;;;;;;;;;;;;;8CAI5E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC,kNAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,6LAAC,mIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;kDAAc;;;;;;kDACtD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;kDAAc;;;;;;;;;;;;0CAI1D,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDAG1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;0DACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAmE;;;;;;kFAGhF,6LAAC;wEAAE,WAAU;kFACV,SAAS,MAAM;;;;;;;;;;;;0EAGpB,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAKzB,6LAAC,mIAAA,CAAA,OAAI;0DACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAmE;;;;;;kFAGhF,6LAAC;wEAAE,WAAU;kFACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,EAAE,MAAM;;;;;;;;;;;;0EAGhD,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAKtB,6LAAC,mIAAA,CAAA,OAAI;0DACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAmE;;;;;;kFAGhF,6LAAC;wEAAE,WAAU;kFACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,EAAE,MAAM;;;;;;;;;;;;0EAGhD,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAKrB,6LAAC,mIAAA,CAAA,OAAI;0DACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAmE;;;;;;kFAGhF,6LAAC;wEAAE,WAAU;kFACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,GAAG,IAAI,MAAM;;;;;;;;;;;;0EAGvD,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAK/B,6LAAC,mIAAA,CAAA,OAAI;0DACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAmE;;;;;;kFAGhF,6LAAC;wEAAE,WAAU;kFACV,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,MAAM,IAAI,CAAC,GAAG,KAAK,SAAS,MAAM,EAAE,OAAO,CAAC;;;;;;;;;;;;0EAGvF,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO9B,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,WAAU;;;;;;;;;;;;kEAKd,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAgB,eAAe;;0EAC5C,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAM;;;;;;oEACvB,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,iBAC7C,6LAAC,qIAAA,CAAA,aAAU;4EAAW,OAAO;sFAAM;2EAAlB;;;;;;;;;;;;;;;;;kEAMvB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAoB,eAAe;;0EAChD,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAM;;;;;;kFACxB,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;kFAC9B,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAc;;;;;;;;;;;;;;;;;;kEAKpC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAiB,eAAe;;0EAC7C,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAM;;;;;;kFACxB,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;kFAC9B,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAc;;;;;;;;;;;;;;;;;;kEAKpC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAQ,eAAe;;0EACpC,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAa;;;;;;kFAC/B,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;kFACzB,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ;;;;;;kFAC1B,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAiB;;;;;;kFACnC,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAK/B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAW,eAAe,CAAC,QAA0B,aAAa;;0EAC/E,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;kFACzB,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQlC,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;wDAAc;wDAAiB,iBAAiB,MAAM;wDAAC;;;;;;;;;;;;0DAE9E,6LAAC,mIAAA,CAAA,cAAW;0DACT,wBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAE,WAAU;sEAAiC;;;;;;;;;;;2DAE9C,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAE,WAAU;sEAA4B;;;;;;;;;;;yEAG3C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;0EACC,cAAA,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;sFAC3F,6LAAC;4EAAG,WAAU;sFAA6E;;;;;;;;;;;;;;;;;0EAG/F,6LAAC;0EACE,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wEAAoB,WAAU;;0FAC7B,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGACC,KAAK,QAAQ,MAAM,CAAC,EAAE,IAAI;oGAC1B,KAAK,QAAQ,IAAI;oGACjB,WAAU;;;;;;gGAEX,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;oGAAI,WAAU;8GACZ,QAAQ,MAAM,CAAC,MAAM;;;;;;;;;;;;sGAI5B,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGAAE,WAAU;8GACV,QAAQ,IAAI;;;;;;8GAEf,6LAAC;oGAAE,WAAU;8GACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;0FAK5B,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;8FAChC,aAAa,CAAC,QAAQ,QAAQ,CAAC;;;;;;;;;;;0FAGpC,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAE,WAAU;;gGACV,QAAQ,KAAK;gGAAC;;;;;;;wFAEhB,QAAQ,YAAY,kBACnB,6LAAC;4FAAE,WAAU;;gGAAsB;gGACzB,QAAQ,YAAY;gGAAC;;;;;;;;;;;;;;;;;;0FAKrC,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,cAAc,GAAG,KACrB,iBACA,QAAQ,cAAc,GAAG,KACvB,oBACA,kBACN;sGACC,QAAQ,cAAc;;;;;;wFAExB,QAAQ,cAAc,GAAG,oBACxB,6LAAC,2NAAA,CAAA,gBAAa;4FAAC,WAAU;;;;;;;;;;;;;;;;;0FAI/B,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,qMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;sGAChB,6LAAC;4FAAK,WAAU;sGACb,QAAQ,MAAM,EAAE,QAAQ,MAAM;;;;;;sGAEjC,6LAAC;4FAAK,WAAU;;gGAA+B;gGAC3C,QAAQ,aAAa,IAAI;gGAAE;;;;;;;;;;;;;;;;;;0FAInC,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;oFACJ,SAAS,QAAQ,YAAY,GAAG,YAAY;oFAC5C,WAAU;8FAET,QAAQ,YAAY,GAAG,SAAS;;;;;;;;;;;0FAGrC,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;oFACJ,SAAS,QAAQ,YAAY,GAAG,YAAY;oFAC5C,WAAU;8FAET,QAAQ,YAAY,GAAG,UAAU;;;;;;;;;;;0FAGtC,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC,sIAAA,CAAA,kBAAe;8FACd,cAAA,6LAAC;wFAAI,WAAU;;0GACb,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GAAC,MAAK;4GAAK,SAAQ;4GAAU,OAAO;sHACzC,cAAA,6LAAC;gHAAE,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gHAAE,QAAO;0HACxC,cAAA,6LAAC,mMAAA,CAAA,MAAG;oHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;kHAIrB,6LAAC,sIAAA,CAAA,iBAAc;kHACb,cAAA,6LAAC;sHAAE;;;;;;;;;;;;;;;;;0GAIP,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAQ;4GACR,SAAS,IAAM,eAAe;sHAE9B,cAAA,6LAAC,8MAAA,CAAA,OAAI;gHAAC,WAAU;;;;;;;;;;;;;;;;kHAGpB,6LAAC,sIAAA,CAAA,iBAAc;kHACb,cAAA,6LAAC;sHAAE;;;;;;;;;;;;;;;;;0GAIP,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAQ;4GACR,SAAS,IAAM,yBAAyB,QAAQ,EAAE;sHAEjD,QAAQ,YAAY,GAAG,OAAO;;;;;;;;;;;kHAGnC,6LAAC,sIAAA,CAAA,iBAAc;kHACb,cAAA,6LAAC;sHAAG,QAAQ,YAAY,GAAG,iBAAiB;;;;;;;;;;;;;;;;;0GAIhD,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAQ;4GACR,SAAS,IAAM,sBAAsB,QAAQ,EAAE;sHAE9C,QAAQ,YAAY,GAAG,QAAQ;;;;;;;;;;;kHAGpC,6LAAC,sIAAA,CAAA,iBAAc;kHACb,cAAA,6LAAC;sHAAG,QAAQ,YAAY,GAAG,gBAAgB;;;;;;;;;;;;;;;;;0GAI/C,6LAAC,sIAAA,CAAA,UAAO;;kHACN,6LAAC,sIAAA,CAAA,iBAAc;wGAAC,OAAO;kHACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAQ;4GACR,SAAS,IAAM,kBAAkB,QAAQ,EAAE,EAAE,QAAQ,IAAI;4GACzD,WAAU;sHAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;;;;;;;;;;;kHAGtB,6LAAC,sIAAA,CAAA,iBAAc;kHACb,cAAA,6LAAC;sHAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uEA9JN,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CA+K/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;0CAExC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAc;;;;;;8DACnC,6LAAC,mIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAc;;;;;;;;;;;;sDAI3C,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;8EACnC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;8EACnC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;8EACnC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;8EACnC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;8EACnC,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;;;;;;;;;;;;kEAGvC,6LAAC,oIAAA,CAAA,YAAS;kEACP,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;;gFACZ,SAAS,IAAI,kBAAI,6LAAC;oFAAK,WAAU;8FAAW,SAAS,IAAI;;;;;;8FAC1D,6LAAC;;sGACC,6LAAC;4FAAI,WAAU;sGAA2B,SAAS,OAAO;;;;;;wFACzD,SAAS,WAAW,kBACnB,6LAAC;4FAAI,WAAU;sGAAqC,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;kFAKhF,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAK,WAAU;sFACb,SAAS,IAAI;;;;;;;;;;;kFAGlB,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAS,SAAS,SAAS,GAAG,YAAY;sFAC9C,SAAS,SAAS,GAAG,QAAQ;;;;;;;;;;;kFAGlC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,SAAS,WAAW;;;;;;kFAChC,6LAAC,oIAAA,CAAA,YAAS;kFACP,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC;;;;;;kFAEpD,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8FACX,6LAAC,+IAAA,CAAA,sBAAmB;oFAAC,OAAO;8FAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wFAAC,SAAQ;wFAAQ,WAAU;kGAChC,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4FAAC,WAAU;;;;;;;;;;;;;;;;8FAG9B,6LAAC,+IAAA,CAAA,sBAAmB;oFAAC,OAAM;;sGACzB,6LAAC,+IAAA,CAAA,mBAAgB;4FAAC,SAAS,IAAM,oBAAoB;;8GACnD,6LAAC,8MAAA,CAAA,OAAI;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;sGAGnC,6LAAC,+IAAA,CAAA,mBAAgB;4FACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;4FAC/C,WAAU;;8GAEV,6LAAC,6MAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;;;;;;;;;;;;;;;;;;;+DA1C9B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA2D1C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAiB,cAAc;0BAC3C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,kBAAkB,gBAAgB;;;;;;8CAErC,6LAAC,qIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAC1B,kBAAkB,uBAAuB;;;;;;;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAc;;;;;;sDACvD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,iBAAiB,OAAO;4CAC/B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAClF,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAc;;;;;;sDACvD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,iBAAiB,IAAI;4CAC5B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC/E,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAuB,WAAU;sDAAc;;;;;;sDAC9D,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,iBAAiB,WAAW;4CACnC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACtF,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAc;;;;;;sDACvD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,iBAAiB,IAAI;4CAC5B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC/E,aAAY;;;;;;;;;;;;8CAGhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS,iBAAiB,SAAS;4CACnC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,WAAW,EAAE,MAAM,CAAC,OAAO;oDAAC,CAAC;;;;;;sDAExF,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAc;;;;;;;;;;;;8CAE3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;sDAAmB;;;;;;sDAGtD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;sDACd,kBAAkB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAc;;;;;;8CACrC,6LAAC,qIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAc;;;;;;;;;;;;sCAI7C,6LAAC,6IAAA,CAAA,cAAW;4BACV,UAAU;4BACV,UAAU,IAAM,kBAAkB;;;;;;;;;;;;;;;;;0BAMxC,6LAAC,mJAAA,CAAA,oBAAiB;gBAChB,SAAS;gBACT,MAAM;gBACN,cAAc;gBACd,QAAQ;;;;;;0BAIV,6LAAC,gJAAA,CAAA,gBAAa;gBACZ,MAAM,cAAc,IAAI;gBACxB,cAAc,CAAC,OAAS,iBAAiB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAK,CAAC;gBACnE,OAAM;gBACN,aAAa,CAAC,4BAA4B,EAAE,cAAc,WAAW,CAAC,mCAAmC,CAAC;gBAC1G,aAAY;gBACZ,YAAW;gBACX,SAAQ;gBACR,WAAW;;;;;;0BAIb,6LAAC,oIAAA,CAAA,iBAAc;gBAAC,QAAQ,MAAM,MAAM;gBAAE,UAAU,MAAM,WAAW;;;;;;;;;;;;AAGvE;GAzkCwB;;QACI,kIAAA,CAAA,UAAO;QACnB,oIAAA,CAAA,WAAQ;;;KAFA", "debugId": null}}]}