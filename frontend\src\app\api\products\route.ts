import { NextRequest, NextResponse } from 'next/server'
import { ProductModel } from '@/lib/models/Product'
import { checkDatabaseHealth } from '@/lib/database'

// GET - جلب جميع المنتجات من PostgreSQL
export async function GET(request: NextRequest) {
  try {
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      return NextResponse.json(
        { error: 'قاعدة البيانات غير متاحة' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)

    // استخراج معاملات الفلترة
    const filters = {
      category: searchParams.get('category') || undefined,
      available: searchParams.get('available') === 'true' ? true :
                 searchParams.get('available') === 'false' ? false : undefined,
      published: searchParams.get('published') === 'true' ? true :
                 searchParams.get('published') === 'false' ? false : undefined,
      search: searchParams.get('search') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
      sortBy: (searchParams.get('sortBy') as any) || 'created_at',
      sortOrder: (searchParams.get('sortOrder') as 'ASC' | 'DESC') || 'DESC'
    }

    // جلب المنتجات من قاعدة البيانات
    const result = await ProductModel.getAll(filters)

    return NextResponse.json({
      products: result.products,
      total: result.total,
      page: Math.floor(filters.offset / filters.limit) + 1,
      totalPages: Math.ceil(result.total / filters.limit),
      filters: filters,
      source: 'postgresql'
    })
  } catch (error) {
    console.error('Error fetching products from PostgreSQL:', error)
    return NextResponse.json(
      { error: 'فشل في جلب المنتجات من قاعدة البيانات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء منتج جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // التحقق من البيانات المطلوبة
    if (!body.name || !body.price) {
      return NextResponse.json(
        { error: 'اسم المنتج والسعر مطلوبان' },
        { status: 400 }
      )
    }

    // إنشاء المنتج
    const product = await ProductModel.create({
      name: body.name,
      description: body.description || '',
      category_id: body.category_id,
      price: parseFloat(body.price),
      rental_price: body.rental_price ? parseFloat(body.rental_price) : undefined,
      colors: Array.isArray(body.colors) ? body.colors : [],
      sizes: Array.isArray(body.sizes) ? body.sizes : [],
      images: Array.isArray(body.images) ? body.images : [],
      stock_quantity: parseInt(body.stock_quantity) || 0,
      is_available: body.is_available ?? true,
      is_published: body.is_published ?? true,
      features: Array.isArray(body.features) ? body.features : [],
      specifications: body.specifications || {},
      rating: 0,
      reviews_count: 0
    })

    return NextResponse.json({
      message: 'تم إنشاء المنتج بنجاح',
      product: product
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { error: 'فشل في إنشاء المنتج' },
      { status: 500 }
    )
  }
}


