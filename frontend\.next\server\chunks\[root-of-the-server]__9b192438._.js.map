{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/database.ts"], "sourcesContent": ["import { Pool, PoolClient } from 'pg'\n\n// إعداد اتصال PostgreSQL\nconst pool = new Pool({\n  user: process.env.POSTGRES_USER || 'postgres',\n  host: process.env.POSTGRES_HOST || 'localhost',\n  database: process.env.POSTGRES_DB || 'graduation_platform',\n  password: process.env.POSTGRES_PASSWORD || 'password',\n  port: parseInt(process.env.POSTGRES_PORT || '5432'),\n  max: 20, // الحد الأقصى للاتصالات\n  idleTimeoutMillis: 30000, // مهلة الخمول\n  connectionTimeoutMillis: 2000, // مهلة الاتصال\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false\n})\n\n// دالة للحصول على اتصال من المجموعة\nexport async function getConnection(): Promise<PoolClient> {\n  try {\n    const client = await pool.connect()\n    return client\n  } catch (error) {\n    console.error('خطأ في الاتصال بقاعدة البيانات:', error)\n    throw new Error('فشل في الاتصال بقاعدة البيانات')\n  }\n}\n\n// دالة لتنفيذ استعلام مع معاملات\nexport async function query(text: string, params?: any[]): Promise<any> {\n  const client = await getConnection()\n  try {\n    const start = Date.now()\n    const result = await client.query(text, params)\n    const duration = Date.now() - start\n    \n    // تسجيل الاستعلامات في وضع التطوير\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Executed query:', { text, duration, rows: result.rowCount })\n    }\n    \n    return result\n  } catch (error) {\n    console.error('خطأ في تنفيذ الاستعلام:', error)\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n// دالة لتنفيذ معاملة (transaction)\nexport async function transaction<T>(\n  callback: (client: PoolClient) => Promise<T>\n): Promise<T> {\n  const client = await getConnection()\n  try {\n    await client.query('BEGIN')\n    const result = await callback(client)\n    await client.query('COMMIT')\n    return result\n  } catch (error) {\n    await client.query('ROLLBACK')\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n// دالة لإغلاق مجموعة الاتصالات\nexport async function closePool(): Promise<void> {\n  await pool.end()\n}\n\n// دالة للتحقق من حالة قاعدة البيانات\nexport async function checkDatabaseHealth(): Promise<boolean> {\n  try {\n    const result = await query('SELECT NOW() as current_time')\n    return result.rows.length > 0\n  } catch (error) {\n    console.error('فشل في فحص حالة قاعدة البيانات:', error)\n    return false\n  }\n}\n\n// دالة لإنشاء الجداول الأساسية\nexport async function initializeDatabase(): Promise<void> {\n  try {\n    console.log('بدء تهيئة قاعدة البيانات...')\n    \n    // إنشاء جدول المستخدمين\n    await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        first_name VARCHAR(100) NOT NULL,\n        last_name VARCHAR(100) NOT NULL,\n        phone VARCHAR(20),\n        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),\n        is_active BOOLEAN DEFAULT true,\n        email_verified BOOLEAN DEFAULT false,\n        profile_image TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول الفئات\n    await query(`\n      CREATE TABLE IF NOT EXISTS categories (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        name_ar VARCHAR(100) NOT NULL,\n        name_en VARCHAR(100),\n        name_fr VARCHAR(100),\n        slug VARCHAR(100) UNIQUE NOT NULL,\n        description TEXT,\n        icon VARCHAR(50),\n        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,\n        order_index INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول المنتجات\n    await query(`\n      CREATE TABLE IF NOT EXISTS products (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        name VARCHAR(255) NOT NULL,\n        description TEXT,\n        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,\n        price DECIMAL(10,2) NOT NULL,\n        rental_price DECIMAL(10,2),\n        colors TEXT[] DEFAULT '{}',\n        sizes TEXT[] DEFAULT '{}',\n        images TEXT[] DEFAULT '{}',\n        stock_quantity INTEGER DEFAULT 0,\n        is_available BOOLEAN DEFAULT true,\n        is_published BOOLEAN DEFAULT true,\n        features TEXT[] DEFAULT '{}',\n        specifications JSONB DEFAULT '{}',\n        rating DECIMAL(3,2) DEFAULT 0,\n        reviews_count INTEGER DEFAULT 0,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول المدارس\n    await query(`\n      CREATE TABLE IF NOT EXISTS schools (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        name VARCHAR(255) NOT NULL,\n        name_en VARCHAR(255),\n        name_fr VARCHAR(255),\n        address TEXT,\n        city VARCHAR(100),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        website VARCHAR(255),\n        logo_url TEXT,\n        graduation_date DATE,\n        student_count INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        settings JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول الطلبات\n    await query(`\n      CREATE TABLE IF NOT EXISTS orders (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,\n        order_number VARCHAR(50) UNIQUE NOT NULL,\n        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),\n        total_amount DECIMAL(10,2) NOT NULL,\n        shipping_amount DECIMAL(10,2) DEFAULT 0,\n        tax_amount DECIMAL(10,2) DEFAULT 0,\n        discount_amount DECIMAL(10,2) DEFAULT 0,\n        payment_method VARCHAR(50),\n        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),\n        shipping_address JSONB,\n        billing_address JSONB,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول عناصر الطلب\n    await query(`\n      CREATE TABLE IF NOT EXISTS order_items (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,\n        product_id UUID REFERENCES products(id) ON DELETE CASCADE,\n        quantity INTEGER NOT NULL,\n        unit_price DECIMAL(10,2) NOT NULL,\n        total_price DECIMAL(10,2) NOT NULL,\n        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),\n        size VARCHAR(50),\n        color VARCHAR(50),\n        customizations JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول التقييمات\n    await query(`\n      CREATE TABLE IF NOT EXISTS reviews (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        product_id UUID REFERENCES products(id) ON DELETE CASCADE,\n        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,\n        rating INTEGER CHECK (rating >= 1 AND rating <= 5),\n        comment TEXT,\n        images TEXT[] DEFAULT '{}',\n        is_verified BOOLEAN DEFAULT false,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, product_id, order_id)\n      )\n    `)\n\n    // إنشاء جدول عناصر القائمة\n    await query(`\n      CREATE TABLE IF NOT EXISTS menu_items (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        title_ar VARCHAR(100) NOT NULL,\n        title_en VARCHAR(100),\n        title_fr VARCHAR(100),\n        slug VARCHAR(100) NOT NULL,\n        icon VARCHAR(50),\n        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,\n        order_index INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),\n        target_value VARCHAR(255),\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء الفهارس لتحسين الأداء\n    await query('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)')\n    await query('CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)')\n    await query('CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)')\n    await query('CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')\n    await query('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)')\n\n    console.log('تم تهيئة قاعدة البيانات بنجاح!')\n  } catch (error) {\n    console.error('خطأ في تهيئة قاعدة البيانات:', error)\n    throw error\n  }\n}\n\nexport default pool\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;AAEA,yBAAyB;AACzB,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,UAAU,QAAQ,GAAG,CAAC,WAAW,IAAI;IACrC,UAAU,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAC3C,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC5C,KAAK;IACL,mBAAmB;IACnB,yBAAyB;IACzB,KAAK,6EAAwE;AAC/E;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,MAAM,IAAY,EAAE,MAAc;IACtD,MAAM,SAAS,MAAM;IACrB,IAAI;QACF,MAAM,QAAQ,KAAK,GAAG;QACtB,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,MAAM;QACxC,MAAM,WAAW,KAAK,GAAG,KAAK;QAE9B,mCAAmC;QACnC,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,mBAAmB;gBAAE;gBAAM;gBAAU,MAAM,OAAO,QAAQ;YAAC;QACzE;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAGO,eAAe,YACpB,QAA4C;IAE5C,MAAM,SAAS,MAAM;IACrB,IAAI;QACF,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM,SAAS,MAAM,SAAS;QAC9B,MAAM,OAAO,KAAK,CAAC;QACnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAGO,eAAe;IACpB,MAAM,KAAK,GAAG;AAChB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,MAAM;QAC3B,OAAO,OAAO,IAAI,CAAC,MAAM,GAAG;IAC9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,wBAAwB;QACxB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;IAeb,CAAC;QAED,oBAAoB;QACpB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;IAeb,CAAC;QAED,sBAAsB;QACtB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;IAqBb,CAAC;QAED,qBAAqB;QACrB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;IAoBb,CAAC;QAED,qBAAqB;QACrB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;IAmBb,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;IAcb,CAAC;QAED,uBAAuB;QACvB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;IAcb,CAAC;QAED,2BAA2B;QAC3B,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;IAgBb,CAAC;QAED,8BAA8B;QAC9B,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QAEZ,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/database/init/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { initializeDatabase, checkDatabaseHealth } from '@/lib/database'\n\n// POST - تهيئة قاعدة البيانات\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('بدء تهيئة قاعدة البيانات...')\n    \n    // تهيئة قاعدة البيانات\n    await initializeDatabase()\n    \n    // التحقق من حالة قاعدة البيانات\n    const isHealthy = await checkDatabaseHealth()\n    \n    if (isHealthy) {\n      return NextResponse.json({\n        message: 'تم تهيئة قاعدة البيانات بنجاح',\n        status: 'success',\n        timestamp: new Date().toISOString()\n      })\n    } else {\n      return NextResponse.json({\n        error: 'فشل في التحقق من حالة قاعدة البيانات بعد التهيئة',\n        status: 'error'\n      }, { status: 500 })\n    }\n  } catch (error) {\n    console.error('Error initializing database:', error)\n    return NextResponse.json({\n      error: 'فشل في تهيئة قاعدة البيانات',\n      details: error instanceof Error ? error.message : 'خطأ غير معروف',\n      status: 'error'\n    }, { status: 500 })\n  }\n}\n\n// GET - التحقق من حالة قاعدة البيانات\nexport async function GET(request: NextRequest) {\n  try {\n    const isHealthy = await checkDatabaseHealth()\n    \n    if (isHealthy) {\n      return NextResponse.json({\n        status: 'healthy',\n        message: 'قاعدة البيانات تعمل بشكل طبيعي',\n        timestamp: new Date().toISOString()\n      })\n    } else {\n      return NextResponse.json({\n        status: 'unhealthy',\n        message: 'قاعدة البيانات غير متاحة',\n        timestamp: new Date().toISOString()\n      }, { status: 503 })\n    }\n  } catch (error) {\n    console.error('Error checking database health:', error)\n    return NextResponse.json({\n      status: 'error',\n      message: 'فشل في فحص حالة قاعدة البيانات',\n      details: error instanceof Error ? error.message : 'خطأ غير معروف',\n      timestamp: new Date().toISOString()\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,uBAAuB;QACvB,MAAM,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;QAEvB,gCAAgC;QAChC,MAAM,YAAY,MAAM,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD;QAE1C,IAAI,WAAW;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,QAAQ;YACV,GAAG;gBAAE,QAAQ;YAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,QAAQ;QACV,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,YAAY,MAAM,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD;QAE1C,IAAI,WAAW;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR,SAAS;gBACT,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;gBACR,SAAS;gBACT,WAAW,IAAI,OAAO,WAAW;YACnC,GAAG;gBAAE,QAAQ;YAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,WAAW,IAAI,OAAO,WAAW;QACnC,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}