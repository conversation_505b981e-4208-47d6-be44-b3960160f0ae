import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockProduct } from '@/lib/mockData'

// GET - جلب منتج واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const products = MockDataManager.getProducts()
    const product = products.find(p => p.id === params.id)

    if (!product) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({ product })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث منتج
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      name,
      description,
      category,
      price,
      rental_price,
      colors,
      sizes,
      images,
      stock_quantity,
      is_available,
      is_published,
      features,
      specifications
    } = body

    // جلب المنتجات الحالية
    const products = MockDataManager.getProducts()
    const productIndex = products.findIndex(p => p.id === params.id)

    if (productIndex === -1) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      )
    }

    // تحديث المنتج
    const updatedProduct: MockProduct = {
      ...products[productIndex],
      name: name || products[productIndex].name,
      description: description || products[productIndex].description,
      category: category || products[productIndex].category,
      price: price !== undefined ? parseFloat(price) : products[productIndex].price,
      rental_price: rental_price !== undefined ? (rental_price ? parseFloat(rental_price) : undefined) : products[productIndex].rental_price,
      colors: colors || products[productIndex].colors,
      sizes: sizes || products[productIndex].sizes,
      images: images || products[productIndex].images,
      stock_quantity: stock_quantity !== undefined ? parseInt(stock_quantity) : products[productIndex].stock_quantity,
      is_available: is_available !== undefined ? is_available : products[productIndex].is_available,
      features: features || products[productIndex].features,
      specifications: specifications || products[productIndex].specifications,
      updated_at: new Date().toISOString()
    }

    // حفظ التحديثات
    products[productIndex] = updatedProduct
    MockDataManager.saveProducts(products)

    return NextResponse.json({ 
      message: 'تم تحديث المنتج بنجاح',
      product: updatedProduct 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف منتج
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // جلب المنتجات الحالية
    const products = MockDataManager.getProducts()
    const productIndex = products.findIndex(p => p.id === params.id)

    if (productIndex === -1) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      )
    }

    // حذف المنتج
    products.splice(productIndex, 1)
    MockDataManager.saveProducts(products)

    return NextResponse.json({ 
      message: 'تم حذف المنتج بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
