import { NextRequest, NextResponse } from 'next/server'
import { ProductModel } from '@/lib/models/Product'
import { checkDatabaseHealth } from '@/lib/database'

// GET - جلب منتج واحد بالمعرف
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      return NextResponse.json(
        { error: 'قاعدة البيانات غير متاحة' },
        { status: 503 }
      )
    }

    const { id } = params

    // التحقق من صحة المعرف
    if (!id) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      )
    }

    // جلب المنتج من قاعدة البيانات
    const product = await ProductModel.getById(id)

    if (!product) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      product: product,
      source: 'postgresql'
    })
  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json(
      { error: 'فشل في جلب المنتج' },
      { status: 500 }
    )
  }
}

// PUT - تحديث منتج موجود
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // التحقق من صحة المعرف
    if (!id) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المنتج
    const existingProduct = await ProductModel.getById(id)
    if (!existingProduct) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      )
    }

    // تحضير البيانات للتحديث
    const updateData: any = {}

    if (body.name !== undefined) updateData.name = body.name
    if (body.description !== undefined) updateData.description = body.description
    if (body.category_id !== undefined) updateData.category_id = body.category_id
    if (body.price !== undefined) updateData.price = parseFloat(body.price)
    if (body.rental_price !== undefined) updateData.rental_price = body.rental_price ? parseFloat(body.rental_price) : null
    if (body.colors !== undefined) updateData.colors = Array.isArray(body.colors) ? body.colors : []
    if (body.sizes !== undefined) updateData.sizes = Array.isArray(body.sizes) ? body.sizes : []
    if (body.images !== undefined) updateData.images = Array.isArray(body.images) ? body.images : []
    if (body.stock_quantity !== undefined) updateData.stock_quantity = parseInt(body.stock_quantity)
    if (body.is_available !== undefined) updateData.is_available = body.is_available
    if (body.is_published !== undefined) updateData.is_published = body.is_published
    if (body.features !== undefined) updateData.features = Array.isArray(body.features) ? body.features : []
    if (body.specifications !== undefined) updateData.specifications = body.specifications

    // تحديث المنتج
    const updatedProduct = await ProductModel.update(id, updateData)

    if (!updatedProduct) {
      return NextResponse.json(
        { error: 'فشل في تحديث المنتج' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'تم تحديث المنتج بنجاح',
      product: updatedProduct
    })
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { error: 'فشل في تحديث المنتج' },
      { status: 500 }
    )
  }
}

// DELETE - حذف منتج
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // التحقق من صحة المعرف
    if (!id) {
      return NextResponse.json(
        { error: 'معرف المنتج مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المنتج
    const existingProduct = await ProductModel.getById(id)
    if (!existingProduct) {
      return NextResponse.json(
        { error: 'المنتج غير موجود' },
        { status: 404 }
      )
    }

    // حذف المنتج
    const deleted = await ProductModel.delete(id)

    if (!deleted) {
      return NextResponse.json(
        { error: 'فشل في حذف المنتج' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'تم حذف المنتج بنجاح',
      deletedProductId: id
    })
  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      { error: 'فشل في حذف المنتج' },
      { status: 500 }
    )
  }
}
