{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/database.ts"], "sourcesContent": ["import { Pool, PoolClient } from 'pg'\n\n// إعداد اتصال PostgreSQL\nconst pool = new Pool({\n  user: process.env.POSTGRES_USER || 'postgres',\n  host: process.env.POSTGRES_HOST || 'localhost',\n  database: process.env.POSTGRES_DB || 'graduation_platform',\n  password: process.env.POSTGRES_PASSWORD || 'password',\n  port: parseInt(process.env.POSTGRES_PORT || '5432'),\n  max: 20, // الحد الأقصى للاتصالات\n  idleTimeoutMillis: 30000, // مهلة الخمول\n  connectionTimeoutMillis: 2000, // مهلة الاتصال\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false\n})\n\n// دالة للحصول على اتصال من المجموعة\nexport async function getConnection(): Promise<PoolClient> {\n  try {\n    const client = await pool.connect()\n    return client\n  } catch (error) {\n    console.error('خطأ في الاتصال بقاعدة البيانات:', error)\n    throw new Error('فشل في الاتصال بقاعدة البيانات')\n  }\n}\n\n// دالة لتنفيذ استعلام مع معاملات\nexport async function query(text: string, params?: any[]): Promise<any> {\n  const client = await getConnection()\n  try {\n    const start = Date.now()\n    const result = await client.query(text, params)\n    const duration = Date.now() - start\n    \n    // تسجيل الاستعلامات في وضع التطوير\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Executed query:', { text, duration, rows: result.rowCount })\n    }\n    \n    return result\n  } catch (error) {\n    console.error('خطأ في تنفيذ الاستعلام:', error)\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n// دالة لتنفيذ معاملة (transaction)\nexport async function transaction<T>(\n  callback: (client: PoolClient) => Promise<T>\n): Promise<T> {\n  const client = await getConnection()\n  try {\n    await client.query('BEGIN')\n    const result = await callback(client)\n    await client.query('COMMIT')\n    return result\n  } catch (error) {\n    await client.query('ROLLBACK')\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n// دالة لإغلاق مجموعة الاتصالات\nexport async function closePool(): Promise<void> {\n  await pool.end()\n}\n\n// دالة للتحقق من حالة قاعدة البيانات\nexport async function checkDatabaseHealth(): Promise<boolean> {\n  try {\n    const result = await query('SELECT NOW() as current_time')\n    return result.rows.length > 0\n  } catch (error) {\n    console.error('فشل في فحص حالة قاعدة البيانات:', error)\n    return false\n  }\n}\n\n// دالة لإنشاء الجداول الأساسية\nexport async function initializeDatabase(): Promise<void> {\n  try {\n    console.log('بدء تهيئة قاعدة البيانات...')\n    \n    // إنشاء جدول المستخدمين\n    await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        first_name VARCHAR(100) NOT NULL,\n        last_name VARCHAR(100) NOT NULL,\n        phone VARCHAR(20),\n        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),\n        is_active BOOLEAN DEFAULT true,\n        email_verified BOOLEAN DEFAULT false,\n        profile_image TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول الفئات\n    await query(`\n      CREATE TABLE IF NOT EXISTS categories (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        name_ar VARCHAR(100) NOT NULL,\n        name_en VARCHAR(100),\n        name_fr VARCHAR(100),\n        slug VARCHAR(100) UNIQUE NOT NULL,\n        description TEXT,\n        icon VARCHAR(50),\n        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,\n        order_index INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول المنتجات\n    await query(`\n      CREATE TABLE IF NOT EXISTS products (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        name VARCHAR(255) NOT NULL,\n        description TEXT,\n        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,\n        price DECIMAL(10,2) NOT NULL,\n        rental_price DECIMAL(10,2),\n        colors TEXT[] DEFAULT '{}',\n        sizes TEXT[] DEFAULT '{}',\n        images TEXT[] DEFAULT '{}',\n        stock_quantity INTEGER DEFAULT 0,\n        is_available BOOLEAN DEFAULT true,\n        is_published BOOLEAN DEFAULT true,\n        features TEXT[] DEFAULT '{}',\n        specifications JSONB DEFAULT '{}',\n        rating DECIMAL(3,2) DEFAULT 0,\n        reviews_count INTEGER DEFAULT 0,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول المدارس\n    await query(`\n      CREATE TABLE IF NOT EXISTS schools (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        name VARCHAR(255) NOT NULL,\n        name_en VARCHAR(255),\n        name_fr VARCHAR(255),\n        address TEXT,\n        city VARCHAR(100),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        website VARCHAR(255),\n        logo_url TEXT,\n        graduation_date DATE,\n        student_count INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        settings JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول الطلبات\n    await query(`\n      CREATE TABLE IF NOT EXISTS orders (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,\n        order_number VARCHAR(50) UNIQUE NOT NULL,\n        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),\n        total_amount DECIMAL(10,2) NOT NULL,\n        shipping_amount DECIMAL(10,2) DEFAULT 0,\n        tax_amount DECIMAL(10,2) DEFAULT 0,\n        discount_amount DECIMAL(10,2) DEFAULT 0,\n        payment_method VARCHAR(50),\n        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),\n        shipping_address JSONB,\n        billing_address JSONB,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول عناصر الطلب\n    await query(`\n      CREATE TABLE IF NOT EXISTS order_items (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,\n        product_id UUID REFERENCES products(id) ON DELETE CASCADE,\n        quantity INTEGER NOT NULL,\n        unit_price DECIMAL(10,2) NOT NULL,\n        total_price DECIMAL(10,2) NOT NULL,\n        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),\n        size VARCHAR(50),\n        color VARCHAR(50),\n        customizations JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء جدول التقييمات\n    await query(`\n      CREATE TABLE IF NOT EXISTS reviews (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        product_id UUID REFERENCES products(id) ON DELETE CASCADE,\n        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,\n        rating INTEGER CHECK (rating >= 1 AND rating <= 5),\n        comment TEXT,\n        images TEXT[] DEFAULT '{}',\n        is_verified BOOLEAN DEFAULT false,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, product_id, order_id)\n      )\n    `)\n\n    // إنشاء جدول عناصر القائمة\n    await query(`\n      CREATE TABLE IF NOT EXISTS menu_items (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        title_ar VARCHAR(100) NOT NULL,\n        title_en VARCHAR(100),\n        title_fr VARCHAR(100),\n        slug VARCHAR(100) NOT NULL,\n        icon VARCHAR(50),\n        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,\n        order_index INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),\n        target_value VARCHAR(255),\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `)\n\n    // إنشاء الفهارس لتحسين الأداء\n    await query('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)')\n    await query('CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)')\n    await query('CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)')\n    await query('CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)')\n    await query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')\n    await query('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)')\n\n    console.log('تم تهيئة قاعدة البيانات بنجاح!')\n  } catch (error) {\n    console.error('خطأ في تهيئة قاعدة البيانات:', error)\n    throw error\n  }\n}\n\nexport default pool\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;AAEA,yBAAyB;AACzB,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,UAAU,QAAQ,GAAG,CAAC,WAAW,IAAI;IACrC,UAAU,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAC3C,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC5C,KAAK;IACL,mBAAmB;IACnB,yBAAyB;IACzB,KAAK,6EAAwE;AAC/E;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,MAAM,IAAY,EAAE,MAAc;IACtD,MAAM,SAAS,MAAM;IACrB,IAAI;QACF,MAAM,QAAQ,KAAK,GAAG;QACtB,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,MAAM;QACxC,MAAM,WAAW,KAAK,GAAG,KAAK;QAE9B,mCAAmC;QACnC,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,mBAAmB;gBAAE;gBAAM;gBAAU,MAAM,OAAO,QAAQ;YAAC;QACzE;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAGO,eAAe,YACpB,QAA4C;IAE5C,MAAM,SAAS,MAAM;IACrB,IAAI;QACF,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM,SAAS,MAAM,SAAS;QAC9B,MAAM,OAAO,KAAK,CAAC;QACnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAGO,eAAe;IACpB,MAAM,KAAK,GAAG;AAChB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,MAAM,MAAM;QAC3B,OAAO,OAAO,IAAI,CAAC,MAAM,GAAG;IAC9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,wBAAwB;QACxB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;IAeb,CAAC;QAED,oBAAoB;QACpB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;IAeb,CAAC;QAED,sBAAsB;QACtB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;IAqBb,CAAC;QAED,qBAAqB;QACrB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;IAoBb,CAAC;QAED,qBAAqB;QACrB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;IAmBb,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;IAcb,CAAC;QAED,uBAAuB;QACvB,MAAM,MAAM,CAAC;;;;;;;;;;;;;;IAcb,CAAC;QAED,2BAA2B;QAC3B,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;IAgBb,CAAC;QAED,8BAA8B;QAC9B,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM;QAEZ,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/models/Product.ts"], "sourcesContent": ["import { query, transaction } from '../database'\nimport { PoolClient } from 'pg'\n\nexport interface Product {\n  id: string\n  name: string\n  description?: string\n  category_id?: string\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  is_published: boolean\n  features: string[]\n  specifications: Record<string, any>\n  rating: number\n  reviews_count: number\n  created_at: Date\n  updated_at: Date\n}\n\nexport interface ProductFilters {\n  category?: string\n  available?: boolean\n  published?: boolean\n  search?: string\n  minPrice?: number\n  maxPrice?: number\n  limit?: number\n  offset?: number\n  sortBy?: 'name' | 'price' | 'rating' | 'created_at'\n  sortOrder?: 'ASC' | 'DESC'\n}\n\nexport class ProductModel {\n  // جلب جميع المنتجات مع الفلترة\n  static async getAll(filters: ProductFilters = {}): Promise<{ products: Product[], total: number }> {\n    let whereConditions: string[] = []\n    let params: any[] = []\n    let paramIndex = 1\n\n    // بناء شروط WHERE\n    if (filters.category) {\n      whereConditions.push(`category_id = $${paramIndex}`)\n      params.push(filters.category)\n      paramIndex++\n    }\n\n    if (filters.available !== undefined) {\n      whereConditions.push(`is_available = $${paramIndex}`)\n      params.push(filters.available)\n      paramIndex++\n    }\n\n    if (filters.published !== undefined) {\n      whereConditions.push(`is_published = $${paramIndex}`)\n      params.push(filters.published)\n      paramIndex++\n    }\n\n    if (filters.search) {\n      whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`)\n      params.push(`%${filters.search}%`)\n      paramIndex++\n    }\n\n    if (filters.minPrice !== undefined) {\n      whereConditions.push(`price >= $${paramIndex}`)\n      params.push(filters.minPrice)\n      paramIndex++\n    }\n\n    if (filters.maxPrice !== undefined) {\n      whereConditions.push(`price <= $${paramIndex}`)\n      params.push(filters.maxPrice)\n      paramIndex++\n    }\n\n    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''\n\n    // ترتيب النتائج\n    const sortBy = filters.sortBy || 'created_at'\n    const sortOrder = filters.sortOrder || 'DESC'\n    const orderClause = `ORDER BY ${sortBy} ${sortOrder}`\n\n    // حد النتائج والإزاحة\n    const limit = filters.limit || 50\n    const offset = filters.offset || 0\n    const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`\n    params.push(limit, offset)\n\n    // استعلام العد الكلي\n    const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`\n    const countResult = await query(countQuery, params.slice(0, -2))\n    const total = parseInt(countResult.rows[0].total)\n\n    // استعلام البيانات\n    const dataQuery = `\n      SELECT \n        id, name, description, category_id, price, rental_price,\n        colors, sizes, images, stock_quantity, is_available, is_published,\n        features, specifications, rating, reviews_count, created_at, updated_at\n      FROM products \n      ${whereClause} \n      ${orderClause} \n      ${limitClause}\n    `\n    \n    const result = await query(dataQuery, params)\n    \n    return {\n      products: result.rows,\n      total\n    }\n  }\n\n  // جلب منتج واحد بالمعرف\n  static async getById(id: string): Promise<Product | null> {\n    const result = await query(\n      'SELECT * FROM products WHERE id = $1',\n      [id]\n    )\n    \n    return result.rows[0] || null\n  }\n\n  // إنشاء منتج جديد\n  static async create(productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> {\n    const result = await query(`\n      INSERT INTO products (\n        name, description, category_id, price, rental_price,\n        colors, sizes, images, stock_quantity, is_available, is_published,\n        features, specifications, rating, reviews_count\n      ) VALUES (\n        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15\n      ) RETURNING *\n    `, [\n      productData.name,\n      productData.description,\n      productData.category_id,\n      productData.price,\n      productData.rental_price,\n      productData.colors,\n      productData.sizes,\n      productData.images,\n      productData.stock_quantity,\n      productData.is_available,\n      productData.is_published,\n      productData.features,\n      JSON.stringify(productData.specifications),\n      productData.rating || 0,\n      productData.reviews_count || 0\n    ])\n\n    return result.rows[0]\n  }\n\n  // تحديث منتج\n  static async update(id: string, updates: Partial<Product>): Promise<Product | null> {\n    const setClause: string[] = []\n    const params: any[] = []\n    let paramIndex = 1\n\n    // بناء جملة SET ديناميكياً\n    Object.entries(updates).forEach(([key, value]) => {\n      if (key !== 'id' && key !== 'created_at' && value !== undefined) {\n        if (key === 'specifications') {\n          setClause.push(`${key} = $${paramIndex}`)\n          params.push(JSON.stringify(value))\n        } else {\n          setClause.push(`${key} = $${paramIndex}`)\n          params.push(value)\n        }\n        paramIndex++\n      }\n    })\n\n    if (setClause.length === 0) {\n      throw new Error('لا توجد حقول للتحديث')\n    }\n\n    // إضافة updated_at\n    setClause.push(`updated_at = NOW()`)\n    params.push(id)\n\n    const result = await query(`\n      UPDATE products \n      SET ${setClause.join(', ')} \n      WHERE id = $${paramIndex} \n      RETURNING *\n    `, params)\n\n    return result.rows[0] || null\n  }\n\n  // حذف منتج\n  static async delete(id: string): Promise<boolean> {\n    const result = await query(\n      'DELETE FROM products WHERE id = $1',\n      [id]\n    )\n\n    return result.rowCount > 0\n  }\n\n  // تحديث تقييم المنتج\n  static async updateRating(productId: string): Promise<void> {\n    await query(`\n      UPDATE products \n      SET \n        rating = (\n          SELECT COALESCE(AVG(rating), 0) \n          FROM reviews \n          WHERE product_id = $1\n        ),\n        reviews_count = (\n          SELECT COUNT(*) \n          FROM reviews \n          WHERE product_id = $1\n        ),\n        updated_at = NOW()\n      WHERE id = $1\n    `, [productId])\n  }\n\n  // تحديث المخزون\n  static async updateStock(productId: string, quantity: number): Promise<boolean> {\n    return await transaction(async (client: PoolClient) => {\n      // التحقق من المخزون الحالي\n      const stockResult = await client.query(\n        'SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE',\n        [productId]\n      )\n\n      if (stockResult.rows.length === 0) {\n        throw new Error('المنتج غير موجود')\n      }\n\n      const currentStock = stockResult.rows[0].stock_quantity\n      const newStock = currentStock + quantity\n\n      if (newStock < 0) {\n        throw new Error('المخزون غير كافي')\n      }\n\n      // تحديث المخزون\n      const updateResult = await client.query(\n        'UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2',\n        [newStock, productId]\n      )\n\n      return updateResult.rowCount > 0\n    })\n  }\n\n  // البحث في المنتجات\n  static async search(searchTerm: string, limit: number = 20): Promise<Product[]> {\n    const result = await query(`\n      SELECT * FROM products \n      WHERE \n        is_published = true \n        AND is_available = true \n        AND (\n          name ILIKE $1 \n          OR description ILIKE $1 \n          OR $2 = ANY(features)\n        )\n      ORDER BY \n        CASE \n          WHEN name ILIKE $1 THEN 1\n          WHEN description ILIKE $1 THEN 2\n          ELSE 3\n        END,\n        rating DESC\n      LIMIT $3\n    `, [`%${searchTerm}%`, searchTerm, limit])\n\n    return result.rows\n  }\n\n  // جلب المنتجات الأكثر مبيعاً\n  static async getBestSellers(limit: number = 10): Promise<Product[]> {\n    const result = await query(`\n      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold\n      FROM products p\n      LEFT JOIN order_items oi ON p.id = oi.product_id\n      LEFT JOIN orders o ON oi.order_id = o.id\n      WHERE p.is_published = true AND p.is_available = true\n        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))\n      GROUP BY p.id\n      ORDER BY total_sold DESC, p.rating DESC\n      LIMIT $1\n    `, [limit])\n\n    return result.rows\n  }\n\n  // جلب المنتجات الجديدة\n  static async getNewProducts(limit: number = 10): Promise<Product[]> {\n    const result = await query(`\n      SELECT * FROM products\n      WHERE is_published = true AND is_available = true\n      ORDER BY created_at DESC\n      LIMIT $1\n    `, [limit])\n\n    return result.rows\n  }\n\n  // جلب المنتجات ذات التقييم العالي\n  static async getTopRated(limit: number = 10): Promise<Product[]> {\n    const result = await query(`\n      SELECT * FROM products\n      WHERE is_published = true AND is_available = true AND rating > 0\n      ORDER BY rating DESC, reviews_count DESC\n      LIMIT $1\n    `, [limit])\n\n    return result.rows\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAqCO,MAAM;IACX,+BAA+B;IAC/B,aAAa,OAAO,UAA0B,CAAC,CAAC,EAAmD;QACjG,IAAI,kBAA4B,EAAE;QAClC,IAAI,SAAgB,EAAE;QACtB,IAAI,aAAa;QAEjB,kBAAkB;QAClB,IAAI,QAAQ,QAAQ,EAAE;YACpB,gBAAgB,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY;YACnD,OAAO,IAAI,CAAC,QAAQ,QAAQ;YAC5B;QACF;QAEA,IAAI,QAAQ,SAAS,KAAK,WAAW;YACnC,gBAAgB,IAAI,CAAC,CAAC,gBAAgB,EAAE,YAAY;YACpD,OAAO,IAAI,CAAC,QAAQ,SAAS;YAC7B;QACF;QAEA,IAAI,QAAQ,SAAS,KAAK,WAAW;YACnC,gBAAgB,IAAI,CAAC,CAAC,gBAAgB,EAAE,YAAY;YACpD,OAAO,IAAI,CAAC,QAAQ,SAAS;YAC7B;QACF;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,gBAAgB,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,uBAAuB,EAAE,WAAW,CAAC,CAAC;YACtF,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACjC;QACF;QAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;YAClC,gBAAgB,IAAI,CAAC,CAAC,UAAU,EAAE,YAAY;YAC9C,OAAO,IAAI,CAAC,QAAQ,QAAQ;YAC5B;QACF;QAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;YAClC,gBAAgB,IAAI,CAAC,CAAC,UAAU,EAAE,YAAY;YAC9C,OAAO,IAAI,CAAC,QAAQ,QAAQ;YAC5B;QACF;QAEA,MAAM,cAAc,gBAAgB,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,gBAAgB,IAAI,CAAC,UAAU,GAAG;QAE5F,gBAAgB;QAChB,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,YAAY,QAAQ,SAAS,IAAI;QACvC,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,WAAW;QAErD,sBAAsB;QACtB,MAAM,QAAQ,QAAQ,KAAK,IAAI;QAC/B,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,cAAc,CAAC,OAAO,EAAE,WAAW,SAAS,EAAE,aAAa,GAAG;QACpE,OAAO,IAAI,CAAC,OAAO;QAEnB,qBAAqB;QACrB,MAAM,aAAa,CAAC,uCAAuC,EAAE,aAAa;QAC1E,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,YAAY,OAAO,KAAK,CAAC,GAAG,CAAC;QAC7D,MAAM,QAAQ,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK;QAEhD,mBAAmB;QACnB,MAAM,YAAY,CAAC;;;;;;MAMjB,EAAE,YAAY;MACd,EAAE,YAAY;MACd,EAAE,YAAY;IAChB,CAAC;QAED,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,WAAW;QAEtC,OAAO;YACL,UAAU,OAAO,IAAI;YACrB;QACF;IACF;IAEA,wBAAwB;IACxB,aAAa,QAAQ,EAAU,EAA2B;QACxD,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EACvB,wCACA;YAAC;SAAG;QAGN,OAAO,OAAO,IAAI,CAAC,EAAE,IAAI;IAC3B;IAEA,kBAAkB;IAClB,aAAa,OAAO,WAA8D,EAAoB;QACpG,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;;;;IAQ5B,CAAC,EAAE;YACD,YAAY,IAAI;YAChB,YAAY,WAAW;YACvB,YAAY,WAAW;YACvB,YAAY,KAAK;YACjB,YAAY,YAAY;YACxB,YAAY,MAAM;YAClB,YAAY,KAAK;YACjB,YAAY,MAAM;YAClB,YAAY,cAAc;YAC1B,YAAY,YAAY;YACxB,YAAY,YAAY;YACxB,YAAY,QAAQ;YACpB,KAAK,SAAS,CAAC,YAAY,cAAc;YACzC,YAAY,MAAM,IAAI;YACtB,YAAY,aAAa,IAAI;SAC9B;QAED,OAAO,OAAO,IAAI,CAAC,EAAE;IACvB;IAEA,aAAa;IACb,aAAa,OAAO,EAAU,EAAE,OAAyB,EAA2B;QAClF,MAAM,YAAsB,EAAE;QAC9B,MAAM,SAAgB,EAAE;QACxB,IAAI,aAAa;QAEjB,2BAA2B;QAC3B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,QAAQ,QAAQ,QAAQ,gBAAgB,UAAU,WAAW;gBAC/D,IAAI,QAAQ,kBAAkB;oBAC5B,UAAU,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,YAAY;oBACxC,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC;gBAC7B,OAAO;oBACL,UAAU,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,YAAY;oBACxC,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF;QAEA,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,mBAAmB;QACnB,UAAU,IAAI,CAAC,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI,CAAC;QAEZ,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;UAEtB,EAAE,UAAU,IAAI,CAAC,MAAM;kBACf,EAAE,WAAW;;IAE3B,CAAC,EAAE;QAEH,OAAO,OAAO,IAAI,CAAC,EAAE,IAAI;IAC3B;IAEA,WAAW;IACX,aAAa,OAAO,EAAU,EAAoB;QAChD,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EACvB,sCACA;YAAC;SAAG;QAGN,OAAO,OAAO,QAAQ,GAAG;IAC3B;IAEA,qBAAqB;IACrB,aAAa,aAAa,SAAiB,EAAiB;QAC1D,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;;;;;;;;;;;IAeb,CAAC,EAAE;YAAC;SAAU;IAChB;IAEA,gBAAgB;IAChB,aAAa,YAAY,SAAiB,EAAE,QAAgB,EAAoB;QAC9E,OAAO,MAAM,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YAC9B,2BAA2B;YAC3B,MAAM,cAAc,MAAM,OAAO,KAAK,CACpC,gEACA;gBAAC;aAAU;YAGb,IAAI,YAAY,IAAI,CAAC,MAAM,KAAK,GAAG;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,eAAe,YAAY,IAAI,CAAC,EAAE,CAAC,cAAc;YACvD,MAAM,WAAW,eAAe;YAEhC,IAAI,WAAW,GAAG;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAChB,MAAM,eAAe,MAAM,OAAO,KAAK,CACrC,6EACA;gBAAC;gBAAU;aAAU;YAGvB,OAAO,aAAa,QAAQ,GAAG;QACjC;IACF;IAEA,oBAAoB;IACpB,aAAa,OAAO,UAAkB,EAAE,QAAgB,EAAE,EAAsB;QAC9E,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;;;;;;;;;;;;;;IAkB5B,CAAC,EAAE;YAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YAAE;YAAY;SAAM;QAEzC,OAAO,OAAO,IAAI;IACpB;IAEA,6BAA6B;IAC7B,aAAa,eAAe,QAAgB,EAAE,EAAsB;QAClE,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;;;;;;IAU5B,CAAC,EAAE;YAAC;SAAM;QAEV,OAAO,OAAO,IAAI;IACpB;IAEA,uBAAuB;IACvB,aAAa,eAAe,QAAgB,EAAE,EAAsB;QAClE,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;IAK5B,CAAC,EAAE;YAAC;SAAM;QAEV,OAAO,OAAO,IAAI;IACpB;IAEA,kCAAkC;IAClC,aAAa,YAAY,QAAgB,EAAE,EAAsB;QAC/D,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;IAK5B,CAAC,EAAE;YAAC;SAAM;QAEV,OAAO,OAAO,IAAI;IACpB;AACF", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { ProductModel } from '@/lib/models/Product'\nimport { checkDatabaseHealth } from '@/lib/database'\n\n// GET - جلب جميع المنتجات من PostgreSQL\nexport async function GET(request: NextRequest) {\n  try {\n    // التحقق من حالة قاعدة البيانات\n    const isHealthy = await checkDatabaseHealth()\n    if (!isHealthy) {\n      return NextResponse.json(\n        { error: 'قاعدة البيانات غير متاحة' },\n        { status: 503 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n\n    // استخراج معاملات الفلترة\n    const filters = {\n      category: searchParams.get('category') || undefined,\n      available: searchParams.get('available') === 'true' ? true :\n                 searchParams.get('available') === 'false' ? false : undefined,\n      published: searchParams.get('published') === 'true' ? true :\n                 searchParams.get('published') === 'false' ? false : undefined,\n      search: searchParams.get('search') || undefined,\n      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,\n      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,\n      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,\n      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,\n      sortBy: (searchParams.get('sortBy') as any) || 'created_at',\n      sortOrder: (searchParams.get('sortOrder') as 'ASC' | 'DESC') || 'DESC'\n    }\n\n    // جلب المنتجات من قاعدة البيانات\n    const result = await ProductModel.getAll(filters)\n\n    return NextResponse.json({\n      products: result.products,\n      total: result.total,\n      page: Math.floor(filters.offset / filters.limit) + 1,\n      totalPages: Math.ceil(result.total / filters.limit),\n      filters: filters,\n      source: 'postgresql'\n    })\n  } catch (error) {\n    console.error('Error fetching products from PostgreSQL:', error)\n    return NextResponse.json(\n      { error: 'فشل في جلب المنتجات من قاعدة البيانات' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إنشاء منتج جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n\n    // التحقق من البيانات المطلوبة\n    if (!body.name || !body.price) {\n      return NextResponse.json(\n        { error: 'اسم المنتج والسعر مطلوبان' },\n        { status: 400 }\n      )\n    }\n\n    // إنشاء المنتج\n    const product = await ProductModel.create({\n      name: body.name,\n      description: body.description || '',\n      category_id: body.category_id,\n      price: parseFloat(body.price),\n      rental_price: body.rental_price ? parseFloat(body.rental_price) : undefined,\n      colors: Array.isArray(body.colors) ? body.colors : [],\n      sizes: Array.isArray(body.sizes) ? body.sizes : [],\n      images: Array.isArray(body.images) ? body.images : [],\n      stock_quantity: parseInt(body.stock_quantity) || 0,\n      is_available: body.is_available ?? true,\n      is_published: body.is_published ?? true,\n      features: Array.isArray(body.features) ? body.features : [],\n      specifications: body.specifications || {},\n      rating: 0,\n      reviews_count: 0\n    })\n\n    return NextResponse.json({\n      message: 'تم إنشاء المنتج بنجاح',\n      product: product\n    }, { status: 201 })\n  } catch (error) {\n    console.error('Error creating product:', error)\n    return NextResponse.json(\n      { error: 'فشل في إنشاء المنتج' },\n      { status: 500 }\n    )\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,gCAAgC;QAChC,MAAM,YAAY,MAAM,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD;QAC1C,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,0BAA0B;QAC1B,MAAM,UAAU;YACd,UAAU,aAAa,GAAG,CAAC,eAAe;YAC1C,WAAW,aAAa,GAAG,CAAC,iBAAiB,SAAS,OAC3C,aAAa,GAAG,CAAC,iBAAiB,UAAU,QAAQ;YAC/D,WAAW,aAAa,GAAG,CAAC,iBAAiB,SAAS,OAC3C,aAAa,GAAG,CAAC,iBAAiB,UAAU,QAAQ;YAC/D,QAAQ,aAAa,GAAG,CAAC,aAAa;YACtC,UAAU,aAAa,GAAG,CAAC,cAAc,WAAW,aAAa,GAAG,CAAC,eAAgB;YACrF,UAAU,aAAa,GAAG,CAAC,cAAc,WAAW,aAAa,GAAG,CAAC,eAAgB;YACrF,OAAO,aAAa,GAAG,CAAC,WAAW,SAAS,aAAa,GAAG,CAAC,YAAa;YAC1E,QAAQ,aAAa,GAAG,CAAC,YAAY,SAAS,aAAa,GAAG,CAAC,aAAc;YAC7E,QAAQ,AAAC,aAAa,GAAG,CAAC,aAAqB;YAC/C,WAAW,AAAC,aAAa,GAAG,CAAC,gBAAmC;QAClE;QAEA,iCAAiC;QACjC,MAAM,SAAS,MAAM,iIAAA,CAAA,eAAY,CAAC,MAAM,CAAC;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,UAAU,OAAO,QAAQ;YACzB,OAAO,OAAO,KAAK;YACnB,MAAM,KAAK,KAAK,CAAC,QAAQ,MAAM,GAAG,QAAQ,KAAK,IAAI;YACnD,YAAY,KAAK,IAAI,CAAC,OAAO,KAAK,GAAG,QAAQ,KAAK;YAClD,SAAS;YACT,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwC,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,8BAA8B;QAC9B,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,UAAU,MAAM,iIAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YACxC,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW,IAAI;YACjC,aAAa,KAAK,WAAW;YAC7B,OAAO,WAAW,KAAK,KAAK;YAC5B,cAAc,KAAK,YAAY,GAAG,WAAW,KAAK,YAAY,IAAI;YAClE,QAAQ,MAAM,OAAO,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,EAAE;YACrD,OAAO,MAAM,OAAO,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,EAAE;YAClD,QAAQ,MAAM,OAAO,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,EAAE;YACrD,gBAAgB,SAAS,KAAK,cAAc,KAAK;YACjD,cAAc,KAAK,YAAY,IAAI;YACnC,cAAc,KAAK,YAAY,IAAI;YACnC,UAAU,MAAM,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,EAAE;YAC3D,gBAAgB,KAAK,cAAc,IAAI,CAAC;YACxC,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}