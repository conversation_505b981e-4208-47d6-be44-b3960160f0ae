{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { ProductModel } from '@/lib/models/Product'\nimport { checkDatabaseHealth } from '@/lib/database'\n\n// GET - جلب جميع المنتجات\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const category = searchParams.get('category')\n    const available = searchParams.get('available')\n    const published = searchParams.get('published')\n    const limit = searchParams.get('limit')\n    const offset = searchParams.get('offset')\n\n    // جلب البيانات الوهمية\n    let products = MockDataManager.getProducts()\n\n    // تطبيق الفلاتر\n    if (category && category !== 'all') {\n      products = products.filter(product => product.category === category)\n    }\n\n    if (available === 'true') {\n      products = products.filter(product => product.is_available === true)\n    } else if (available === 'false') {\n      products = products.filter(product => product.is_available === false)\n    }\n\n    // فلترة حسب حالة النشر\n    if (published === 'true') {\n      products = products.filter(product => product.is_published === true)\n    } else if (published === 'false') {\n      products = products.filter(product => product.is_published === false)\n    }\n\n    // ترتيب حسب تاريخ الإنشاء\n    products.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n    // تطبيق التصفح\n    const limitNum = limit ? parseInt(limit) : products.length\n    const offsetNum = offset ? parseInt(offset) : 0\n    \n    const paginatedProducts = products.slice(offsetNum, offsetNum + limitNum)\n\n    return NextResponse.json({ \n      products: paginatedProducts,\n      total: products.length\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة منتج جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      name,\n      description,\n      category,\n      price,\n      rental_price,\n      colors,\n      sizes,\n      images,\n      stock_quantity,\n      is_available,\n      is_published,\n      features,\n      specifications\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!name || !description || !category || !price) {\n      return NextResponse.json(\n        { error: 'البيانات المطلوبة مفقودة' },\n        { status: 400 }\n      )\n    }\n\n    // جلب المنتجات الحالية\n    const products = MockDataManager.getProducts()\n\n    // إنشاء المنتج الجديد\n    const newProduct: MockProduct = {\n      id: MockDataManager.generateId(),\n      name,\n      description,\n      category,\n      price: parseFloat(price),\n      rental_price: rental_price ? parseFloat(rental_price) : undefined,\n      colors: colors || [],\n      sizes: sizes || [],\n      images: images || [],\n      stock_quantity: parseInt(stock_quantity) || 0,\n      is_available: is_available ?? true,\n      is_published: is_published ?? true,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      features: features || [],\n      specifications: specifications || {}\n    }\n\n    // حفظ المنتج\n    products.push(newProduct)\n    MockDataManager.saveProducts(products)\n\n    return NextResponse.json({ \n      message: 'تم إضافة المنتج بنجاح',\n      product: newProduct \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAKO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,uBAAuB;QACvB,IAAI,WAAW,gBAAgB,WAAW;QAE1C,gBAAgB;QAChB,IAAI,YAAY,aAAa,OAAO;YAClC,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QAC7D;QAEA,IAAI,cAAc,QAAQ;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QACjE,OAAO,IAAI,cAAc,SAAS;YAChC,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QACjE;QAEA,uBAAuB;QACvB,IAAI,cAAc,QAAQ;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QACjE,OAAO,IAAI,cAAc,SAAS;YAChC,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QACjE;QAEA,0BAA0B;QAC1B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAEzF,eAAe;QACf,MAAM,WAAW,QAAQ,SAAS,SAAS,SAAS,MAAM;QAC1D,MAAM,YAAY,SAAS,SAAS,UAAU;QAE9C,MAAM,oBAAoB,SAAS,KAAK,CAAC,WAAW,YAAY;QAEhE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV,OAAO,SAAS,MAAM;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,MAAM,EACN,KAAK,EACL,MAAM,EACN,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,cAAc,EACf,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,WAAW,gBAAgB,WAAW;QAE5C,sBAAsB;QACtB,MAAM,aAA0B;YAC9B,IAAI,gBAAgB,UAAU;YAC9B;YACA;YACA;YACA,OAAO,WAAW;YAClB,cAAc,eAAe,WAAW,gBAAgB;YACxD,QAAQ,UAAU,EAAE;YACpB,OAAO,SAAS,EAAE;YAClB,QAAQ,UAAU,EAAE;YACpB,gBAAgB,SAAS,mBAAmB;YAC5C,cAAc,gBAAgB;YAC9B,cAAc,gBAAgB;YAC9B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,UAAU,YAAY,EAAE;YACxB,gBAAgB,kBAAkB,CAAC;QACrC;QAEA,aAAa;QACb,SAAS,IAAI,CAAC;QACd,gBAAgB,YAAY,CAAC;QAE7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}