{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "core.esm.js", "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndMonitor/context.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndMonitor/useDndMonitor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndMonitor/useDndMonitorProvider.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/Accessibility/defaults.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/Accessibility/Accessibility.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/store/actions.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/other/noop.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/useSensor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/useSensors.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/coordinates/constants.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/coordinates/distanceBetweenPoints.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/coordinates/getRelativeTransformOrigin.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/algorithms/helpers.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/algorithms/closestCenter.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/algorithms/closestCorners.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/algorithms/rectIntersection.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/algorithms/pointerWithin.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/rect/adjustScale.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/rect/getRectDelta.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/rect/rectAdjustment.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/transform/parseTransform.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/transform/inverseTransform.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/rect/getRect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/rect/getWindowClientRect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/isFixed.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/isScrollable.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollableAncestors.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollableElement.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollCoordinates.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/types/direction.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/documentScrollingElement.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollPosition.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollDirectionAndSpeed.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollElementRect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollOffsets.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/scroll/scrollIntoViewIfNeeded.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/rect/Rect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/utilities/Listeners.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/utilities/getEventListenerTarget.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/utilities/hasExceededDistance.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/events.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/keyboard/types.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/keyboard/defaults.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/keyboard/KeyboardSensor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/pointer/AbstractPointerSensor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/pointer/PointerSensor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/mouse/MouseSensor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/sensors/touch/TouchSensor.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useAutoScroller.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useCachedNode.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useCombineActivators.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useDroppableMeasuring.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useInitialValue.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useInitialRect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useMutationObserver.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useResizeObserver.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useRect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useRectDelta.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollableAncestors.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollOffsets.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollOffsetsDelta.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useSensorSetup.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useSyntheticListeners.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useWindowRect.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useRects.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/utilities/nodes/getMeasurableNode.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/utilities/useDragOverlayMeasuring.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndContext/defaults.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/store/constructors.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/store/context.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/store/reducer.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/Accessibility/components/RestoreFocus.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/modifiers/applyModifiers.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndContext/hooks/useMeasuringConfiguration.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DndContext/DndContext.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/useDraggable.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/useDndContext.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/hooks/useDroppable.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DragOverlay/hooks/useDropAnimation.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DragOverlay/hooks/useKey.ts", "file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/node_modules/%40dnd-kit/core/src/components/DragOverlay/DragOverlay.tsx"], "sourcesContent": ["import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n  Tab = 'Tab',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  cancel?: EventDescriptor;\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint =\n  | DelayConstraint\n  | DistanceConstraint\n  | (DelayConstraint & DistanceConstraint);\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is PointerActivationConstraint & DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  bypassActivationConstraint?(\n    props: Pick<AbstractPointerSensorProps, 'activeNode' | 'event' | 'options'>\n  ): boolean;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps =\n  SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint, bypassActivationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (\n        bypassActivationConstraint?.({\n          event: this.props.event,\n          activeNode: this.props.activeNode,\n          options: this.props.options,\n        })\n      ) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handlePending(\n    constraint: PointerActivationConstraint,\n    offset?: Coordinates | undefined\n  ): void {\n    const {active, onPending} = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    // Constraint validation\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onAbort, onEnd} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onAbort, onCancel} = this.props;\n\n    this.detach();\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'pointercancel'},\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  cancel: {name: 'touchcancel'},\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id == null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useState} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n\n  function measureRect() {\n    setRect((currentRect): ClientRect | null => {\n      if (!element) {\n        return null;\n      }\n  \n      if (element.isConnected === false) {\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return currentRect ?? fallbackRect ?? null;\n      }\n  \n      const newRect = measure(element);\n  \n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n  \n      return newRect;\n    });\n  }\n  \n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useState} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, setRects] = useState<ClientRect[]>(defaultValue);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue;\n      }\n\n      return elements.map((element) =>\n        isDocumentScrollingElement(element)\n          ? (windowRect as ClientRect)\n          : new Rect(measure(element), element)\n      );\n    });\n  }\n\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver?.disconnect();\n    measureRects();\n    elements.forEach((element) => resizeObserver?.observe(element));\n  }, [elements]);\n\n  return rects;\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n  DragPendingEvent,\n  DragAbortEvent,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragAbort?(event: DragAbortEvent): void;\n  onDragPending?(event: DragPendingEvent): void;\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const activeSensorRef = useRef<SensorInstance | null>(null);\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onAbort(id) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragAbort} = latestProps.current;\n          const event: DragAbortEvent = {id};\n          onDragAbort?.(event);\n          dispatchMonitorEvent({type: 'onDragAbort', event});\n        },\n        onPending(id, constraint, initialCoordinates, offset) {\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragPending} = latestProps.current;\n          const event: DragPendingEvent = {\n            id,\n            constraint,\n            initialCoordinates,\n            offset,\n          };\n\n          onDragPending?.(event);\n          dispatchMonitorEvent({type: 'onDragPending', event});\n        },\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            activatorEvent,\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n            setActiveSensor(activeSensorRef.current);\n            setActivatorEvent(activatorEvent);\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      activeSensorRef.current = sensorInstance;\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n            activeSensorRef.current = null;\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Draggable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {useLatestValue, useNodeRef, useUniqueId} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} =\n    useContext(InternalContext);\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n"], "names": ["DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "isPointWithinRect", "point", "pointer<PERSON><PERSON><PERSON>", "pointerCoordinates", "adjustScale", "transform", "rect1", "rect2", "scaleX", "scaleY", "getRectDelta", "createRectAdjustmentFn", "modifier", "adjustClientRect", "adjustments", "acc", "adjustment", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "removeAll", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "Tab", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "handleKeydown", "removeTextSelection", "activationConstraint", "bypassActivationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "handlePending", "clearTimeout", "offset", "onPending", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "onAbort", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "DraggableRect", "scrollContainerRef", "autoScroll", "sortedScrollableAncestors", "reverse", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "previousValue", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "setRect", "measureRect", "currentRect", "isConnected", "newRect", "records", "record", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "setRects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "reducer", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "RestoreFocus", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "measuring", "store", "useReducer", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "appliedTranslate", "activeSensorRef", "instantiateSensor", "sensorInstance", "onDragAbort", "onDragPending", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "Provider", "restoreFocus", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAMA,iBAAiB,GAAA,WAAA,GAAGC,0NAAa,AAAbA,EAAuC,IAA1B,CAAvC;SCCSC,cAAcC,QAAAA;IAC5B,MAAMC,gBAAgB,6MAAGC,aAAAA,AAAU,EAACL,iBAAD,CAAnC;IAEAM,sNAAAA,AAAS,EAAC;QACR,IAAI,CAACF,gBAAL,EAAuB;YACrB,MAAM,IAAIG,KAAJ,CACJ,8DADI,CAAN;;QAKF,MAAMC,WAAW,GAAGJ,gBAAgB,CAACD,QAAD,CAApC;QAEA,OAAOK,WAAP;KATO,EAUN;QAACL,QAAD;QAAWC,gBAAX;KAVM,CAAT;AAWD;SCfeK;IACd,MAAM,CAACC,SAAD,CAAA,6MAAcC,WAAAA,AAAQ,EAAC,IAAM,IAAIC,GAAJ,EAAP,CAA5B;IAEA,MAAMR,gBAAgB,6MAAGS,cAAAA,AAAW,GACjCV,QAAD;QACEO,SAAS,CAACI,GAAV,CAAcX,QAAd;QACA,OAAO,IAAMO,SAAS,CAACK,MAAV,CAAiBZ,QAAjB,CAAb;KAHgC,EAKlC;QAACO,SAAD;KALkC,CAApC;IAQA,MAAMM,QAAQ,OAAGH,oNAAAA,AAAW,GAC1B;YAAC,EAACI,IAAD,EAAOC,KAAAA;QACNR,SAAS,CAACS,OAAV,EAAmBhB,QAAD;YAAA,IAAA;YAAA,OAAA,CAAA,iBAAcA,QAAQ,CAACc,IAAD,CAAtB,KAAA,OAAA,KAAA,IAAc,eAAA,IAAA,CAAAd,QAAQ,EAASe,KAAT,CAAtB;SAAlB;KAFwB,EAI1B;QAACR,SAAD;KAJ0B,CAA5B;IAOA,OAAO;QAACM,QAAD;QAAWZ,gBAAX;KAAP;AACD;MCrBYgB,+BAA+B,GAA6B;IACvEC,SAAS,EAAA;AAD8D,CAAlE;AAQP,MAAaC,oBAAoB,GAAkB;IACjDC,WAAW,EAAA,IAAA;YAAC,EAACC,MAAAA;QACX,OAAA,8BAAmCA,MAAM,CAACC,EAA1C,GAAA;KAF+C;IAIjDC,UAAU,EAAA,KAAA;YAAC,EAACF,MAAD,EAASG,IAAAA;QAClB,IAAIA,IAAJ,EAAU;YACR,OAAA,oBAAyBH,MAAM,CAACC,EAAhC,GAAA,oCAAoEE,IAAI,CAACF,EAAzE,GAAA;;QAGF,OAAA,oBAAyBD,MAAM,CAACC,EAAhC,GAAA;KAT+C;IAWjDG,SAAS,EAAA,KAAA;YAAC,EAACJ,MAAD,EAASG,IAAAA;QACjB,IAAIA,IAAJ,EAAU;YACR,OAAA,oBAAyBH,MAAM,CAACC,EAAhC,GAAA,sCAAsEE,IAAI,CAACF,EAA3E;;QAGF,OAAA,oBAAyBD,MAAM,CAACC,EAAhC,GAAA;KAhB+C;IAkBjDI,YAAY,EAAA,KAAA;YAAC,EAACL,MAAAA;QACZ,OAAA,4CAAiDA,MAAM,CAACC,EAAxD,GAAA;;AAnB+C,CAA5C;SCUSK,cAAAA,IAAAA;QAAc,EAC5BC,aAAa,GAAGT,oBADY,EAE5BU,SAF4B,EAG5BC,uBAH4B,EAI5BC,wBAAwB,GAAGd,+BAAAA;IAE3B,MAAM,EAACe,QAAD,EAAWC,YAAAA,MAAgBC,mMAAAA,AAAe,EAAhD;IACA,MAAMC,YAAY,6KAAGC,cAAAA,AAAW,EAAA,gBAAhC;IACA,MAAM,CAACC,OAAD,EAAUC,UAAV,CAAA,IAAwB9B,oNAAAA,AAAQ,EAAC,KAAD,CAAtC;8MAEAL,YAAS,AAATA,EAAU;QACRmC,UAAU,CAAC,IAAD,CAAV;KADO,EAEN,EAFM,CAAT;IAIAvC,aAAa,2MACXwC,UAAAA,AAAO,EACL,IAAA,CAAO;YACLnB,WAAW,EAAA,KAAA;oBAAC,EAACC,MAAAA;gBACXW,QAAQ,CAACJ,aAAa,CAACR,WAAd,CAA0B;oBAACC;iBAA3B,CAAD,CAAR;aAFG;YAILmB,UAAU,EAAA,KAAA;oBAAC,EAACnB,MAAD,EAASG,IAAAA;gBAClB,IAAII,aAAa,CAACY,UAAlB,EAA8B;oBAC5BR,QAAQ,CAACJ,aAAa,CAACY,UAAd,CAAyB;wBAACnB,MAAD;wBAASG;qBAAlC,CAAD,CAAR;;aANC;YASLD,UAAU,EAAA,KAAA;oBAAC,EAACF,MAAD,EAASG,IAAAA;gBAClBQ,QAAQ,CAACJ,aAAa,CAACL,UAAd,CAAyB;oBAACF,MAAD;oBAASG;iBAAlC,CAAD,CAAR;aAVG;YAYLC,SAAS,EAAA,KAAA;oBAAC,EAACJ,MAAD,EAASG,IAAAA;gBACjBQ,QAAQ,CAACJ,aAAa,CAACH,SAAd,CAAwB;oBAACJ,MAAD;oBAASG;iBAAjC,CAAD,CAAR;aAbG;YAeLE,YAAY,EAAA,KAAA;oBAAC,EAACL,MAAD,EAASG,IAAAA;gBACpBQ,QAAQ,CAACJ,aAAa,CAACF,YAAd,CAA2B;oBAACL,MAAD;oBAASG;iBAApC,CAAD,CAAR;;SAhBJ,CADK,EAoBL;QAACQ,QAAD;QAAWJ,aAAX;KApBK,CADI,CAAb;IAyBA,IAAI,CAACS,OAAL,EAAc;QACZ,OAAO,IAAP;;IAGF,MAAMI,MAAM,GACVC,gNAAAA,CAAAA,aAAA,CAAA,qMAAA,CAAA,UAAA,CAAA,QAAA,EAAA,IAAA,wMACEA,UAAAA,CAAAA,aAAA,+KAACC,aAAD,EAAA;QACErB,EAAE,EAAEQ;QACJc,KAAK,EAAEb,wBAAwB,CAACb,SAAAA;KAFlC,CADF,wMAKEwB,UAAAA,CAAAA,aAAA,+KAACG,aAAD,EAAA;QAAYvB,EAAE,EAAEa;QAAcF,YAAY,EAAEA;KAA5C,CALF,CADF;IAUA,OAAOJ,SAAS,oNAAGiB,eAAAA,AAAY,EAACL,MAAD,EAASZ,SAAT,CAAf,GAAqCY,MAArD;AACD;ACvED,IAAYM,MAAZ;AAAA,CAAA,SAAYA,MAAAA;IACVA,MAAAA,CAAAA,YAAA,GAAA,WAAA;IACAA,MAAAA,CAAAA,WAAA,GAAA,UAAA;IACAA,MAAAA,CAAAA,UAAA,GAAA,SAAA;IACAA,MAAAA,CAAAA,aAAA,GAAA,YAAA;IACAA,MAAAA,CAAAA,WAAA,GAAA,UAAA;IACAA,MAAAA,CAAAA,oBAAA,GAAA,mBAAA;IACAA,MAAAA,CAAAA,uBAAA,GAAA,sBAAA;IACAA,MAAAA,CAAAA,sBAAA,GAAA,qBAAA;AACD,CATD,EAAYA,MAAM,IAAA,CAANA,MAAM,GAAA,CAAA,CAAA,CAAlB;SCHgBC,QAAAA;SCIAC,UACdC,MAAAA,EACAC,OAAAA;IAEA,iNAAOZ,UAAAA,AAAO,EACZ,IAAA,CAAO;YACLW,MADK;YAELC,OAAO,EAAEA,OAAF,IAAA,OAAEA,OAAF,GAAc,CAAA;SAFvB,CADY,EAMZ;QAACD,MAAD;QAASC,OAAT;KANY,CAAd;AAQD;SCZeC;qCACXC,UAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,OAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAEH,iNAAOd,UAAO,AAAPA,EACL,IACE,CAAC;eAAGc,OAAJ;SAAA,CAAaC,MAAb,CACGJ,MAAD,IAA6CA,MAAM,IAAI,IADzD,CAFU,EAMZ,CAAC;WAAGG,OAAJ;KANY,CAAd;AAQD;MCbYE,kBAAkB,GAAA,WAAA,GAAgBC,MAAM,CAACC,MAAP,CAAc;IAC3DC,CAAC,EAAE,CADwD;IAE3DC,CAAC,EAAE;AAFwD,CAAd,CAAxC;ACAP;;IAGA,SAAgBC,gBAAgBC,EAAAA,EAAiBC,EAAAA;IAC/C,OAAOC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACH,CAAH,GAAOI,EAAE,CAACJ,CAAnB,EAAsB,CAAtB,IAA2BK,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACF,CAAH,GAAOG,EAAE,CAACH,CAAnB,EAAsB,CAAtB,CAArC,CAAP;AACD;SCJeO,2BACdnD,KAAAA,EACAoD,IAAAA;IAEA,MAAMC,gBAAgB,6KAAGC,sBAAAA,AAAmB,EAACtD,KAAD,CAA5C;IAEA,IAAI,CAACqD,gBAAL,EAAuB;QACrB,OAAO,KAAP;;IAGF,MAAME,eAAe,GAAG;QACtBZ,CAAC,EAAG,CAACU,gBAAgB,CAACV,CAAjB,GAAqBS,IAAI,CAACI,IAA3B,IAAmCJ,IAAI,CAACK,KAAzC,GAAkD,GAD/B;QAEtBb,CAAC,EAAG,CAACS,gBAAgB,CAACT,CAAjB,GAAqBQ,IAAI,CAACM,GAA3B,IAAkCN,IAAI,CAACO,MAAxC,GAAkD;KAFvD;IAKA,OAAUJ,eAAe,CAACZ,CAA1B,GAAA,OAAgCY,eAAe,CAACX,CAAhD,GAAA;AACD;ACdD;;IAGA,SAAgBgB,kBAAAA,IAAAA,EAAAA,KAAAA;QACd,EAACC,IAAI,EAAE,EAAChC,KAAK,EAAEiC,CAAAA;QACf,EAACD,IAAI,EAAE,EAAChC,KAAK,EAAEkC,CAAAA;IAEf,OAAOD,CAAC,GAAGC,CAAX;AACD;AAED;;IAGA,SAAgBC,mBAAAA,KAAAA,EAAAA,KAAAA;QACd,EAACH,IAAI,EAAE,EAAChC,KAAK,EAAEiC,CAAAA;QACf,EAACD,IAAI,EAAE,EAAChC,KAAK,EAAEkC,CAAAA;IAEf,OAAOA,CAAC,GAAGD,CAAX;AACD;AAED;;;IAIA,SAAgBG,mBAAAA,KAAAA;QAAmB,EAACT,IAAD,EAAOE,GAAP,EAAYC,MAAZ,EAAoBF,KAAAA;IACrD,OAAO;QACL;YACEd,CAAC,EAAEa,IADL;YAEEZ,CAAC,EAAEc;SAHA;QAKL;YACEf,CAAC,EAAEa,IAAI,GAAGC,KADZ;YAEEb,CAAC,EAAEc;SAPA;QASL;YACEf,CAAC,EAAEa,IADL;YAEEZ,CAAC,EAAEc,GAAG,GAAGC;SAXN;QAaL;YACEhB,CAAC,EAAEa,IAAI,GAAGC,KADZ;YAEEb,CAAC,EAAEc,GAAG,GAAGC;SAfN;KAAP;AAkBD;AAaD,SAAgBO,kBACdC,UAAAA,EACAC,QAAAA;IAEA,IAAI,CAACD,UAAD,IAAeA,UAAU,CAACE,MAAX,KAAsB,CAAzC,EAA4C;QAC1C,OAAO,IAAP;;IAGF,MAAM,CAACC,cAAD,CAAA,GAAmBH,UAAzB;IAEA,OAAOC,QAAQ,GAAGE,cAAc,CAACF,QAAD,CAAjB,GAA8BE,cAA7C;AACD;AClED;;IAGA,SAASC,iBAAT,CACEnB,IADF,EAEEI,IAFF,EAGEE,GAHF;QAEEF,SAAAA,KAAAA,GAAAA;QAAAA,OAAOJ,IAAI,CAACI,IAAAA;;QACZE,QAAAA,KAAAA,GAAAA;QAAAA,MAAMN,IAAI,CAACM,GAAAA;;IAEX,OAAO;QACLf,CAAC,EAAEa,IAAI,GAAGJ,IAAI,CAACK,KAAL,GAAa,GADlB;QAELb,CAAC,EAAEc,GAAG,GAAGN,IAAI,CAACO,MAAL,GAAc;KAFzB;AAID;AAED;;;IAIA,MAAaa,aAAa,IAAuB;QAAC,EAChDC,aADgD,EAEhDC,cAFgD,EAGhDC,mBAAAA;IAEA,MAAMC,UAAU,GAAGL,iBAAiB,CAClCE,aADkC,EAElCA,aAAa,CAACjB,IAFoB,EAGlCiB,aAAa,CAACf,GAHoB,CAApC;IAKA,MAAMS,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAM2B,WAAW,GAAGlC,eAAe,CAAC0B,iBAAiB,CAACnB,IAAD,CAAlB,EAA0BwB,UAA1B,CAAnC;YAEAT,UAAU,CAACa,IAAX,CAAgB;gBAACzE,EAAD;gBAAKsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAEkD;;aAAvD;;;IAIJ,OAAOZ,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAxBM;ACnBP;;;IAIA,MAAasB,cAAc,IAAuB;QAAC,EACjDT,aADiD,EAEjDC,cAFiD,EAGjDC,mBAAAA;IAEA,MAAMQ,OAAO,GAAGlB,kBAAkB,CAACQ,aAAD,CAAlC;IACA,MAAMN,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAMgC,WAAW,GAAGnB,kBAAkB,CAACb,IAAD,CAAtC;YACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd,EAAsBC,KAAtB;gBAC/B,OAAOF,WAAW,GAAG1C,eAAe,CAACuC,WAAW,CAACK,KAAD,CAAZ,EAAqBD,MAArB,CAApC;aADgB,EAEf,CAFe,CAAlB;YAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;YAEAzB,UAAU,CAACa,IAAX,CAAgB;gBACdzE,EADc;gBAEdsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAE6D;;aAFpC;;;IAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CA3BM;ACJP;;IAGA,SAAgBiC,qBACdC,KAAAA,EACAC,MAAAA;IAEA,MAAMrC,GAAG,GAAGV,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACrC,GAAhB,EAAqBoC,KAAK,CAACpC,GAA3B,CAAZ;IACA,MAAMF,IAAI,GAAGR,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACvC,IAAhB,EAAsBsC,KAAK,CAACtC,IAA5B,CAAb;IACA,MAAMyC,KAAK,GAAGjD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACvC,IAAP,GAAcuC,MAAM,CAACtC,KAA9B,EAAqCqC,KAAK,CAACtC,IAAN,GAAasC,KAAK,CAACrC,KAAxD,CAAd;IACA,MAAM0C,MAAM,GAAGnD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACrC,GAAP,GAAaqC,MAAM,CAACpC,MAA7B,EAAqCmC,KAAK,CAACpC,GAAN,GAAYoC,KAAK,CAACnC,MAAvD,CAAf;IACA,MAAMF,KAAK,GAAGwC,KAAK,GAAGzC,IAAtB;IACA,MAAMG,MAAM,GAAGwC,MAAM,GAAGzC,GAAxB;IAEA,IAAIF,IAAI,GAAGyC,KAAP,IAAgBvC,GAAG,GAAGyC,MAA1B,EAAkC;QAChC,MAAMC,UAAU,GAAGL,MAAM,CAACtC,KAAP,GAAesC,MAAM,CAACpC,MAAzC;QACA,MAAM0C,SAAS,GAAGP,KAAK,CAACrC,KAAN,GAAcqC,KAAK,CAACnC,MAAtC;QACA,MAAM2C,gBAAgB,GAAG7C,KAAK,GAAGE,MAAjC;QACA,MAAM4C,iBAAiB,GACrBD,gBAAgB,GAAA,CAAIF,UAAU,GAAGC,SAAb,GAAyBC,gBAA7B,CADlB;QAGA,OAAOX,MAAM,CAACY,iBAAiB,CAACX,OAAlB,CAA0B,CAA1B,CAAD,CAAb;;IAIF,OAAO,CAAP;AACD;AAED;;;IAIA,MAAaY,gBAAgB,IAAuB;QAAC,EACnD/B,aADmD,EAEnDC,cAFmD,EAGnDC,mBAAAA;IAEA,MAAMR,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAMmD,iBAAiB,GAAGV,oBAAoB,CAACzC,IAAD,EAAOqB,aAAP,CAA9C;YAEA,IAAI8B,iBAAiB,GAAG,CAAxB,EAA2B;gBACzBpC,UAAU,CAACa,IAAX,CAAgB;oBACdzE,EADc;oBAEdsD,IAAI,EAAE;wBAACgB,kBAAD;wBAAqBhD,KAAK,EAAE0E;;iBAFpC;;;;IAQN,OAAOpC,UAAU,CAACc,IAAX,CAAgBjB,kBAAhB,CAAP;AACD,CAxBM;AC/BP;;IAGA,SAASyC,iBAAT,CAA2BC,KAA3B,EAA+CtD,IAA/C;IACE,MAAM,EAACM,GAAD,EAAMF,IAAN,EAAY2C,MAAZ,EAAoBF,KAAAA,KAAS7C,IAAnC;IAEA,OACEM,GAAG,IAAIgD,KAAK,CAAC9D,CAAb,IAAkB8D,KAAK,CAAC9D,CAAN,IAAWuD,MAA7B,IAAuC3C,IAAI,IAAIkD,KAAK,CAAC/D,CAArD,IAA0D+D,KAAK,CAAC/D,CAAN,IAAWsD,KADvE;AAGD;AAED;;IAGA,MAAaU,aAAa,IAAuB;QAAC,EAChDhC,mBADgD,EAEhDD,cAFgD,EAGhDkC,kBAAAA;IAEA,IAAI,CAACA,kBAAL,EAAyB;QACvB,OAAO,EAAP;;IAGF,MAAMzC,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAI,IAAIqD,iBAAiB,CAACG,kBAAD,EAAqBxD,IAArB,CAA7B,EAAyD;;;;;UAMvD,MAAM+B,OAAO,GAAGlB,kBAAkB,CAACb,IAAD,CAAlC;YACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd;gBAC/B,OAAOD,WAAW,GAAG1C,eAAe,CAAC+D,kBAAD,EAAqBpB,MAArB,CAApC;aADgB,EAEf,CAFe,CAAlB;YAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;YAEAzB,UAAU,CAACa,IAAX,CAAgB;gBACdzE,EADc;gBAEdsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAE6D;;aAFpC;;;IAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAnCM;SCjBSiD,YACdC,SAAAA,EACAC,KAAAA,EACAC,KAAAA;IAEA,OAAO;QACL,GAAGF,SADE;QAELG,MAAM,EAAEF,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACtD,KAAN,GAAcuD,KAAK,CAACvD,KAArC,GAA6C,CAFhD;QAGLyD,MAAM,EAAEH,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACpD,MAAN,GAAeqD,KAAK,CAACrD,MAAtC,GAA+C;KAHzD;AAKD;SCVewD,aACdJ,KAAAA,EACAC,KAAAA;IAEA,OAAOD,KAAK,IAAIC,KAAT,GACH;QACErE,CAAC,EAAEoE,KAAK,CAACvD,IAAN,GAAawD,KAAK,CAACxD,IADxB;QAEEZ,CAAC,EAAEmE,KAAK,CAACrD,GAAN,GAAYsD,KAAK,CAACtD,GAAAA;KAHpB,GAKHlB,kBALJ;AAMD;SCXe4E,uBAAuBC,QAAAA;IACrC,OAAO,SAASC,gBAAT,CACLlE,IADK;yCAEFmE,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,WAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QAEH,OAAOA,WAAW,CAACjC,MAAZ,CACL,CAACkC,GAAD,EAAMC,UAAN,GAAA,CAAsB;gBACpB,GAAGD,GADiB;gBAEpB9D,GAAG,EAAE8D,GAAG,CAAC9D,GAAJ,GAAU2D,QAAQ,GAAGI,UAAU,CAAC7E,CAFjB;gBAGpBuD,MAAM,EAAEqB,GAAG,CAACrB,MAAJ,GAAakB,QAAQ,GAAGI,UAAU,CAAC7E,CAHvB;gBAIpBY,IAAI,EAAEgE,GAAG,CAAChE,IAAJ,GAAW6D,QAAQ,GAAGI,UAAU,CAAC9E,CAJnB;gBAKpBsD,KAAK,EAAEuB,GAAG,CAACvB,KAAJ,GAAYoB,QAAQ,GAAGI,UAAU,CAAC9E,CAAAA;aAL3C,CADK,EAQL;YAAC,GAAGS,IAAAA;SARC,CAAP;KAJF;AAeD;AAEM,MAAMsE,eAAe,GAAA,WAAA,GAAGN,sBAAsB,CAAC,CAAD,CAA9C;SClBSO,eAAeb,SAAAA;IAC7B,IAAIA,SAAS,CAACc,UAAV,CAAqB,WAArB,CAAJ,EAAuC;QACrC,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;QAEA,OAAO;YACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,EAAD,CADb;YAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,EAAD,CAFb;YAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;YAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD,CAAA;SAJzB;KAHF,MASO,IAAIf,SAAS,CAACc,UAAV,CAAqB,SAArB,CAAJ,EAAqC;QAC1C,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;QAEA,OAAO;YACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,CAAD,CADb;YAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,CAAD,CAFb;YAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;YAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD,CAAA;SAJzB;;IAQF,OAAO,IAAP;AACD;SCpBeG,iBACd5E,IAAAA,EACA0D,SAAAA,EACAvD,eAAAA;IAEA,MAAM0E,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;IAEA,IAAI,CAACmB,eAAL,EAAsB;QACpB,OAAO7E,IAAP;;IAGF,MAAM,EAAC6D,MAAD,EAASC,MAAT,EAAiBvE,CAAC,EAAEuF,UAApB,EAAgCtF,CAAC,EAAEuF,UAAAA,KAAcF,eAAvD;IAEA,MAAMtF,CAAC,GAAGS,IAAI,CAACI,IAAL,GAAY0E,UAAZ,GAAyB,CAAC,IAAIjB,MAAL,IAAemB,UAAU,CAAC7E,eAAD,CAA5D;IACA,MAAMX,CAAC,GACLQ,IAAI,CAACM,GAAL,GACAyE,UADA,GAEA,CAAC,IAAIjB,MAAL,IACEkB,UAAU,CAAC7E,eAAe,CAACuE,KAAhB,CAAsBvE,eAAe,CAAC8E,OAAhB,CAAwB,GAAxB,IAA+B,CAArD,CAAD,CAJd;IAKA,MAAMC,CAAC,GAAGrB,MAAM,GAAG7D,IAAI,CAACK,KAAL,GAAawD,MAAhB,GAAyB7D,IAAI,CAACK,KAA9C;IACA,MAAM8E,CAAC,GAAGrB,MAAM,GAAG9D,IAAI,CAACO,MAAL,GAAcuD,MAAjB,GAA0B9D,IAAI,CAACO,MAA/C;IAEA,OAAO;QACLF,KAAK,EAAE6E,CADF;QAEL3E,MAAM,EAAE4E,CAFH;QAGL7E,GAAG,EAAEd,CAHA;QAILqD,KAAK,EAAEtD,CAAC,GAAG2F,CAJN;QAKLnC,MAAM,EAAEvD,CAAC,GAAG2F,CALP;QAML/E,IAAI,EAAEb;KANR;AAQD;ACzBD,MAAM6F,cAAc,GAAY;IAACC,eAAe,EAAE;AAAlB,CAAhC;AAEA;;IAGA,SAAgBC,cACdC,OAAAA,EACAvG,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAAmBoG;;IAEnB,IAAIpF,IAAI,GAAeuF,OAAO,CAACC,qBAAR,EAAvB;IAEA,IAAIxG,OAAO,CAACqG,eAAZ,EAA6B;QAC3B,MAAM,EAAC3B,SAAD,EAAYvD,eAAAA,SAChBsF,kLAAAA,AAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCH,OAApC,CADF;QAGA,IAAI7B,SAAJ,EAAe;YACb1D,IAAI,GAAG4E,gBAAgB,CAAC5E,IAAD,EAAO0D,SAAP,EAAkBvD,eAAlB,CAAvB;;;IAIJ,MAAM,EAACG,GAAD,EAAMF,IAAN,EAAYC,KAAZ,EAAmBE,MAAnB,EAA2BwC,MAA3B,EAAmCF,KAAAA,KAAS7C,IAAlD;IAEA,OAAO;QACLM,GADK;QAELF,IAFK;QAGLC,KAHK;QAILE,MAJK;QAKLwC,MALK;QAMLF;KANF;AAQD;AAED;;;;;;;IAQA,SAAgB8C,+BAA+BJ,OAAAA;IAC7C,OAAOD,aAAa,CAACC,OAAD,EAAU;QAACF,eAAe,EAAE;KAA5B,CAApB;AACD;SCjDeO,oBAAoBL,OAAAA;IAClC,MAAMlF,KAAK,GAAGkF,OAAO,CAACM,UAAtB;IACA,MAAMtF,MAAM,GAAGgF,OAAO,CAACO,WAAvB;IAEA,OAAO;QACLxF,GAAG,EAAE,CADA;QAELF,IAAI,EAAE,CAFD;QAGLyC,KAAK,EAAExC,KAHF;QAIL0C,MAAM,EAAExC,MAJH;QAKLF,KALK;QAMLE;KANF;AAQD;SCZewF,QACdC,IAAAA,EACAC,aAAAA;QAAAA,kBAAAA,KAAAA,GAAAA;QAAAA,0LAAqCR,YAAAA,AAAS,EAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC;;IAErC,OAAOC,aAAa,CAACC,QAAd,KAA2B,OAAlC;AACD;SCLeC,aACdZ,OAAAA,EACAU,aAAAA;QAAAA,kBAAAA,KAAAA,GAAAA;QAAAA,0LAAqCR,YAAAA,AAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CACnCH,OADmC;;IAIrC,MAAMa,aAAa,GAAG,uBAAtB;IACA,MAAMC,UAAU,GAAG;QAAC,UAAD;QAAa,WAAb;QAA0B,WAA1B;KAAnB;IAEA,OAAOA,UAAU,CAACC,IAAX,EAAiBtF,QAAD;QACrB,MAAMvC,KAAK,GAAGwH,aAAa,CAACjF,QAAD,CAA3B;QAEA,OAAO,OAAOvC,KAAP,KAAiB,QAAjB,GAA4B2H,aAAa,CAACG,IAAd,CAAmB9H,KAAnB,CAA5B,GAAwD,KAA/D;KAHK,CAAP;AAKD;SCNe+H,uBACdjB,OAAAA,EACAkB,KAAAA;IAEA,MAAMC,aAAa,GAAc,EAAjC;IAEA,SAASC,uBAAT,CAAiCX,IAAjC;QACE,IAAIS,KAAK,IAAI,IAAT,IAAiBC,aAAa,CAACzF,MAAd,IAAwBwF,KAA7C,EAAoD;YAClD,OAAOC,aAAP;;QAGF,IAAI,CAACV,IAAL,EAAW;YACT,OAAOU,aAAP;;QAGF,8KACEE,aAAAA,AAAU,EAACZ,IAAD,CAAV,IACAA,IAAI,CAACa,gBAAL,IAAyB,IADzB,IAEA,CAACH,aAAa,CAACI,QAAd,CAAuBd,IAAI,CAACa,gBAA5B,CAHH,EAIE;YACAH,aAAa,CAAC9E,IAAd,CAAmBoE,IAAI,CAACa,gBAAxB;YAEA,OAAOH,aAAP;;QAGF,IAAI,2KAACK,gBAAAA,AAAa,EAACf,IAAD,CAAd,6KAAwBgB,gBAAAA,AAAY,EAAChB,IAAD,CAAxC,EAAgD;YAC9C,OAAOU,aAAP;;QAGF,IAAIA,aAAa,CAACI,QAAd,CAAuBd,IAAvB,CAAJ,EAAkC;YAChC,OAAOU,aAAP;;QAGF,MAAMT,aAAa,6KAAGR,YAAAA,AAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCM,IAApC,CAAtB;QAEA,IAAIA,IAAI,KAAKT,OAAb,EAAsB;YACpB,IAAIY,YAAY,CAACH,IAAD,EAAOC,aAAP,CAAhB,EAAuC;gBACrCS,aAAa,CAAC9E,IAAd,CAAmBoE,IAAnB;;;QAIJ,IAAID,OAAO,CAACC,IAAD,EAAOC,aAAP,CAAX,EAAkC;YAChC,OAAOS,aAAP;;QAGF,OAAOC,uBAAuB,CAACX,IAAI,CAACiB,UAAN,CAA9B;;IAGF,IAAI,CAAC1B,OAAL,EAAc;QACZ,OAAOmB,aAAP;;IAGF,OAAOC,uBAAuB,CAACpB,OAAD,CAA9B;AACD;AAED,SAAgB2B,2BAA2BlB,IAAAA;IACzC,MAAM,CAACmB,uBAAD,CAAA,GAA4BX,sBAAsB,CAACR,IAAD,EAAO,CAAP,CAAxD;IAEA,OAAOmB,uBAAP,IAAA,OAAOA,uBAAP,GAAkC,IAAlC;AACD;SC5DeC,qBAAqB7B,OAAAA;IACnC,IAAI,uKAAC8B,YAAD,IAAc,CAAC9B,OAAnB,EAA4B;QAC1B,OAAO,IAAP;;IAGF,6KAAI+B,YAAAA,AAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAP;;IAGF,IAAI,EAACgC,kLAAAA,AAAM,EAAChC,OAAD,CAAX,EAAsB;QACpB,OAAO,IAAP;;IAGF,8KACEqB,aAAAA,AAAU,EAACrB,OAAD,CAAV,IACAA,OAAO,KAAKiC,6LAAAA,AAAgB,EAACjC,OAAD,CAAhB,CAA0BsB,gBAFxC,EAGE;QACA,OAAOY,MAAP;;IAGF,8KAAIV,gBAAAA,AAAa,EAACxB,OAAD,CAAjB,EAA4B;QAC1B,OAAOA,OAAP;;IAGF,OAAO,IAAP;AACD;SC9BemC,qBAAqBnC,OAAAA;IACnC,8KAAI+B,WAAAA,AAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAO,CAACoC,OAAf;;IAGF,OAAOpC,OAAO,CAACqC,UAAf;AACD;AAED,SAAgBC,qBAAqBtC,OAAAA;IACnC,6KAAI+B,YAAAA,AAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAO,CAACuC,OAAf;;IAGF,OAAOvC,OAAO,CAACwC,SAAf;AACD;AAED,SAAgBC,qBACdzC,OAAAA;IAEA,OAAO;QACLhG,CAAC,EAAEmI,oBAAoB,CAACnC,OAAD,CADlB;QAEL/F,CAAC,EAAEqI,oBAAoB,CAACtC,OAAD;KAFzB;AAID;AC3BD,IAAY0C,SAAZ;AAAA,CAAA,SAAYA,SAAAA;IACVA,SAAAA,CAAAA,SAAAA,CAAAA,UAAAA,GAAAA,EAAA,GAAA,SAAA;IACAA,SAAAA,CAAAA,SAAAA,CAAAA,WAAAA,GAAAA,CAAAA,EAAA,GAAA,UAAA;AACD,CAHD,EAAYA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA,CAArB;SCEgBC,2BAA2B3C,OAAAA;IACzC,IAAI,CAAC8B,kLAAD,IAAc,CAAC9B,OAAnB,EAA4B;QAC1B,OAAO,KAAP;;IAGF,OAAOA,OAAO,KAAK4C,QAAQ,CAACtB,gBAA5B;AACD;SCNeuB,kBAAkBC,kBAAAA;IAChC,MAAMC,SAAS,GAAG;QAChB/I,CAAC,EAAE,CADa;QAEhBC,CAAC,EAAE;KAFL;IAIA,MAAM+I,UAAU,GAAGL,0BAA0B,CAACG,kBAAD,CAA1B,GACf;QACE9H,MAAM,EAAEkH,MAAM,CAAC3B,WADjB;QAEEzF,KAAK,EAAEoH,MAAM,CAAC5B,UAAAA;KAHD,GAKf;QACEtF,MAAM,EAAE8H,kBAAkB,CAACG,YAD7B;QAEEnI,KAAK,EAAEgI,kBAAkB,CAACI,WAAAA;KAPhC;IASA,MAAMC,SAAS,GAAG;QAChBnJ,CAAC,EAAE8I,kBAAkB,CAACM,WAAnB,GAAiCJ,UAAU,CAAClI,KAD/B;QAEhBb,CAAC,EAAE6I,kBAAkB,CAACO,YAAnB,GAAkCL,UAAU,CAAChI,MAAAA;KAFlD;IAKA,MAAMsI,KAAK,GAAGR,kBAAkB,CAACN,SAAnB,IAAgCO,SAAS,CAAC9I,CAAxD;IACA,MAAMsJ,MAAM,GAAGT,kBAAkB,CAACT,UAAnB,IAAiCU,SAAS,CAAC/I,CAA1D;IACA,MAAMwJ,QAAQ,GAAGV,kBAAkB,CAACN,SAAnB,IAAgCW,SAAS,CAAClJ,CAA3D;IACA,MAAMwJ,OAAO,GAAGX,kBAAkB,CAACT,UAAnB,IAAiCc,SAAS,CAACnJ,CAA3D;IAEA,OAAO;QACLsJ,KADK;QAELC,MAFK;QAGLC,QAHK;QAILC,OAJK;QAKLN,SALK;QAMLJ;KANF;AAQD;AC5BD,MAAMW,gBAAgB,GAAG;IACvB1J,CAAC,EAAE,GADoB;IAEvBC,CAAC,EAAE;AAFoB,CAAzB;AAKA,SAAgB0J,2BACdC,eAAAA,EACAC,mBAAAA,EAAAA,IAAAA,EAEAC,YAAAA,EACAC,mBAAAA;QAFA,EAAChJ,GAAD,EAAMF,IAAN,EAAYyC,KAAZ,EAAmBE,MAAAA;QACnBsG,iBAAAA,KAAAA,GAAAA;QAAAA,eAAe;;QACfC,wBAAAA,KAAAA,GAAAA;QAAAA,sBAAsBL;;IAEtB,MAAM,EAACJ,KAAD,EAAQE,QAAR,EAAkBD,MAAlB,EAA0BE,OAAAA,KAAWZ,iBAAiB,CAACe,eAAD,CAA5D;IAEA,MAAMI,SAAS,GAAG;QAChBhK,CAAC,EAAE,CADa;QAEhBC,CAAC,EAAE;KAFL;IAIA,MAAMgK,KAAK,GAAG;QACZjK,CAAC,EAAE,CADS;QAEZC,CAAC,EAAE;KAFL;IAIA,MAAMiK,SAAS,GAAG;QAChBlJ,MAAM,EAAE6I,mBAAmB,CAAC7I,MAApB,GAA6B+I,mBAAmB,CAAC9J,CADzC;QAEhBa,KAAK,EAAE+I,mBAAmB,CAAC/I,KAApB,GAA4BiJ,mBAAmB,CAAC/J,CAAAA;KAFzD;IAKA,IAAI,CAACsJ,KAAD,IAAUvI,GAAG,IAAI8I,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAAzD,EAAiE;;QAE/DgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAACyB,QAAxB;QACAF,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAApC,GAA6CD,GAA9C,IAAqDmJ,SAAS,CAAClJ,MADjE,CAFF;KAHF,MAQO,IACL,CAACwI,QAAD,IACAhG,MAAM,IAAIqG,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAF5C,EAGL;;QAEAgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAAC2B,OAAxB;QACAJ,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAAvC,GAAgDwC,MAAjD,IACE0G,SAAS,CAAClJ,MAFd,CAFF;;IAQF,IAAI,CAACyI,OAAD,IAAYnG,KAAK,IAAIuG,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAA/D,EAAsE;;QAEpEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAAC2B,OAAxB;QACAJ,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAAtC,GAA8CwC,KAA/C,IAAwD4G,SAAS,CAACpJ,KADpE,CAFF;KAHF,MAQO,IAAI,CAACyI,MAAD,IAAW1I,IAAI,IAAIgJ,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAA5D,EAAmE;;QAExEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAACyB,QAAxB;QACAF,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAArC,GAA6CD,IAA9C,IAAsDqJ,SAAS,CAACpJ,KADlE,CAFF;;IAOF,OAAO;QACLkJ,SADK;QAELC;KAFF;AAID;SC7EeK,qBAAqBtE,OAAAA;IACnC,IAAIA,OAAO,KAAK4C,QAAQ,CAACtB,gBAAzB,EAA2C;QACzC,MAAM,EAAChB,UAAD,EAAaC,WAAAA,KAAe2B,MAAlC;QAEA,OAAO;YACLnH,GAAG,EAAE,CADA;YAELF,IAAI,EAAE,CAFD;YAGLyC,KAAK,EAAEgD,UAHF;YAIL9C,MAAM,EAAE+C,WAJH;YAKLzF,KAAK,EAAEwF,UALF;YAMLtF,MAAM,EAAEuF;SANV;;IAUF,MAAM,EAACxF,GAAD,EAAMF,IAAN,EAAYyC,KAAZ,EAAmBE,MAAAA,KAAUwC,OAAO,CAACC,qBAAR,EAAnC;IAEA,OAAO;QACLlF,GADK;QAELF,IAFK;QAGLyC,KAHK;QAILE,MAJK;QAKL1C,KAAK,EAAEkF,OAAO,CAACkD,WALV;QAMLlI,MAAM,EAAEgF,OAAO,CAACiD,YAAAA;KANlB;AAQD;SCdesB,iBAAiBC,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAwC,CAACkC,GAAD,EAAM4B,IAAN;QAC7C,iLAAOxJ,MAAAA,AAAG,EAAC4H,GAAD,EAAM4D,oBAAoB,CAAChC,IAAD,CAA1B,CAAV;KADK,EAEJ5G,kBAFI,CAAP;AAGD;AAED,SAAgB4K,iBAAiBD,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;QACxC,OAAO5B,GAAG,GAAGsD,oBAAoB,CAAC1B,IAAD,CAAjC;KADK,EAEJ,CAFI,CAAP;AAGD;AAED,SAAgBiE,iBAAiBF,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;QACxC,OAAO5B,GAAG,GAAGyD,oBAAoB,CAAC7B,IAAD,CAAjC;KADK,EAEJ,CAFI,CAAP;AAGD;SCtBekE,uBACd3E,OAAAA,EACA4E,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAA6C7E;;IAE7C,IAAI,CAACC,OAAL,EAAc;QACZ;;IAGF,MAAM,EAACjF,GAAD,EAAMF,IAAN,EAAY2C,MAAZ,EAAoBF,KAAAA,KAASsH,OAAO,CAAC5E,OAAD,CAA1C;IACA,MAAM4B,uBAAuB,GAAGD,0BAA0B,CAAC3B,OAAD,CAA1D;IAEA,IAAI,CAAC4B,uBAAL,EAA8B;QAC5B;;IAGF,IACEpE,MAAM,IAAI,CAAV,IACAF,KAAK,IAAI,CADT,IAEAvC,GAAG,IAAImH,MAAM,CAAC3B,WAFd,IAGA1F,IAAI,IAAIqH,MAAM,CAAC5B,UAJjB,EAKE;QACAN,OAAO,CAAC6E,cAAR,CAAuB;YACrBC,KAAK,EAAE,QADc;YAErBC,MAAM,EAAE;SAFV;;AAKH;ACtBD,MAAMjE,UAAU,GAAG;IACjB;QAAC,GAAD;QAAM;YAAC,MAAD;YAAS,OAAT;SAAN;QAAyB2D,gBAAzB;KADiB;IAEjB;QAAC,GAAD;QAAM;YAAC,KAAD;YAAQ,QAAR;SAAN;QAAyBC,gBAAzB;KAFiB;CAAnB;AAKA,MAAaM;IACXC,YAAYxK,IAAAA,EAAkBuF,OAAAA,CAAAA;aAyBtBvF,IAAAA,GAAAA,KAAAA;aAEDK,KAAAA,GAAAA,KAAAA;aAEAE,MAAAA,GAAAA,KAAAA;aAIAD,GAAAA,GAAAA,KAAAA;aAEAyC,MAAAA,GAAAA,KAAAA;aAEAF,KAAAA,GAAAA,KAAAA;aAEAzC,IAAAA,GAAAA,KAAAA;QAtCL,MAAM2J,mBAAmB,GAAGvD,sBAAsB,CAACjB,OAAD,CAAlD;QACA,MAAMkF,aAAa,GAAGX,gBAAgB,CAACC,mBAAD,CAAtC;QAEA,IAAA,CAAK/J,IAAL,GAAY;YAAC,GAAGA,IAAAA;SAAhB;QACA,IAAA,CAAKK,KAAL,GAAaL,IAAI,CAACK,KAAlB;QACA,IAAA,CAAKE,MAAL,GAAcP,IAAI,CAACO,MAAnB;QAEA,KAAK,MAAM,CAACmK,IAAD,EAAOC,IAAP,EAAaC,eAAb,CAAX,IAA4CvE,UAA5C,CAAwD;YACtD,KAAK,MAAMwE,GAAX,IAAkBF,IAAlB,CAAwB;gBACtBtL,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4BD,GAA5B,EAAiC;oBAC/BnJ,GAAG,EAAE;wBACH,MAAMqJ,cAAc,GAAGH,eAAe,CAACb,mBAAD,CAAtC;wBACA,MAAMiB,mBAAmB,GAAGP,aAAa,CAACC,IAAD,CAAb,GAAsBK,cAAlD;wBAEA,OAAO,IAAA,CAAK/K,IAAL,CAAU6K,GAAV,CAAA,GAAiBG,mBAAxB;qBAL6B;oBAO/BC,UAAU,EAAE;iBAPd;;;QAYJ5L,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;YAACG,UAAU,EAAE;SAAjD;;;MCpCSC;IAOXV,YAAoB7H,MAAAA,CAAAA;aAAAA,MAAAA,GAAAA,KAAAA;aANZvG,SAAAA,GAIF,EAAA;aAaC+O,SAAAA,GAAY;YACjB,IAAA,CAAK/O,SAAL,CAAeS,OAAf,EAAwBhB,QAAD;gBAAA,IAAA;gBAAA,OAAA,CAAA,eACrB,IAAA,CAAK8G,MADgB,KAAA,OAAA,KAAA,IACrB,aAAayI,mBAAb,CAAiC,GAAGvP,QAApC,CADqB;aAAvB;;QAZkB,IAAA,CAAA,MAAA,GAAA8G,MAAA;;IAEbnG,GAAG,CACR6O,SADQ,EAERC,OAFQ,EAGRtM,OAHQ,EAAA;;QAKR,CAAA,gBAAA,IAAA,CAAK2D,MAAL,KAAA,OAAA,KAAA,IAAA,cAAa4I,gBAAb,CAA8BF,SAA9B,EAAyCC,OAAzC,EAAmEtM,OAAnE;QACA,IAAA,CAAK5C,SAAL,CAAewF,IAAf,CAAoB;YAACyJ,SAAD;YAAYC,OAAZ;YAAsCtM,OAAtC;SAApB;;;SCbYwM,uBACd7I,MAAAA;;;;;;IAQA,MAAM,EAAC8I,WAAAA,+KAAehG,YAAAA,AAAS,EAAC9C,MAAD,CAA/B;IAEA,OAAOA,MAAM,YAAY8I,WAAlB,GAAgC9I,MAAhC,GAAyC6E,6LAAAA,AAAgB,EAAC7E,MAAD,CAAhE;AACD;SCZe+I,oBACdC,KAAAA,EACAC,WAAAA;IAEA,MAAMC,EAAE,GAAGjM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACpM,CAAf,CAAX;IACA,MAAMuM,EAAE,GAAGlM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACnM,CAAf,CAAX;IAEA,IAAI,OAAOoM,WAAP,KAAuB,QAA3B,EAAqC;QACnC,OAAOhM,IAAI,CAACC,IAAL,CAAUgM,EAAE,IAAI,CAAN,GAAUC,EAAE,IAAI,CAA1B,IAA+BF,WAAtC;;IAGF,IAAI,OAAOA,WAAP,IAAsB,OAAOA,WAAjC,EAA8C;QAC5C,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAjB,IAAsBuM,EAAE,GAAGF,WAAW,CAACpM,CAA9C;;IAGF,IAAI,OAAOoM,WAAX,EAAwB;QACtB,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAxB;;IAGF,IAAI,OAAOqM,WAAX,EAAwB;QACtB,OAAOE,EAAE,GAAGF,WAAW,CAACpM,CAAxB;;IAGF,OAAO,KAAP;AACD;AC1BD,IAAYuM,SAAZ;AAAA,CAAA,SAAYA,SAAAA;IACVA,SAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,SAAAA,CAAAA,YAAA,GAAA,WAAA;IACAA,SAAAA,CAAAA,UAAA,GAAA,SAAA;IACAA,SAAAA,CAAAA,cAAA,GAAA,aAAA;IACAA,SAAAA,CAAAA,SAAA,GAAA,QAAA;IACAA,SAAAA,CAAAA,kBAAA,GAAA,iBAAA;IACAA,SAAAA,CAAAA,mBAAA,GAAA,kBAAA;AACD,CARD,EAAYA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA,CAArB;AAUA,SAAgBC,eAAepP,KAAAA;IAC7BA,KAAK,CAACoP,cAAN;AACD;AAED,SAAgBC,gBAAgBrP,KAAAA;IAC9BA,KAAK,CAACqP,eAAN;AACD;ICbWC,YAAZ;AAAA,CAAA,SAAYA,YAAAA;IACVA,YAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,YAAAA,CAAAA,OAAA,GAAA,WAAA;IACAA,YAAAA,CAAAA,QAAA,GAAA,YAAA;IACAA,YAAAA,CAAAA,OAAA,GAAA,WAAA;IACAA,YAAAA,CAAAA,KAAA,GAAA,SAAA;IACAA,YAAAA,CAAAA,MAAA,GAAA,QAAA;IACAA,YAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,YAAAA,CAAAA,MAAA,GAAA,KAAA;AACD,CATD,EAAYA,YAAY,IAAA,CAAZA,YAAY,GAAA,CAAA,CAAA,CAAxB;ACDO,MAAMC,oBAAoB,GAAkB;IACjDC,KAAK,EAAE;QAACF,YAAY,CAACG,KAAd;QAAqBH,YAAY,CAACI,KAAlC;KAD0C;IAEjDC,MAAM,EAAE;QAACL,YAAY,CAACM,GAAd;KAFyC;IAGjDC,GAAG,EAAE;QAACP,YAAY,CAACG,KAAd;QAAqBH,YAAY,CAACI,KAAlC;QAAyCJ,YAAY,CAACQ,GAAtD;KAAA;AAH4C,CAA5C;AAMP,MAAaC,+BAA+B,GAA6B,CACvE/P,KADuE,EAAA;QAEvE,EAACgQ,kBAAAA;IAED,OAAQhQ,KAAK,CAACiQ,IAAd;QACE,KAAKX,YAAY,CAACY,KAAlB;YACE,OAAO;gBACL,GAAGF,kBADE;gBAELrN,CAAC,EAAEqN,kBAAkB,CAACrN,CAAnB,GAAuB;aAF5B;QAIF,KAAK2M,YAAY,CAACa,IAAlB;YACE,OAAO;gBACL,GAAGH,kBADE;gBAELrN,CAAC,EAAEqN,kBAAkB,CAACrN,CAAnB,GAAuB;aAF5B;QAIF,KAAK2M,YAAY,CAACc,IAAlB;YACE,OAAO;gBACL,GAAGJ,kBADE;gBAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;aAF5B;QAIF,KAAK0M,YAAY,CAACe,EAAlB;YACE,OAAO;gBACL,GAAGL,kBADE;gBAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;aAF5B;;IAMJ,OAAO0N,SAAP;AACD,CA5BM;MC+BMC;IAMX3C,YAAoB4C,KAAAA,CAAAA;aAAAA,KAAAA,GAAAA,KAAAA;aALbC,iBAAAA,GAAoB;aACnBC,oBAAAA,GAAAA,KAAAA;aACAlR,SAAAA,GAAAA,KAAAA;aACAmR,eAAAA,GAAAA,KAAAA;QAEY,IAAA,CAAA,KAAA,GAAAH,KAAA;QAClB,MAAM,EACJxQ,KAAK,EAAE,EAAC+F,MAAAA,OACNyK,KAFJ;QAIA,IAAA,CAAKA,KAAL,GAAaA,KAAb;QACA,IAAA,CAAKhR,SAAL,GAAiB,IAAI8O,SAAJ,2KAAc1D,mBAAAA,AAAgB,EAAC7E,MAAD,CAA9B,CAAjB;QACA,IAAA,CAAK4K,eAAL,GAAuB,IAAIrC,SAAJ,2KAAczF,YAAAA,AAAS,EAAC9C,MAAD,CAAvB,CAAvB;QACA,IAAA,CAAK6K,aAAL,GAAqB,IAAA,CAAKA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;QAEA,IAAA,CAAKE,MAAL;;IAGMA,MAAM,GAAA;QACZ,IAAA,CAAKC,WAAL;QAEA,IAAA,CAAKL,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC8B,MAAnC,EAA2C,IAAA,CAAKH,YAAhD;QACA,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC+B,gBAAnC,EAAqD,IAAA,CAAKJ,YAA1D;QAEAK,UAAU,CAAC,IAAM,IAAA,CAAK3R,SAAL,CAAeI,GAAf,CAAmBuP,SAAS,CAACiC,OAA7B,EAAsC,IAAA,CAAKR,aAA3C,CAAP,CAAV;;IAGMI,WAAW,GAAA;QACjB,MAAM,EAACK,UAAD,EAAaC,OAAAA,KAAW,IAAA,CAAKd,KAAnC;QACA,MAAMpH,IAAI,GAAGiI,UAAU,CAACjI,IAAX,CAAgBmI,OAA7B;QAEA,IAAInI,IAAJ,EAAU;YACRkE,sBAAsB,CAAClE,IAAD,CAAtB;;QAGFkI,OAAO,CAAC9O,kBAAD,CAAP;;IAGMoO,aAAa,CAAC5Q,KAAD,EAAA;QACnB,IAAIwR,4LAAAA,AAAe,EAACxR,KAAD,CAAnB,EAA4B;YAC1B,MAAM,EAACM,MAAD,EAASmR,OAAT,EAAkBrP,OAAAA,KAAW,IAAA,CAAKoO,KAAxC;YACA,MAAM,EACJkB,aAAa,GAAGnC,oBADZ,EAEJoC,gBAAgB,GAAG5B,+BAFf,EAGJ6B,cAAc,GAAG,QAAA,KACfxP,OAJJ;YAKA,MAAM,EAAC6N,IAAAA,KAAQjQ,KAAf;YAEA,IAAI0R,aAAa,CAAC7B,GAAd,CAAkB3F,QAAlB,CAA2B+F,IAA3B,CAAJ,EAAsC;gBACpC,IAAA,CAAK4B,SAAL,CAAe7R,KAAf;gBACA;;YAGF,IAAI0R,aAAa,CAAC/B,MAAd,CAAqBzF,QAArB,CAA8B+F,IAA9B,CAAJ,EAAyC;gBACvC,IAAA,CAAKa,YAAL,CAAkB9Q,KAAlB;gBACA;;YAGF,MAAM,EAACyE,aAAAA,KAAiBgN,OAAO,CAACF,OAAhC;YACA,MAAMvB,kBAAkB,GAAGvL,aAAa,GACpC;gBAAC9B,CAAC,EAAE8B,aAAa,CAACjB,IAAlB;gBAAwBZ,CAAC,EAAE6B,aAAa,CAACf,GAAAA;aADL,GAEpClB,kBAFJ;YAIA,IAAI,CAAC,IAAA,CAAKkO,oBAAV,EAAgC;gBAC9B,IAAA,CAAKA,oBAAL,GAA4BV,kBAA5B;;YAGF,MAAM8B,cAAc,GAAGH,gBAAgB,CAAC3R,KAAD,EAAQ;gBAC7CM,MAD6C;gBAE7CmR,OAAO,EAAEA,OAAO,CAACF,OAF4B;gBAG7CvB;aAHqC,CAAvC;YAMA,IAAI8B,cAAJ,EAAoB;gBAClB,MAAMC,gBAAgB,IAAGC,oLAAAA,AAAmB,EAC1CF,cAD0C,EAE1C9B,kBAF0C,CAA5C;gBAIA,MAAMiC,WAAW,GAAG;oBAClBtP,CAAC,EAAE,CADe;oBAElBC,CAAC,EAAE;iBAFL;gBAIA,MAAM,EAACuK,mBAAAA,KAAuBsE,OAAO,CAACF,OAAtC;gBAEA,KAAK,MAAMhF,eAAX,IAA8BY,mBAA9B,CAAmD;oBACjD,MAAMR,SAAS,GAAG3M,KAAK,CAACiQ,IAAxB;oBACA,MAAM,EAAChE,KAAD,EAAQG,OAAR,EAAiBF,MAAjB,EAAyBC,QAAzB,EAAmCL,SAAnC,EAA8CJ,SAAAA,KAClDF,iBAAiB,CAACe,eAAD,CADnB;oBAEA,MAAM2F,iBAAiB,GAAGjF,oBAAoB,CAACV,eAAD,CAA9C;oBAEA,MAAM4F,kBAAkB,GAAG;wBACzBxP,CAAC,EAAEK,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAACjM,KAAlB,GAA0BiM,iBAAiB,CAACzO,KAAlB,GAA0B,CADxD,GAEIyO,iBAAiB,CAACjM,KAHrB,EAIDjD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACIgC,iBAAiB,CAAC1O,IADtB,GAEI0O,iBAAiB,CAAC1O,IAAlB,GAAyB0O,iBAAiB,CAACzO,KAAlB,GAA0B,CAHzD,EAIEqO,cAAc,CAACnP,CAJjB,CAJC,CADsB;wBAYzBC,CAAC,EAAEI,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAAC/L,MAAlB,GAA2B+L,iBAAiB,CAACvO,MAAlB,GAA2B,CAD1D,GAEIuO,iBAAiB,CAAC/L,MAHrB,EAIDnD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI8B,iBAAiB,CAACxO,GADtB,GAEIwO,iBAAiB,CAACxO,GAAlB,GAAwBwO,iBAAiB,CAACvO,MAAlB,GAA2B,CAHzD,EAIEmO,cAAc,CAAClP,CAJjB,CAJC;qBAZL;oBAyBA,MAAMwP,UAAU,GACbzF,SAAS,KAAK2C,YAAY,CAACY,KAA3B,IAAoC,CAAC9D,OAAtC,IACCO,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IAAmC,CAACjE,MAFvC;oBAGA,MAAMmG,UAAU,GACb1F,SAAS,KAAK2C,YAAY,CAACc,IAA3B,IAAmC,CAACjE,QAArC,IACCQ,SAAS,KAAK2C,YAAY,CAACe,EAA3B,IAAiC,CAACpE,KAFrC;oBAIA,IAAImG,UAAU,IAAID,kBAAkB,CAACxP,CAAnB,KAAyBmP,cAAc,CAACnP,CAA1D,EAA6D;wBAC3D,MAAM2P,oBAAoB,GACxB/F,eAAe,CAACvB,UAAhB,GAA6B+G,gBAAgB,CAACpP,CADhD;wBAEA,MAAM4P,yBAAyB,GAC5B5F,SAAS,KAAK2C,YAAY,CAACY,KAA3B,IACCoC,oBAAoB,IAAIxG,SAAS,CAACnJ,CADpC,IAECgK,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IACCmC,oBAAoB,IAAI5G,SAAS,CAAC/I,CAJtC;wBAMA,IAAI4P,yBAAyB,IAAI,CAACR,gBAAgB,CAACnP,CAAnD,EAAsD;;;4BAGpD2J,eAAe,CAACiG,QAAhB,CAAyB;gCACvBhP,IAAI,EAAE8O,oBADiB;gCAEvBG,QAAQ,EAAEb;6BAFZ;4BAIA;;wBAGF,IAAIW,yBAAJ,EAA+B;4BAC7BN,WAAW,CAACtP,CAAZ,GAAgB4J,eAAe,CAACvB,UAAhB,GAA6BsH,oBAA7C;yBADF,MAEO;4BACLL,WAAW,CAACtP,CAAZ,GACEgK,SAAS,KAAK2C,YAAY,CAACY,KAA3B,GACI3D,eAAe,CAACvB,UAAhB,GAA6Bc,SAAS,CAACnJ,CAD3C,GAEI4J,eAAe,CAACvB,UAAhB,GAA6BU,SAAS,CAAC/I,CAH7C;;wBAMF,IAAIsP,WAAW,CAACtP,CAAhB,EAAmB;4BACjB4J,eAAe,CAACmG,QAAhB,CAAyB;gCACvBlP,IAAI,EAAE,CAACyO,WAAW,CAACtP,CADI;gCAEvB8P,QAAQ,EAAEb;6BAFZ;;wBAKF;qBAlCF,MAmCO,IAAIS,UAAU,IAAIF,kBAAkB,CAACvP,CAAnB,KAAyBkP,cAAc,CAAClP,CAA1D,EAA6D;wBAClE,MAAM0P,oBAAoB,GACxB/F,eAAe,CAACpB,SAAhB,GAA4B4G,gBAAgB,CAACnP,CAD/C;wBAEA,MAAM2P,yBAAyB,GAC5B5F,SAAS,KAAK2C,YAAY,CAACc,IAA3B,IACCkC,oBAAoB,IAAIxG,SAAS,CAAClJ,CADpC,IAEC+J,SAAS,KAAK2C,YAAY,CAACe,EAA3B,IACCiC,oBAAoB,IAAI5G,SAAS,CAAC9I,CAJtC;wBAMA,IAAI2P,yBAAyB,IAAI,CAACR,gBAAgB,CAACpP,CAAnD,EAAsD;;;4BAGpD4J,eAAe,CAACiG,QAAhB,CAAyB;gCACvB9O,GAAG,EAAE4O,oBADkB;gCAEvBG,QAAQ,EAAEb;6BAFZ;4BAIA;;wBAGF,IAAIW,yBAAJ,EAA+B;4BAC7BN,WAAW,CAACrP,CAAZ,GAAgB2J,eAAe,CAACpB,SAAhB,GAA4BmH,oBAA5C;yBADF,MAEO;4BACLL,WAAW,CAACrP,CAAZ,GACE+J,SAAS,KAAK2C,YAAY,CAACc,IAA3B,GACI7D,eAAe,CAACpB,SAAhB,GAA4BW,SAAS,CAAClJ,CAD1C,GAEI2J,eAAe,CAACpB,SAAhB,GAA4BO,SAAS,CAAC9I,CAH5C;;wBAMF,IAAIqP,WAAW,CAACrP,CAAhB,EAAmB;4BACjB2J,eAAe,CAACmG,QAAhB,CAAyB;gCACvBhP,GAAG,EAAE,CAACuO,WAAW,CAACrP,CADK;gCAEvB6P,QAAQ,EAAEb;6BAFZ;;wBAMF;;;gBAIJ,IAAA,CAAKe,UAAL,CACE3S,KADF,4KAEE4S,MAAAA,AAAsB,EACpBZ,qLAAAA,AAAmB,EAACF,cAAD,EAAiB,IAAA,CAAKpB,oBAAtB,CADC,EAEpBuB,WAFoB,CAFxB;;;;IAWEU,UAAU,CAAC3S,KAAD,EAAe6S,WAAf,EAAA;QAChB,MAAM,EAACC,MAAAA,KAAU,IAAA,CAAKtC,KAAtB;QAEAxQ,KAAK,CAACoP,cAAN;QACA0D,MAAM,CAACD,WAAD,CAAN;;IAGMhB,SAAS,CAAC7R,KAAD,EAAA;QACf,MAAM,EAAC+S,KAAAA,KAAS,IAAA,CAAKvC,KAArB;QAEAxQ,KAAK,CAACoP,cAAN;QACA,IAAA,CAAK4D,MAAL;QACAD,KAAK;;IAGCjC,YAAY,CAAC9Q,KAAD,EAAA;QAClB,MAAM,EAACiT,QAAAA,KAAY,IAAA,CAAKzC,KAAxB;QAEAxQ,KAAK,CAACoP,cAAN;QACA,IAAA,CAAK4D,MAAL;QACAC,QAAQ;;IAGFD,MAAM,GAAA;QACZ,IAAA,CAAKxT,SAAL,CAAe+O,SAAf;QACA,IAAA,CAAKoC,eAAL,CAAqBpC,SAArB;;;AA1OSgC,eA6OJ2C,UAAAA,GAAgD;IACrD;QACEzE,SAAS,EAAE,WADb;QAEEC,OAAO,EAAE,CACP1O,KADO,EAAA,MAAA;gBAEP,EAAC0R,aAAa,GAAGnC,oBAAjB,EAAuC4D,YAAAA;gBACvC,EAAC7S,MAAAA;YAED,MAAM,EAAC2P,IAAAA,KAAQjQ,KAAK,CAACoT,WAArB;YAEA,IAAI1B,aAAa,CAAClC,KAAd,CAAoBtF,QAApB,CAA6B+F,IAA7B,CAAJ,EAAwC;gBACtC,MAAMoD,SAAS,GAAG/S,MAAM,CAACgT,aAAP,CAAqB/B,OAAvC;gBAEA,IAAI8B,SAAS,IAAIrT,KAAK,CAAC+F,MAAN,KAAiBsN,SAAlC,EAA6C;oBAC3C,OAAO,KAAP;;gBAGFrT,KAAK,CAACoP,cAAN;gBAEA+D,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;oBAACnT,KAAK,EAAEA,KAAK,CAACoT,WAAAA;iBAAjB,CAAZ;gBAEA,OAAO,IAAP;;YAGF,OAAO,KAAP;;IAvBJ,CADqD;CAAA;ACxOzD,SAASG,oBAAT,CACEC,UADF;IAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,cAAcA,UAA7B,CAAd;AACD;AAED,SAASE,iBAAT,CACEF,UADF;IAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,WAAWA,UAA1B,CAAd;AACD;AAaD,MAAaG;IAUX/F,YACU4C,KAAAA,EACAoD,MAAAA,EACRC,cAAAA,CAAAA;;YAAAA,mBAAAA,KAAAA,GAAAA;YAAAA,iBAAiBjF,sBAAsB,CAAC4B,KAAK,CAACxQ,KAAN,CAAY+F,MAAb;;aAF/ByK,KAAAA,GAAAA,KAAAA;aACAoD,MAAAA,GAAAA,KAAAA;aAXHnD,iBAAAA,GAAoB;aACnBlF,QAAAA,GAAAA,KAAAA;aACAuI,SAAAA,GAAqB;aACrBC,kBAAAA,GAAAA,KAAAA;aACAC,SAAAA,GAAmC;aACnCxU,SAAAA,GAAAA,KAAAA;aACAyU,iBAAAA,GAAAA,KAAAA;aACAtD,eAAAA,GAAAA,KAAAA;QAGE,IAAA,CAAA,KAAA,GAAAH,KAAA;QACA,IAAA,CAAA,MAAA,GAAAoD,MAAA;QAGR,MAAM,EAAC5T,KAAAA,KAASwQ,KAAhB;QACA,MAAM,EAACzK,MAAAA,KAAU/F,KAAjB;QAEA,IAAA,CAAKwQ,KAAL,GAAaA,KAAb;QACA,IAAA,CAAKoD,MAAL,GAAcA,MAAd;QACA,IAAA,CAAKrI,QAAL,4KAAgBX,oBAAAA,AAAgB,EAAC7E,MAAD,CAAhC;QACA,IAAA,CAAKkO,iBAAL,GAAyB,IAAI3F,SAAJ,CAAc,IAAA,CAAK/C,QAAnB,CAAzB;QACA,IAAA,CAAK/L,SAAL,GAAiB,IAAI8O,SAAJ,CAAcuF,cAAd,CAAjB;QACA,IAAA,CAAKlD,eAAL,GAAuB,IAAIrC,SAAJ,2KAAczF,YAAAA,AAAS,EAAC9C,MAAD,CAAvB,CAAvB;QACA,IAAA,CAAKgO,kBAAL,GAAA,CAAA,iMAA0BzQ,sBAAAA,AAAmB,EAACtD,KAAD,CAA7C,KAAA,OAAA,uBAAwDwC,kBAAxD;QACA,IAAA,CAAKwO,WAAL,GAAmB,IAAA,CAAKA,WAAL,CAAiBH,IAAjB,CAAsB,IAAtB,CAAnB;QACA,IAAA,CAAK8B,UAAL,GAAkB,IAAA,CAAKA,UAAL,CAAgB9B,IAAhB,CAAqB,IAArB,CAAlB;QACA,IAAA,CAAKgB,SAAL,GAAiB,IAAA,CAAKA,SAAL,CAAehB,IAAf,CAAoB,IAApB,CAAjB;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;QACA,IAAA,CAAKqD,aAAL,GAAqB,IAAA,CAAKA,aAAL,CAAmBrD,IAAnB,CAAwB,IAAxB,CAArB;QACA,IAAA,CAAKsD,mBAAL,GAA2B,IAAA,CAAKA,mBAAL,CAAyBtD,IAAzB,CAA8B,IAA9B,CAA3B;QAEA,IAAA,CAAKE,MAAL;;IAGMA,MAAM,GAAA;QACZ,MAAM,EACJ6C,MADI,EAEJpD,KAAK,EAAE,EACLpO,OAAO,EAAE,EAACgS,oBAAD,EAAuBC,0BAAAA,SAEhC,IALJ;QAOA,IAAA,CAAK7U,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAACU,IAAP,CAAYC,IAA/B,EAAqC,IAAA,CAAK5B,UAA1C,EAAsD;YAAC6B,OAAO,EAAE;SAAhE;QACA,IAAA,CAAKhV,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAAC/D,GAAP,CAAW0E,IAA9B,EAAoC,IAAA,CAAK1C,SAAzC;QAEA,IAAI+B,MAAM,CAACjE,MAAX,EAAmB;YACjB,IAAA,CAAKnQ,SAAL,CAAeI,GAAf,CAAmBgU,MAAM,CAACjE,MAAP,CAAc4E,IAAjC,EAAuC,IAAA,CAAKzD,YAA5C;;QAGF,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC8B,MAAnC,EAA2C,IAAA,CAAKH,YAAhD;QACA,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAACsF,SAAnC,EAA8CrF,cAA9C;QACA,IAAA,CAAKuB,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAAC+B,gBAAnC,EAAqD,IAAA,CAAKJ,YAA1D;QACA,IAAA,CAAKH,eAAL,CAAqB/Q,GAArB,CAAyBuP,SAAS,CAACuF,WAAnC,EAAgDtF,cAAhD;QACA,IAAA,CAAK6E,iBAAL,CAAuBrU,GAAvB,CAA2BuP,SAAS,CAACiC,OAArC,EAA8C,IAAA,CAAK8C,aAAnD;QAEA,IAAIE,oBAAJ,EAA0B;YACxB,IACEC,0BADF,IAAA,QACEA,0BAA0B,CAAG;gBAC3BrU,KAAK,EAAE,IAAA,CAAKwQ,KAAL,CAAWxQ,KADS;gBAE3BqR,UAAU,EAAE,IAAA,CAAKb,KAAL,CAAWa,UAFI;gBAG3BjP,OAAO,EAAE,IAAA,CAAKoO,KAAL,CAAWpO,OAAAA;aAHI,CAD5B,EAME;gBACA,OAAO,IAAA,CAAK4O,WAAL,EAAP;;YAGF,IAAI0C,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;gBAC3C,IAAA,CAAKJ,SAAL,GAAiB7C,UAAU,CACzB,IAAA,CAAKH,WADoB,EAEzBoD,oBAAoB,CAACO,KAFI,CAA3B;gBAIA,IAAA,CAAKC,aAAL,CAAmBR,oBAAnB;gBACA;;YAGF,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;gBAC9C,IAAA,CAAKQ,aAAL,CAAmBR,oBAAnB;gBACA;;;QAIJ,IAAA,CAAKpD,WAAL;;IAGMgC,MAAM,GAAA;QACZ,IAAA,CAAKxT,SAAL,CAAe+O,SAAf;QACA,IAAA,CAAKoC,eAAL,CAAqBpC,SAArB,IAAA,oEAAA;;QAIA4C,UAAU,CAAC,IAAA,CAAK8C,iBAAL,CAAuB1F,SAAxB,EAAmC,EAAnC,CAAV;QAEA,IAAI,IAAA,CAAKyF,SAAL,KAAmB,IAAvB,EAA6B;YAC3Ba,YAAY,CAAC,IAAA,CAAKb,SAAN,CAAZ;YACA,IAAA,CAAKA,SAAL,GAAiB,IAAjB;;;IAIIY,aAAa,CACnBpB,UADmB,EAEnBsB,MAFmB,EAAA;QAInB,MAAM,EAACxU,MAAD,EAASyU,SAAAA,KAAa,IAAA,CAAKvE,KAAjC;QACAuE,SAAS,CAACzU,MAAD,EAASkT,UAAT,EAAqB,IAAA,CAAKO,kBAA1B,EAA8Ce,MAA9C,CAAT;;IAGM9D,WAAW,GAAA;QACjB,MAAM,EAAC+C,kBAAAA,KAAsB,IAA7B;QACA,MAAM,EAACzC,OAAAA,KAAW,IAAA,CAAKd,KAAvB;QAEA,IAAIuD,kBAAJ,EAAwB;YACtB,IAAA,CAAKD,SAAL,GAAiB,IAAjB,CADsB,CAAA,uEAAA;YAItB,IAAA,CAAKG,iBAAL,CAAuBrU,GAAvB,CAA2BuP,SAAS,CAAC6F,KAArC,EAA4C3F,eAA5C,EAA6D;gBAC3D4F,OAAO,EAAE;aADX,EAJsB,CAAA,8CAAA;YAStB,IAAA,CAAKd,mBAAL,GATsB,CAAA,gDAAA;YAYtB,IAAA,CAAKF,iBAAL,CAAuBrU,GAAvB,CACEuP,SAAS,CAAC+F,eADZ,EAEE,IAAA,CAAKf,mBAFP;YAKA7C,OAAO,CAACyC,kBAAD,CAAP;;;IAIIpB,UAAU,CAAC3S,KAAD,EAAA;;QAChB,MAAM,EAAC8T,SAAD,EAAYC,kBAAZ,EAAgCvD,KAAAA,KAAS,IAA/C;QACA,MAAM,EACJsC,MADI,EAEJ1Q,OAAO,EAAE,EAACgS,oBAAAA,OACR5D,KAHJ;QAKA,IAAI,CAACuD,kBAAL,EAAyB;YACvB;;QAGF,MAAMlB,WAAW,GAAA,CAAA,kMAAGvP,sBAAAA,AAAmB,EAACtD,KAAD,CAAtB,KAAA,OAAA,wBAAiCwC,kBAAlD;QACA,MAAMuM,KAAK,4KAAGiD,YAAAA,AAAmB,EAAC+B,kBAAD,EAAqBlB,WAArB,CAAjC,EAAA,wBAAA;QAGA,IAAI,CAACiB,SAAD,IAAcM,oBAAlB,EAAwC;YACtC,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;gBAC9C,IACEA,oBAAoB,CAACe,SAArB,IAAkC,IAAlC,IACArG,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACe,SAA7B,CAFrB,EAGE;oBACA,OAAO,IAAA,CAAKrE,YAAL,EAAP;;gBAGF,IAAIhC,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACgB,QAA7B,CAAvB,EAA+D;oBAC7D,OAAO,IAAA,CAAKpE,WAAL,EAAP;;;YAIJ,IAAI0C,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;gBAC3C,IAAItF,mBAAmB,CAACC,KAAD,EAAQqF,oBAAoB,CAACe,SAA7B,CAAvB,EAAgE;oBAC9D,OAAO,IAAA,CAAKrE,YAAL,EAAP;;;YAIJ,IAAA,CAAK8D,aAAL,CAAmBR,oBAAnB,EAAyCrF,KAAzC;YACA;;QAGF,IAAI/O,KAAK,CAACqV,UAAV,EAAsB;YACpBrV,KAAK,CAACoP,cAAN;;QAGF0D,MAAM,CAACD,WAAD,CAAN;;IAGMhB,SAAS,GAAA;QACf,MAAM,EAACyD,OAAD,EAAUvC,KAAAA,KAAS,IAAA,CAAKvC,KAA9B;QAEA,IAAA,CAAKwC,MAAL;QACA,IAAI,CAAC,IAAA,CAAKc,SAAV,EAAqB;YACnBwB,OAAO,CAAC,IAAA,CAAK9E,KAAL,CAAWlQ,MAAZ,CAAP;;QAEFyS,KAAK;;IAGCjC,YAAY,GAAA;QAClB,MAAM,EAACwE,OAAD,EAAUrC,QAAAA,KAAY,IAAA,CAAKzC,KAAjC;QAEA,IAAA,CAAKwC,MAAL;QACA,IAAI,CAAC,IAAA,CAAKc,SAAV,EAAqB;YACnBwB,OAAO,CAAC,IAAA,CAAK9E,KAAL,CAAWlQ,MAAZ,CAAP;;QAEF2S,QAAQ;;IAGFiB,aAAa,CAAClU,KAAD,EAAA;QACnB,IAAIA,KAAK,CAACiQ,IAAN,KAAeX,YAAY,CAACM,GAAhC,EAAqC;YACnC,IAAA,CAAKkB,YAAL;;;IAIIqD,mBAAmB,GAAA;;QACzB,CAAA,wBAAA,IAAA,CAAK5I,QAAL,CAAcgK,YAAd,EAAA,KAAA,OAAA,KAAA,IAAA,sBAA8BC,eAA9B;;;ACtQJ,MAAM5B,MAAM,GAAyB;IACnCjE,MAAM,EAAE;QAAC4E,IAAI,EAAE;KADoB;IAEnCD,IAAI,EAAE;QAACC,IAAI,EAAE;KAFsB;IAGnC1E,GAAG,EAAE;QAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAakB,sBAAsB9B;IACjC/F,YAAY4C,KAAAA,CAAAA;QACV,MAAM,EAACxQ,KAAAA,KAASwQ,KAAhB,EAAA,uEAAA;;QAGA,MAAMqD,cAAc,6KAAGjJ,mBAAAA,AAAgB,EAAC5K,KAAK,CAAC+F,MAAP,CAAvC;QAEA,KAAA,CAAMyK,KAAN,EAAaoD,MAAb,EAAqBC,cAArB;;;AAPS4B,cAUJvC,UAAAA,GAAa;IAClB;QACEzE,SAAS,EAAE,eADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAAC0E,WAAW,EAAEpT,KAAAA;gBACd,EAACmT,YAAAA;YAED,IAAI,CAACnT,KAAK,CAAC0V,SAAP,IAAoB1V,KAAK,CAAC2V,MAAN,KAAiB,CAAzC,EAA4C;gBAC1C,OAAO,KAAP;;YAGFxC,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAACnT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAZJ,CADkB;CAAA;ACpBtB,MAAM4T,QAAM,GAAyB;IACnCU,IAAI,EAAE;QAACC,IAAI,EAAE;KADsB;IAEnC1E,GAAG,EAAE;QAAC0E,IAAI,EAAE;;AAFuB,CAArC;AAKA,IAAKqB,WAAL;AAAA,CAAA,SAAKA,WAAAA;IACHA,WAAAA,CAAAA,WAAAA,CAAAA,aAAAA,GAAAA,EAAA,GAAA,YAAA;AACD,CAFD,EAAKA,WAAW,IAAA,CAAXA,WAAW,GAAA,CAAA,CAAA,CAAhB;AAQA,MAAaC,oBAAoBlC;IAC/B/F,YAAY4C,KAAAA,CAAAA;QACV,KAAA,CAAMA,KAAN,EAAaoD,QAAb,4KAAqBhJ,mBAAAA,AAAgB,EAAC4F,KAAK,CAACxQ,KAAN,CAAY+F,MAAb,CAArC;;;AAFS8P,YAKJ3C,UAAAA,GAAa;IAClB;QACEzE,SAAS,EAAE,aADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAAC0E,WAAW,EAAEpT,KAAAA;gBACd,EAACmT,YAAAA;YAED,IAAInT,KAAK,CAAC2V,MAAN,KAAiBC,WAAW,CAACE,UAAjC,EAA6C;gBAC3C,OAAO,KAAP;;YAGF3C,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAACnT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAZJ,CADkB;CAAA;AClBtB,MAAM4T,QAAM,GAAyB;IACnCjE,MAAM,EAAE;QAAC4E,IAAI,EAAE;KADoB;IAEnCD,IAAI,EAAE;QAACC,IAAI,EAAE;KAFsB;IAGnC1E,GAAG,EAAE;QAAC0E,IAAI,EAAE;;AAHuB,CAArC;AAUA,MAAawB,oBAAoBpC;IAC/B/F,YAAY4C,KAAAA,CAAAA;QACV,KAAA,CAAMA,KAAN,EAAaoD,QAAb;;IAuBU,OAALoC,KAAK,GAAA;;;;QAIVnL,MAAM,CAAC8D,gBAAP,CAAwBiF,QAAM,CAACU,IAAP,CAAYC,IAApC,EAA0CtS,IAA1C,EAAgD;YAC9CgT,OAAO,EAAE,KADqC;YAE9CT,OAAO,EAAE;SAFX;QAKA,OAAO,SAASyB,QAAT;YACLpL,MAAM,CAAC2D,mBAAP,CAA2BoF,QAAM,CAACU,IAAP,CAAYC,IAAvC,EAA6CtS,IAA7C;SADF,EAAA,0EAAA;;;QAMA,SAASA,IAAT,IAAA;;;AAxCS8T,YAKJ7C,UAAAA,GAAa;IAClB;QACEzE,SAAS,EAAE,cADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAAC0E,WAAW,EAAEpT,KAAAA;gBACd,EAACmT,YAAAA;YAED,MAAM,EAAC+C,OAAAA,KAAWlW,KAAlB;YAEA,IAAIkW,OAAO,CAAC7R,MAAR,GAAiB,CAArB,EAAwB;gBACtB,OAAO,KAAP;;YAGF8O,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAACnT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAdJ,CADkB;CAAA;IChBVmW,mBAAZ;AAAA,CAAA,SAAYA,mBAAAA;IACVA,mBAAAA,CAAAA,mBAAAA,CAAAA,UAAAA,GAAAA,EAAA,GAAA,SAAA;IACAA,mBAAAA,CAAAA,mBAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;AACD,CAHD,EAAYA,mBAAmB,IAAA,CAAnBA,mBAAmB,GAAA,CAAA,CAAA,CAA/B;AAmCA,IAAYC,cAAZ;AAAA,CAAA,SAAYA,cAAAA;IACVA,cAAAA,CAAAA,cAAAA,CAAAA,YAAAA,GAAAA,EAAA,GAAA,WAAA;IACAA,cAAAA,CAAAA,cAAAA,CAAAA,oBAAAA,GAAAA,EAAA,GAAA,mBAAA;AACD,CAHD,EAAYA,cAAc,IAAA,CAAdA,cAAc,GAAA,CAAA,CAAA,CAA1B;AAUA,SAAgBC,gBAAAA,IAAAA;QAAgB,EAC9B5J,YAD8B,EAE9B4G,SAAS,GAAG8C,mBAAmB,CAACG,OAFF,EAG9BC,SAH8B,EAI9BC,YAJ8B,EAK9BC,OAL8B,EAM9BC,QAAQ,GAAG,CANmB,EAO9BC,KAAK,GAAGP,cAAc,CAACQ,SAPO,EAQ9BhQ,kBAR8B,EAS9BuG,mBAT8B,EAU9B0J,uBAV8B,EAW9B9H,KAX8B,EAY9BlC,SAAAA;IAEA,MAAMiK,YAAY,GAAGC,eAAe,CAAC;QAAChI,KAAD;QAAQiI,QAAQ,EAAE,CAACP;KAApB,CAApC;IACA,MAAM,CAACQ,qBAAD,EAAwBC,uBAAxB,CAAA,6KAAmDC,cAAAA,AAAW,EAApE;IACA,MAAMC,WAAW,6MAAGC,SAAAA,AAAM,EAAc;QAAC1U,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;KAAxB,CAA1B;IACA,MAAM0U,eAAe,6MAAGD,SAAAA,AAAM,EAAkB;QAAC1U,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;KAA5B,CAA9B;IACA,MAAMQ,IAAI,6MAAG5B,UAAAA,AAAO,EAAC;QACnB,OAAQ6R,SAAR;YACE,KAAK8C,mBAAmB,CAACG,OAAzB;gBACE,OAAO1P,kBAAkB,GACrB;oBACElD,GAAG,EAAEkD,kBAAkB,CAAChE,CAD1B;oBAEEuD,MAAM,EAAES,kBAAkB,CAAChE,CAF7B;oBAGEY,IAAI,EAAEoD,kBAAkB,CAACjE,CAH3B;oBAIEsD,KAAK,EAAEW,kBAAkB,CAACjE,CAAAA;iBALP,GAOrB,IAPJ;YAQF,KAAKwT,mBAAmB,CAACoB,aAAzB;gBACE,OAAOf,YAAP;;KAZc,EAcjB;QAACnD,SAAD;QAAYmD,YAAZ;QAA0B5P,kBAA1B;KAdiB,CAApB;IAeA,MAAM4Q,kBAAkB,6MAAGH,SAAM,AAANA,EAAuB,IAAjB,CAAjC;IACA,MAAMI,UAAU,GAAG9X,wNAAAA,AAAW,EAAC;QAC7B,MAAM4M,eAAe,GAAGiL,kBAAkB,CAACjG,OAA3C;QAEA,IAAI,CAAChF,eAAL,EAAsB;YACpB;;QAGF,MAAMvB,UAAU,GAAGoM,WAAW,CAAC7F,OAAZ,CAAoB5O,CAApB,GAAwB2U,eAAe,CAAC/F,OAAhB,CAAwB5O,CAAnE;QACA,MAAMwI,SAAS,GAAGiM,WAAW,CAAC7F,OAAZ,CAAoB3O,CAApB,GAAwB0U,eAAe,CAAC/F,OAAhB,CAAwB3O,CAAlE;QAEA2J,eAAe,CAACmG,QAAhB,CAAyB1H,UAAzB,EAAqCG,SAArC;KAV4B,EAW3B,EAX2B,CAA9B;IAYA,MAAMuM,yBAAyB,6MAAGlW,UAAAA,AAAO,EACvC,IACEmV,KAAK,KAAKP,cAAc,CAACQ,SAAzB,GACI,CAAC;eAAGzJ,mBAAJ;SAAA,CAAyBwK,OAAzB,EADJ,GAEIxK,mBAJiC,EAKvC;QAACwJ,KAAD;QAAQxJ,mBAAR;KALuC,CAAzC;8MAQA/N,YAAAA,AAAS,EACP;QACE,IAAI,CAACqX,OAAD,IAAY,CAACtJ,mBAAmB,CAAC9I,MAAjC,IAA2C,CAACjB,IAAhD,EAAsD;YACpD8T,uBAAuB;YACvB;;QAGF,KAAK,MAAM3K,eAAX,IAA8BmL,yBAA9B,CAAyD;YACvD,IAAI,CAAAnB,SAAS,IAAA,IAAT,GAAA,KAAA,IAAAA,SAAS,CAAGhK,eAAH,CAAT,MAAiC,KAArC,EAA4C;gBAC1C;;YAGF,MAAM9G,KAAK,GAAG0H,mBAAmB,CAAC9E,OAApB,CAA4BkE,eAA5B,CAAd;YACA,MAAMC,mBAAmB,GAAGqK,uBAAuB,CAACpR,KAAD,CAAnD;YAEA,IAAI,CAAC+G,mBAAL,EAA0B;gBACxB;;YAGF,MAAM,EAACG,SAAD,EAAYC,KAAAA,KAASN,0BAA0B,CACnDC,eADmD,EAEnDC,mBAFmD,EAGnDpJ,IAHmD,EAInDqJ,YAJmD,EAKnDI,SALmD,CAArD;YAQA,KAAK,MAAMiB,IAAX,IAAmB;gBAAC,GAAD;gBAAM,GAAN;aAAnB,CAAwC;gBACtC,IAAI,CAACgJ,YAAY,CAAChJ,IAAD,CAAZ,CAAmBnB,SAAS,CAACmB,IAAD,CAA5B,CAAL,EAAuD;oBACrDlB,KAAK,CAACkB,IAAD,CAAL,GAAc,CAAd;oBACAnB,SAAS,CAACmB,IAAD,CAAT,GAAkB,CAAlB;;;YAIJ,IAAIlB,KAAK,CAACjK,CAAN,GAAU,CAAV,IAAeiK,KAAK,CAAChK,CAAN,GAAU,CAA7B,EAAgC;gBAC9BsU,uBAAuB;gBAEvBM,kBAAkB,CAACjG,OAAnB,GAA6BhF,eAA7B;gBACA0K,qBAAqB,CAACQ,UAAD,EAAaf,QAAb,CAArB;gBAEAU,WAAW,CAAC7F,OAAZ,GAAsB3E,KAAtB;gBACA0K,eAAe,CAAC/F,OAAhB,GAA0B5E,SAA1B;gBAEA;;;QAIJyK,WAAW,CAAC7F,OAAZ,GAAsB;YAAC5O,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;SAAhC;QACA0U,eAAe,CAAC/F,OAAhB,GAA0B;YAAC5O,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;SAApC;QACAsU,uBAAuB;KAjDlB,EAoDP;QACEzK,YADF;QAEEgL,UAFF;QAGElB,SAHF;QAIEW,uBAJF;QAKET,OALF;QAMEC,QANF;QAQEkB,IAAI,CAACC,SAAL,CAAezU,IAAf,CARF;QAUEwU,IAAI,CAACC,SAAL,CAAef,YAAf,CAVF;QAWEG,qBAXF;QAYE9J,mBAZF;QAaEuK,yBAbF;QAcEb,uBAdF;QAgBEe,IAAI,CAACC,SAAL,CAAehL,SAAf,CAhBF;KApDO,CAAT;AAuED;AAOD,MAAMiL,mBAAmB,GAAiB;IACxCnV,CAAC,EAAE;QAAC,CAAC0I,SAAS,CAACyB,QAAX,CAAA,EAAsB,KAAvB;QAA8B,CAACzB,SAAS,CAAC2B,OAAX,CAAA,EAAqB;KADd;IAExCpK,CAAC,EAAE;QAAC,CAACyI,SAAS,CAACyB,QAAX,CAAA,EAAsB,KAAvB;QAA8B,CAACzB,SAAS,CAAC2B,OAAX,CAAA,EAAqB;;AAFd,CAA1C;AAKA,SAAS+J,eAAT,CAAA,KAAA;QAAyB,EACvBhI,KADuB,EAEvBiI,QAAAA;IAKA,MAAMe,aAAa,OAAGC,oLAAAA,AAAW,EAACjJ,KAAD,CAAjC;IAEA,iLAAOkJ,cAAAA,AAAW,GACfC,cAAD;QACE,IAAIlB,QAAQ,IAAI,CAACe,aAAb,IAA8B,CAACG,cAAnC,EAAmD;;YAEjD,OAAOJ,mBAAP;;QAGF,MAAMnL,SAAS,GAAG;YAChBhK,CAAC,EAAEK,IAAI,CAACmV,IAAL,CAAUpJ,KAAK,CAACpM,CAAN,GAAUoV,aAAa,CAACpV,CAAlC,CADa;YAEhBC,CAAC,EAAEI,IAAI,CAACmV,IAAL,CAAUpJ,KAAK,CAACnM,CAAN,GAAUmV,aAAa,CAACnV,CAAlC;SAFL,EAAA,0EAAA;QAMA,OAAO;YACLD,CAAC,EAAE;gBACD,CAAC0I,SAAS,CAACyB,QAAX,CAAA,EACEoL,cAAc,CAACvV,CAAf,CAAiB0I,SAAS,CAACyB,QAA3B,CAAA,IAAwCH,SAAS,CAAChK,CAAV,KAAgB,CAAC,CAF1D;gBAGD,CAAC0I,SAAS,CAAC2B,OAAX,CAAA,EACEkL,cAAc,CAACvV,CAAf,CAAiB0I,SAAS,CAAC2B,OAA3B,CAAA,IAAuCL,SAAS,CAAChK,CAAV,KAAgB;aALtD;YAOLC,CAAC,EAAE;gBACD,CAACyI,SAAS,CAACyB,QAAX,CAAA,EACEoL,cAAc,CAACtV,CAAf,CAAiByI,SAAS,CAACyB,QAA3B,CAAA,IAAwCH,SAAS,CAAC/J,CAAV,KAAgB,CAAC,CAF1D;gBAGD,CAACyI,SAAS,CAAC2B,OAAX,CAAA,EACEkL,cAAc,CAACtV,CAAf,CAAiByI,SAAS,CAAC2B,OAA3B,CAAA,IAAuCL,SAAS,CAAC/J,CAAV,KAAgB;;SAX7D;KAbc,EA4BhB;QAACoU,QAAD;QAAWjI,KAAX;QAAkBgJ,aAAlB;KA5BgB,CAAlB;AA8BD;SCjOeK,cACdC,cAAAA,EACA9X,EAAAA;IAEA,MAAM+X,aAAa,GAAG/X,EAAE,IAAI,IAAN,GAAa8X,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAb,GAAsC+P,SAA5D;IACA,MAAMlH,IAAI,GAAGkP,aAAa,GAAGA,aAAa,CAAClP,IAAd,CAAmBmI,OAAtB,GAAgC,IAA1D;IAEA,OAAO0G,wLAAAA,AAAW,GACfM,UAAD;;QACE,IAAIhY,EAAE,IAAI,IAAV,EAAgB;YACd,OAAO,IAAP;;;;QAMF,OAAA,CAAA,OAAO6I,IAAP,IAAA,OAAOA,IAAP,GAAemP,UAAf,KAAA,OAAA,OAA6B,IAA7B;KATc,EAWhB;QAACnP,IAAD;QAAO7I,EAAP;KAXgB,CAAlB;AAaD;SCjBeiY,qBACdlW,OAAAA,EACAmW,mBAAAA;IAKA,iNAAOjX,UAAAA,AAAO,EACZ,IACEc,OAAO,CAACgD,MAAR,CAAmC,CAACC,WAAD,EAAcpD,MAAd;YACjC,MAAM,EAACA,MAAM,EAAEuW,MAAAA,KAAUvW,MAAzB;YAEA,MAAMwW,gBAAgB,GAAGD,MAAM,CAACxF,UAAP,CAAkB0F,GAAlB,EAAuBvF,SAAD,GAAA,CAAgB;oBAC7D5E,SAAS,EAAE4E,SAAS,CAAC5E,SADwC;oBAE7DC,OAAO,EAAE+J,mBAAmB,CAACpF,SAAS,CAAC3E,OAAX,EAAoBvM,MAApB;iBAFiB,CAAtB,CAAzB;YAKA,OAAO,CAAC;mBAAGoD,WAAJ,EAAiB;mBAAGoT,gBAApB;aAAP;SARF,EASG,EATH,CAFU,EAYZ;QAACrW,OAAD;QAAUmW,mBAAV;KAZY,CAAd;AAcD;IChBWI,iBAAZ;AAAA,CAAA,SAAYA,iBAAAA;IACVA,iBAAAA,CAAAA,iBAAAA,CAAAA,SAAAA,GAAAA,EAAA,GAAA,QAAA;IACAA,iBAAAA,CAAAA,iBAAAA,CAAAA,iBAAAA,GAAAA,EAAA,GAAA,gBAAA;IACAA,iBAAAA,CAAAA,iBAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;AACD,CAJD,EAAYA,iBAAiB,IAAA,CAAjBA,iBAAiB,GAAA,CAAA,CAAA,CAA7B;AAMA,IAAYC,kBAAZ;AAAA,CAAA,SAAYA,kBAAAA;IACVA,kBAAAA,CAAAA,YAAA,GAAA,WAAA;AACD,CAFD,EAAYA,kBAAkB,IAAA,CAAlBA,kBAAkB,GAAA,CAAA,CAAA,CAA9B;AAYA,MAAMC,YAAY,GAAA,WAAA,GAAY,IAAIC,GAAJ,EAA9B;AAEA,SAAgBC,sBACdC,UAAAA,EAAAA,IAAAA;QACA,EAACC,QAAD,EAAWC,YAAX,EAAyBC,MAAAA;IAEzB,MAAM,CAACC,KAAD,EAAQC,QAAR,CAAA,6MAAoB9Z,WAAAA,AAAQ,EAA4B,IAA5B,CAAlC;IACA,MAAM,EAAC+Z,SAAD,EAAYjM,OAAZ,EAAqBkM,QAAAA,KAAYJ,MAAvC;IACA,MAAMK,aAAa,6MAAGrC,SAAAA,AAAM,EAAC6B,UAAD,CAA5B;IACA,MAAMlC,QAAQ,GAAG2C,UAAU,EAA3B;IACA,MAAMC,WAAW,IAAGC,0LAAAA,AAAc,EAAC7C,QAAD,CAAlC;IACA,MAAM8C,0BAA0B,6MAAGna,cAAAA,AAAW,EAC5C,SAACoa,GAAD;YAACA,QAAAA,KAAAA,GAAAA;YAAAA,MAA0B,EAAA;;QACzB,IAAIH,WAAW,CAACrI,OAAhB,EAAyB;YACvB;;QAGFgI,QAAQ,EAAE1X,KAAD;YACP,IAAIA,KAAK,KAAK,IAAd,EAAoB;gBAClB,OAAOkY,GAAP;;YAGF,OAAOlY,KAAK,CAACmY,MAAN,CAAaD,GAAG,CAACxX,MAAJ,EAAYhC,EAAD,GAAQ,CAACsB,KAAK,CAACqI,QAAN,CAAe3J,EAAf,CAApB,CAAb,CAAP;SALM,CAAR;KAN0C,EAc5C;QAACqZ,WAAD;KAd4C,CAA9C;IAgBA,MAAM5F,SAAS,6MAAGqD,SAAAA,AAAM,EAAwB,IAAxB,CAAxB;IACA,MAAM3S,cAAc,6KAAGuT,cAAW,AAAXA,GACpBgC,aAAD;QACE,IAAIjD,QAAQ,IAAI,CAACmC,QAAjB,EAA2B;YACzB,OAAOJ,YAAP;;QAGF,IACE,CAACkB,aAAD,IACAA,aAAa,KAAKlB,YADlB,IAEAW,aAAa,CAACnI,OAAd,KAA0B2H,UAF1B,IAGAI,KAAK,IAAI,IAJX,EAKE;YACA,MAAMV,GAAG,GAAY,IAAII,GAAJ,EAArB;YAEA,KAAK,IAAIlY,SAAT,IAAsBoY,UAAtB,CAAkC;gBAChC,IAAI,CAACpY,SAAL,EAAgB;oBACd;;gBAGF,IACEwY,KAAK,IACLA,KAAK,CAACjV,MAAN,GAAe,CADf,IAEA,CAACiV,KAAK,CAACpP,QAAN,CAAepJ,SAAS,CAACP,EAAzB,CAFD,IAGAO,SAAS,CAACsC,IAAV,CAAemO,OAJjB,EAKE;;oBAEAqH,GAAG,CAACsB,GAAJ,CAAQpZ,SAAS,CAACP,EAAlB,EAAsBO,SAAS,CAACsC,IAAV,CAAemO,OAArC;oBACA;;gBAGF,MAAMnI,IAAI,GAAGtI,SAAS,CAACsI,IAAV,CAAemI,OAA5B;gBACA,MAAMnO,IAAI,GAAGgG,IAAI,GAAG,IAAIuE,IAAJ,CAASJ,OAAO,CAACnE,IAAD,CAAhB,EAAwBA,IAAxB,CAAH,GAAmC,IAApD;gBAEAtI,SAAS,CAACsC,IAAV,CAAemO,OAAf,GAAyBnO,IAAzB;gBAEA,IAAIA,IAAJ,EAAU;oBACRwV,GAAG,CAACsB,GAAJ,CAAQpZ,SAAS,CAACP,EAAlB,EAAsB6C,IAAtB;;;YAIJ,OAAOwV,GAAP;;QAGF,OAAOqB,aAAP;KA3C8B,EA6ChC;QAACf,UAAD;QAAaI,KAAb;QAAoBH,QAApB;QAA8BnC,QAA9B;QAAwCzJ,OAAxC;KA7CgC,CAAlC;8MAgDAnO,YAAAA,AAAS,EAAC;QACRsa,aAAa,CAACnI,OAAd,GAAwB2H,UAAxB;KADO,EAEN;QAACA,UAAD;KAFM,CAAT;8MAIA9Z,YAAAA,AAAS,EACP;QACE,IAAI4X,QAAJ,EAAc;YACZ;;QAGF8C,0BAA0B;KANrB,EASP;QAACX,QAAD;QAAWnC,QAAX;KATO,CAAT;8MAYA5X,YAAAA,AAAS,EACP;QACE,IAAIka,KAAK,IAAIA,KAAK,CAACjV,MAAN,GAAe,CAA5B,EAA+B;YAC7BkV,QAAQ,CAAC,IAAD,CAAR;;KAHG,EAOP;QAAC3B,IAAI,CAACC,SAAL,CAAeyB,KAAf,CAAD;KAPO,CAAT;8MAUAla,YAAAA,AAAS,EACP;QACE,IACE4X,QAAQ,IACR,OAAOwC,SAAP,KAAqB,QADrB,IAEAxF,SAAS,CAACzC,OAAV,KAAsB,IAHxB,EAIE;YACA;;QAGFyC,SAAS,CAACzC,OAAV,GAAoBJ,UAAU,CAAC;YAC7B2I,0BAA0B;YAC1B9F,SAAS,CAACzC,OAAV,GAAoB,IAApB;SAF4B,EAG3BiI,SAH2B,CAA9B;KAVK,EAgBP;QAACA,SAAD;QAAYxC,QAAZ;QAAsB8C,0BAAtB,EAAkD;WAAGV,YAArD;KAhBO,CAAT;IAmBA,OAAO;QACL1U,cADK;QAELoV,0BAFK;QAGLK,kBAAkB,EAAEb,KAAK,IAAI;KAH/B;;IAMA,SAASK,UAAT;QACE,OAAQF,QAAR;YACE,KAAKZ,iBAAiB,CAACuB,MAAvB;gBACE,OAAO,KAAP;YACF,KAAKvB,iBAAiB,CAACwB,cAAvB;gBACE,OAAOlB,QAAP;YACF;gBACE,OAAO,CAACA,QAAR;;;AAGP;SCpKemB,gBAIdzY,KAAAA,EACA0Y,SAAAA;IAEA,iLAAOtC,cAAAA,AAAW,GACfgC,aAAD;QACE,IAAI,CAACpY,KAAL,EAAY;YACV,OAAO,IAAP;;QAGF,IAAIoY,aAAJ,EAAmB;YACjB,OAAOA,aAAP;;QAGF,OAAO,OAAOM,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAAC1Y,KAAD,CAA3C,GAAqDA,KAA5D;KAVc,EAYhB;QAAC0Y,SAAD;QAAY1Y,KAAZ;KAZgB,CAAlB;AAcD;SCtBe2Y,eACdpR,IAAAA,EACAmE,OAAAA;IAEA,OAAO+M,eAAe,CAAClR,IAAD,EAAOmE,OAAP,CAAtB;AACD;ACAD;;;IAIA,SAAgBkN,oBAAAA,IAAAA;QAAoB,EAACC,QAAD,EAAW1D,QAAAA;IAC7C,MAAM2D,eAAe,6KAAGC,WAAAA,AAAQ,EAACF,QAAD,CAAhC;IACA,MAAMG,gBAAgB,6MAAGrZ,UAAAA,AAAO,EAAC;QAC/B,IACEwV,QAAQ,IACR,OAAOnM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACiQ,gBAAd,KAAmC,WAHrC,EAIE;YACA,OAAOxK,SAAP;;QAGF,MAAM,EAACwK,gBAAAA,KAAoBjQ,MAA3B;QAEA,OAAO,IAAIiQ,gBAAJ,CAAqBH,eAArB,CAAP;KAX8B,EAY7B;QAACA,eAAD;QAAkB3D,QAAlB;KAZ6B,CAAhC;QAcA5X,kNAAAA,AAAS,EAAC;QACR,OAAO,IAAMyb,gBAAN,IAAA,OAAA,KAAA,IAAMA,gBAAgB,CAAEE,UAAlB,EAAb;KADO,EAEN;QAACF,gBAAD;KAFM,CAAT;IAIA,OAAOA,gBAAP;AACD;ACzBD;;;IAIA,SAAgBG,kBAAAA,IAAAA;QAAkB,EAACN,QAAD,EAAW1D,QAAAA;IAC3C,MAAMiE,YAAY,6KAAGL,WAAAA,AAAQ,EAACF,QAAD,CAA7B;IACA,MAAMQ,cAAc,6MAAG1Z,UAAAA,AAAO,EAC5B;QACE,IACEwV,QAAQ,IACR,OAAOnM,MAAP,KAAkB,WADlB,IAEA,OAAOA,MAAM,CAACsQ,cAAd,KAAiC,WAHnC,EAIE;YACA,OAAO7K,SAAP;;QAGF,MAAM,EAAC6K,cAAAA,KAAkBtQ,MAAzB;QAEA,OAAO,IAAIsQ,cAAJ,CAAmBF,YAAnB,CAAP;KAZ0B,EAe5B;QAACjE,QAAD;KAf4B,CAA9B;6MAkBA5X,aAAAA,AAAS,EAAC;QACR,OAAO,IAAM8b,cAAN,IAAA,OAAA,KAAA,IAAMA,cAAc,CAAEH,UAAhB,EAAb;KADO,EAEN;QAACG,cAAD;KAFM,CAAT;IAIA,OAAOA,cAAP;AACD;AC5BD,SAASE,cAAT,CAAwBzS,OAAxB;IACE,OAAO,IAAIgF,IAAJ,CAASjF,aAAa,CAACC,OAAD,CAAtB,EAAiCA,OAAjC,CAAP;AACD;AAED,SAAgB0S,QACd1S,OAAAA,EACA4E,OAAAA,EACA+N,YAAAA;QADA/N,YAAAA,KAAAA,GAAAA;QAAAA,UAAgD6N;;IAGhD,MAAM,CAAChY,IAAD,EAAOmY,OAAP,CAAA,6MAAkB9b,WAAAA,AAAQ,EAAoB,IAApB,CAAhC;IAEA,SAAS+b,WAAT;QACED,OAAO,EAAEE,WAAD;YACN,IAAI,CAAC9S,OAAL,EAAc;gBACZ,OAAO,IAAP;;YAGF,IAAIA,OAAO,CAAC+S,WAAR,KAAwB,KAA5B,EAAmC;gBAAA,IAAA;;;gBAGjC,OAAA,CAAA,OAAOD,WAAP,IAAA,OAAOA,WAAP,GAAsBH,YAAtB,KAAA,OAAA,OAAsC,IAAtC;;YAGF,MAAMK,OAAO,GAAGpO,OAAO,CAAC5E,OAAD,CAAvB;YAEA,IAAIiP,IAAI,CAACC,SAAL,CAAe4D,WAAf,MAAgC7D,IAAI,CAACC,SAAL,CAAe8D,OAAf,CAApC,EAA6D;gBAC3D,OAAOF,WAAP;;YAGF,OAAOE,OAAP;SAjBK,CAAP;;IAqBF,MAAMd,gBAAgB,GAAGJ,mBAAmB,CAAC;QAC3CC,QAAQ,EAACkB,OAAD;YACN,IAAI,CAACjT,OAAL,EAAc;gBACZ;;YAGF,KAAK,MAAMkT,MAAX,IAAqBD,OAArB,CAA8B;gBAC5B,MAAM,EAAC7b,IAAD,EAAOgG,MAAAA,KAAU8V,MAAvB;gBAEA,IACE9b,IAAI,KAAK,WAAT,IACAgG,MAAM,YAAY+V,WADlB,IAEA/V,MAAM,CAACgW,QAAP,CAAgBpT,OAAhB,CAHF,EAIE;oBACA6S,WAAW;oBACX;;;;KAfoC,CAA5C;IAoBA,MAAMN,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAEc;KAAZ,CAAxC;8KAEAQ,4BAAAA,AAAyB,EAAC;QACxBR,WAAW;QAEX,IAAI7S,OAAJ,EAAa;YACXuS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEe,OAAhB,CAAwBtT,OAAxB;YACAkS,gBAAgB,IAAA,IAAhB,GAAA,KAAA,IAAAA,gBAAgB,CAAEoB,OAAlB,CAA0B1Q,QAAQ,CAAC2Q,IAAnC,EAAyC;gBACvCC,SAAS,EAAE,IAD4B;gBAEvCC,OAAO,EAAE;aAFX;SAFF,MAMO;YACLlB,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;YACAF,gBAAgB,IAAA,IAAhB,GAAA,KAAA,IAAAA,gBAAgB,CAAEE,UAAlB;;KAXqB,EAatB;QAACpS,OAAD;KAbsB,CAAzB;IAeA,OAAOvF,IAAP;AACD;SC3EeiZ,aAAajZ,IAAAA;IAC3B,MAAMkZ,WAAW,GAAGhC,eAAe,CAAClX,IAAD,CAAnC;IAEA,OAAO+D,YAAY,CAAC/D,IAAD,EAAOkZ,WAAP,CAAnB;AACD;ACJD,MAAMvD,cAAY,GAAc,EAAhC;AAEA,SAAgBwD,uBAAuBnT,IAAAA;IACrC,MAAMoT,YAAY,6MAAGnF,SAAAA,AAAM,EAACjO,IAAD,CAA3B;IAEA,MAAMqT,SAAS,6KAAGxE,cAAAA,AAAW,GAC1BgC,aAAD;QACE,IAAI,CAAC7Q,IAAL,EAAW;YACT,OAAO2P,cAAP;;QAGF,IACEkB,aAAa,IACbA,aAAa,KAAKlB,cADlB,IAEA3P,IAFA,IAGAoT,YAAY,CAACjL,OAHb,IAIAnI,IAAI,CAACiB,UAAL,KAAoBmS,YAAY,CAACjL,OAAb,CAAqBlH,UAL3C,EAME;YACA,OAAO4P,aAAP;;QAGF,OAAOrQ,sBAAsB,CAACR,IAAD,CAA7B;KAhByB,EAkB3B;QAACA,IAAD;KAlB2B,CAA7B;IAqBAhK,sNAAAA,AAAS,EAAC;QACRod,YAAY,CAACjL,OAAb,GAAuBnI,IAAvB;KADO,EAEN;QAACA,IAAD;KAFM,CAAT;IAIA,OAAOqT,SAAP;AACD;SCvBeC,iBAAiBC,QAAAA;IAC/B,MAAM,CACJC,iBADI,EAEJC,oBAFI,CAAA,GAGFpd,qNAAAA,AAAQ,EAA2B,IAA3B,CAHZ;IAIA,MAAMqd,YAAY,GAAGzF,mNAAAA,AAAM,EAACsF,QAAD,CAA3B,EAAA,4CAAA;IAGA,MAAMI,YAAY,IAAGpd,uNAAAA,AAAW,GAAEK,KAAD;QAC/B,MAAMiK,gBAAgB,GAAGO,oBAAoB,CAACxK,KAAK,CAAC+F,MAAP,CAA7C;QAEA,IAAI,CAACkE,gBAAL,EAAuB;YACrB;;QAGF4S,oBAAoB,EAAED,iBAAD;YACnB,IAAI,CAACA,iBAAL,EAAwB;gBACtB,OAAO,IAAP;;YAGFA,iBAAiB,CAAC1C,GAAlB,CACEjQ,gBADF,EAEEmB,oBAAoB,CAACnB,gBAAD,CAFtB;YAKA,OAAO,IAAI+O,GAAJ,CAAQ4D,iBAAR,CAAP;SAVkB,CAApB;KAP8B,EAmB7B,EAnB6B,CAAhC;IAqBAxd,sNAAAA,AAAS,EAAC;QACR,MAAM4d,gBAAgB,GAAGF,YAAY,CAACvL,OAAtC;QAEA,IAAIoL,QAAQ,KAAKK,gBAAjB,EAAmC;YACjCC,OAAO,CAACD,gBAAD,CAAP;YAEA,MAAME,OAAO,GAAGP,QAAQ,CACrB/D,GADa,EACRjQ,OAAD;gBACH,MAAMwU,iBAAiB,GAAG3S,oBAAoB,CAAC7B,OAAD,CAA9C;gBAEA,IAAIwU,iBAAJ,EAAuB;oBACrBA,iBAAiB,CAACxO,gBAAlB,CAAmC,QAAnC,EAA6CoO,YAA7C,EAA2D;wBACzDvI,OAAO,EAAE;qBADX;oBAIA,OAAO;wBACL2I,iBADK;wBAEL/R,oBAAoB,CAAC+R,iBAAD,CAFf;qBAAP;;gBAMF,OAAO,IAAP;aAfY,EAiBb5a,MAjBa,CAmBVuD,KADF,IAKKA,KAAK,IAAI,IAvBF,CAAhB;YA0BA+W,oBAAoB,CAACK,OAAO,CAAC7Y,MAAR,GAAiB,IAAI2U,GAAJ,CAAQkE,OAAR,CAAjB,GAAoC,IAArC,CAApB;YAEAJ,YAAY,CAACvL,OAAb,GAAuBoL,QAAvB;;QAGF,OAAO;YACLM,OAAO,CAACN,QAAD,CAAP;YACAM,OAAO,CAACD,gBAAD,CAAP;SAFF;;QAKA,SAASC,OAAT,CAAiBN,QAAjB;YACEA,QAAQ,CAAC1c,OAAT,EAAkB0I,OAAD;gBACf,MAAMwU,iBAAiB,GAAG3S,oBAAoB,CAAC7B,OAAD,CAA9C;gBAEAwU,iBAAiB,IAAA,IAAjB,GAAA,KAAA,IAAAA,iBAAiB,CAAE3O,mBAAnB,CAAuC,QAAvC,EAAiDuO,YAAjD;aAHF;;KA3CK,EAiDN;QAACA,YAAD;QAAeJ,QAAf;KAjDM,CAAT;IAmDA,iNAAOnb,UAAAA,AAAO,EAAC;QACb,IAAImb,QAAQ,CAACtY,MAAb,EAAqB;YACnB,OAAOuY,iBAAiB,GACpBQ,KAAK,CAACC,IAAN,CAAWT,iBAAiB,CAACU,MAAlB,EAAX,EAAuChY,MAAvC,CACE,CAACkC,GAAD,EAAMqL,WAAN,OAAsBjT,4KAAG,AAAHA,EAAI4H,GAAD,EAAMqL,WAAN,CAD3B,EAEErQ,kBAFF,CADoB,GAKpB0K,gBAAgB,CAACyP,QAAD,CALpB;;QAQF,OAAOna,kBAAP;KAVY,EAWX;QAACma,QAAD;QAAWC,iBAAX;KAXW,CAAd;AAYD;SCpGeW,sBACd1P,aAAAA,EACAuL,YAAAA;QAAAA,iBAAAA,KAAAA,GAAAA;QAAAA,eAAsB,EAAA;;IAEtB,MAAMoE,oBAAoB,6MAAGnG,SAAAA,AAAM,EAAqB,IAArB,CAAnC;6MAEAjY,aAAAA,AAAS,EACP;QACEoe,oBAAoB,CAACjM,OAArB,GAA+B,IAA/B;KAFK,EAKP6H,YALO,CAAT;8MAQAha,YAAS,AAATA,EAAU;QACR,MAAMqe,gBAAgB,GAAG5P,aAAa,KAAKrL,kBAA3C;QAEA,IAAIib,gBAAgB,IAAI,CAACD,oBAAoB,CAACjM,OAA9C,EAAuD;YACrDiM,oBAAoB,CAACjM,OAArB,GAA+B1D,aAA/B;;QAGF,IAAI,CAAC4P,gBAAD,IAAqBD,oBAAoB,CAACjM,OAA9C,EAAuD;YACrDiM,oBAAoB,CAACjM,OAArB,GAA+B,IAA/B;;KARK,EAUN;QAAC1D,aAAD;KAVM,CAAT;IAYA,OAAO2P,oBAAoB,CAACjM,OAArB,GACHmM,qLAAAA,AAAQ,EAAC7P,aAAD,EAAgB2P,oBAAoB,CAACjM,OAArC,CADL,GAEH/O,kBAFJ;AAGD;SC7Bemb,eAAerb,OAAAA;8MAC7BlD,YAAAA,AAAS,EACP;QACE,IAAI,uKAACqL,YAAL,EAAgB;YACd;;QAGF,MAAMmT,WAAW,GAAGtb,OAAO,CAACsW,GAAR,CAAY;YAAA,IAAC,EAACzW,MAAAA,EAAF,GAAA;YAAA,OAAcA,MAAM,CAAC6T,KAArB,IAAA,OAAA,KAAA,IAAc7T,MAAM,CAAC6T,KAAP,EAAd;SAAZ,CAApB;QAEA,OAAO;YACL,KAAK,MAAMC,QAAX,IAAuB2H,WAAvB,CAAoC;gBAClC3H,QAAQ,IAAA,IAAR,GAAA,KAAA,IAAAA,QAAQ;;SAFZ;KARK;IAgBP3T,OAAO,CAACsW,GAAR,EAAY;QAAA,IAAC,EAACzW,MAAAA,EAAF,GAAA;QAAA,OAAcA,MAAd;KAAZ,CAhBO,CAAT;AAkBD;SCXe0b,sBACdre,SAAAA,EACAe,EAAAA;IAEA,iNAAOiB,UAAO,AAAPA,EAAQ;QACb,OAAOhC,SAAS,CAAC8F,MAAV,CACL,CAACkC,GAAD,EAAA;gBAAM,EAACiH,SAAD,EAAYC,OAAAA;YAChBlH,GAAG,CAACiH,SAAD,CAAH,IAAkBzO,KAAD;gBACf0O,OAAO,CAAC1O,KAAD,EAAQO,EAAR,CAAP;aADF;YAIA,OAAOiH,GAAP;SANG,EAQL,CAAA,CARK,CAAP;KADY,EAWX;QAAChI,SAAD;QAAYe,EAAZ;KAXW,CAAd;AAYD;SCzBeud,cAAcnV,OAAAA;IAC5B,iNAAOnH,UAAAA,AAAO,EAAC,IAAOmH,OAAO,GAAGK,mBAAmB,CAACL,OAAD,CAAtB,GAAkC,IAAjD,EAAwD;QACpEA,OADoE;KAAxD,CAAd;AAGD;ACED,MAAMoQ,cAAY,GAAW,EAA7B;AAEA,SAAgBgF,SACdpB,QAAAA,EACApP,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAA4C7E;;IAE5C,MAAM,CAACsV,YAAD,CAAA,GAAiBrB,QAAvB;IACA,MAAMsB,UAAU,GAAGH,aAAa,CAC9BE,YAAY,6KAAGnV,YAAAA,AAAS,EAACmV,YAAD,CAAZ,GAA6B,IADX,CAAhC;IAGA,MAAM,CAACE,KAAD,EAAQC,QAAR,CAAA,GAAoB1e,qNAAAA,AAAQ,EAAesZ,cAAf,CAAlC;IAEA,SAASqF,YAAT;QACED,QAAQ,CAAC;YACP,IAAI,CAACxB,QAAQ,CAACtY,MAAd,EAAsB;gBACpB,OAAO0U,cAAP;;YAGF,OAAO4D,QAAQ,CAAC/D,GAAT,EAAcjQ,OAAD,GAClB2C,0BAA0B,CAAC3C,OAAD,CAA1B,GACKsV,UADL,GAEI,IAAItQ,IAAJ,CAASJ,OAAO,CAAC5E,OAAD,CAAhB,EAA2BA,OAA3B,CAHC,CAAP;SALM,CAAR;;IAaF,MAAMuS,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAE0D;KAAZ,CAAxC;8KAEApC,4BAAAA,AAAyB,EAAC;QACxBd,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;QACAqD,YAAY;QACZzB,QAAQ,CAAC1c,OAAT,EAAkB0I,OAAD,GAAauS,cAAb,IAAA,OAAA,KAAA,IAAaA,cAAc,CAAEe,OAAhB,CAAwBtT,OAAxB,CAA9B;KAHuB,EAItB;QAACgU,QAAD;KAJsB,CAAzB;IAMA,OAAOuB,KAAP;AACD;SC3CeG,kBACdjV,IAAAA;IAEA,IAAI,CAACA,IAAL,EAAW;QACT,OAAO,IAAP;;IAGF,IAAIA,IAAI,CAACkV,QAAL,CAAcja,MAAd,GAAuB,CAA3B,EAA8B;QAC5B,OAAO+E,IAAP;;IAEF,MAAMmV,UAAU,GAAGnV,IAAI,CAACkV,QAAL,CAAc,CAAd,CAAnB;IAEA,iLAAOnU,gBAAa,AAAbA,EAAcoU,UAAD,CAAb,GAA4BA,UAA5B,GAAyCnV,IAAhD;AACD;SCHeoV,wBAAAA,IAAAA;QAAwB,EACtCjR,OAAAA;IAEA,MAAM,CAACnK,IAAD,EAAOmY,OAAP,CAAA,6MAAkB9b,WAAAA,AAAQ,EAAoB,IAApB,CAAhC;IACA,MAAMwb,YAAY,6MAAGtb,cAAAA,AAAW,GAC7Bud,OAAD;QACE,KAAK,MAAM,EAACnX,MAAAA,EAAZ,IAAuBmX,OAAvB,CAAgC;YAC9B,IAAI/S,0LAAa,AAAbA,EAAcpE,MAAD,CAAjB,EAA2B;gBACzBwV,OAAO,EAAEnY,IAAD;oBACN,MAAMuY,OAAO,GAAGpO,OAAO,CAACxH,MAAD,CAAvB;oBAEA,OAAO3C,IAAI,GACP;wBAAC,GAAGA,IAAJ;wBAAUK,KAAK,EAAEkY,OAAO,CAAClY,KAAzB;wBAAgCE,MAAM,EAAEgY,OAAO,CAAChY,MAAAA;qBADzC,GAEPgY,OAFJ;iBAHK,CAAP;gBAOA;;;KAXwB,EAe9B;QAACpO,OAAD;KAf8B,CAAhC;IAiBA,MAAM2N,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAEO;KAAZ,CAAxC;IACA,MAAMwD,gBAAgB,4MAAG9e,eAAAA,AAAW,GACjCgJ,OAAD;QACE,MAAMS,IAAI,GAAGiV,iBAAiB,CAAC1V,OAAD,CAA9B;QAEAuS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;QAEA,IAAI3R,IAAJ,EAAU;YACR8R,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEe,OAAhB,CAAwB7S,IAAxB;;QAGFmS,OAAO,CAACnS,IAAI,GAAGmE,OAAO,CAACnE,IAAD,CAAV,GAAmB,IAAxB,CAAP;KAVgC,EAYlC;QAACmE,OAAD;QAAU2N,cAAV;KAZkC,CAApC;IAcA,MAAM,CAACwD,OAAD,EAAUC,MAAV,CAAA,6KAAoBC,aAAAA,AAAU,EAACH,gBAAD,CAApC;IAEA,iNAAOjd,UAAO,AAAPA,EACL,IAAA,CAAO;YACLkd,OADK;YAELtb,IAFK;YAGLub;SAHF,CADY,EAMZ;QAACvb,IAAD;QAAOsb,OAAP;QAAgBC,MAAhB;KANY,CAAd;AAQD;AC9CM,MAAME,cAAc,GAAG;IAC5B;QAAC1c,MAAM,EAAEsT,aAAT;QAAwBrT,OAAO,EAAE,CAAA;IAAjC,CAD4B;IAE5B;QAACD,MAAM,EAAEoO,cAAT;QAAyBnO,OAAO,EAAE,CAAA;IAAlC,CAF4B;CAAvB;AAKA,MAAM0c,WAAW,GAAY;IAACvN,OAAO,EAAE,CAAA;AAAV,CAA7B;AAEA,MAAMwN,6BAA6B,GAAyC;IACjF5e,SAAS,EAAE;QACToN,OAAO,EAAExE;KAFsE;IAIjFiW,SAAS,EAAE;QACTzR,OAAO,EAAExE,8BADA;QAET0Q,QAAQ,EAAEZ,iBAAiB,CAACoG,aAFnB;QAGTzF,SAAS,EAAEV,kBAAkB,CAACoG,SAAAA;KAPiD;IASjFC,WAAW,EAAE;QACX5R,OAAO,EAAE7E;;AAVsE,CAA5E;MCdM0W,+BAA+BpG;IAI1ClU,GAAG,CAACvE,EAAD,EAAA;;QACD,OAAOA,EAAE,IAAI,IAAN,GAAA,CAAA,aAAa,KAAA,CAAMuE,GAAN,CAAUvE,EAAV,CAAb,KAAA,OAAA,aAA8B+P,SAA9B,GAA0CA,SAAjD;;IAGF+O,OAAO,GAAA;QACL,OAAOjC,KAAK,CAACC,IAAN,CAAW,IAAA,CAAKC,MAAL,EAAX,CAAP;;IAGFgC,UAAU,GAAA;QACR,OAAO,IAAA,CAAKD,OAAL,GAAe9c,MAAf,EAAsB;YAAA,IAAC,EAACyU,QAAAA,EAAF,GAAA;YAAA,OAAgB,CAACA,QAAjB;SAAtB,CAAP;;IAGFuI,UAAU,CAAChf,EAAD,EAAA;;QACR,OAAA,CAAA,wBAAA,CAAA,YAAO,IAAA,CAAKuE,GAAL,CAASvE,EAAT,CAAP,KAAA,OAAA,KAAA,IAAO,UAAc6I,IAAd,CAAmBmI,OAA1B,KAAA,OAAA,wBAAqCjB,SAArC;;;ACfG,MAAMkP,oBAAoB,GAA4B;IAC3DC,cAAc,EAAE,IAD2C;IAE3Dnf,MAAM,EAAE,IAFmD;IAG3D+Q,UAAU,EAAE,IAH+C;IAI3DqO,cAAc,EAAE,IAJ2C;IAK3Dvb,UAAU,EAAE,IAL+C;IAM3Dwb,iBAAiB,EAAE,IANwC;IAO3DtH,cAAc,EAAA,WAAA,GAAE,IAAIW,GAAJ,EAP2C;IAQ3DtU,cAAc,EAAA,WAAA,GAAE,IAAIsU,GAAJ,EAR2C;IAS3DrU,mBAAmB,EAAA,WAAA,GAAE,IAAIya,sBAAJ,EATsC;IAU3D3e,IAAI,EAAE,IAVqD;IAW3D0e,WAAW,EAAE;QACXT,OAAO,EAAE;YACPnN,OAAO,EAAE;SAFA;QAIXnO,IAAI,EAAE,IAJK;QAKXub,MAAM,EAAE1c;KAhBiD;IAkB3DkL,mBAAmB,EAAE,EAlBsC;IAmB3D0J,uBAAuB,EAAE,EAnBkC;IAoB3D+I,sBAAsB,EAAEb,6BApBmC;IAqB3DjF,0BAA0B,EAAE7X,IArB+B;IAsB3Dgc,UAAU,EAAE,IAtB+C;IAuB3D9D,kBAAkB,EAAE;AAvBuC,CAAtD;AA0BA,MAAM0F,sBAAsB,GAA8B;IAC/DJ,cAAc,EAAE,IAD+C;IAE/DvM,UAAU,EAAE,EAFmD;IAG/D5S,MAAM,EAAE,IAHuD;IAI/Dof,cAAc,EAAE,IAJ+C;IAK/DI,iBAAiB,EAAE;QACjB3f,SAAS,EAAE;KANkD;IAQ/DL,QAAQ,EAAEmC,IARqD;IAS/DoW,cAAc,EAAA,WAAA,GAAE,IAAIW,GAAJ,EAT+C;IAU/DvY,IAAI,EAAE,IAVyD;IAW/DqZ,0BAA0B,EAAE7X;AAXmC,CAA1D;AAcA,MAAM8d,eAAe,GAAA,WAAA,GAAGhhB,0NAAAA,AAAa,EAC1C8gB,sBAD0C,CAArC;AAIA,MAAMG,aAAa,GAAA,WAAA,6MAAGjhB,gBAAAA,AAAa,EACxCygB,oBADwC,CAAnC;SC/CSS;IACd,OAAO;QACL9f,SAAS,EAAE;YACTG,MAAM,EAAE,IADC;YAETyT,kBAAkB,EAAE;gBAACpR,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;aAFrB;YAGTsd,KAAK,EAAE,IAAIlH,GAAJ,EAHE;YAITmH,SAAS,EAAE;gBAACxd,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;;SALlB;QAOLoc,SAAS,EAAE;YACT9F,UAAU,EAAE,IAAIkG,sBAAJ;;KARhB;AAWD;AAED,SAAgBgB,QAAQC,KAAAA,EAAcC,MAAAA;IACpC,OAAQA,MAAM,CAACvgB,IAAf;QACE,KAAKiC,MAAM,CAACyS,SAAZ;YACE,OAAO;gBACL,GAAG4L,KADE;gBAELlgB,SAAS,EAAE;oBACT,GAAGkgB,KAAK,CAAClgB,SADA;oBAET4T,kBAAkB,EAAEuM,MAAM,CAACvM,kBAFlB;oBAGTzT,MAAM,EAAEggB,MAAM,CAAChgB,MAAAA;;aALnB;QAQF,KAAK0B,MAAM,CAACue,QAAZ;YACE,IAAIF,KAAK,CAAClgB,SAAN,CAAgBG,MAAhB,IAA0B,IAA9B,EAAoC;gBAClC,OAAO+f,KAAP;;YAGF,OAAO;gBACL,GAAGA,KADE;gBAELlgB,SAAS,EAAE;oBACT,GAAGkgB,KAAK,CAAClgB,SADA;oBAETggB,SAAS,EAAE;wBACTxd,CAAC,EAAE2d,MAAM,CAACzN,WAAP,CAAmBlQ,CAAnB,GAAuB0d,KAAK,CAAClgB,SAAN,CAAgB4T,kBAAhB,CAAmCpR,CADpD;wBAETC,CAAC,EAAE0d,MAAM,CAACzN,WAAP,CAAmBjQ,CAAnB,GAAuByd,KAAK,CAAClgB,SAAN,CAAgB4T,kBAAhB,CAAmCnR,CAAAA;;;aANnE;QAUF,KAAKZ,MAAM,CAACwe,OAAZ;QACA,KAAKxe,MAAM,CAACye,UAAZ;YACE,OAAO;gBACL,GAAGJ,KADE;gBAELlgB,SAAS,EAAE;oBACT,GAAGkgB,KAAK,CAAClgB,SADA;oBAETG,MAAM,EAAE,IAFC;oBAGTyT,kBAAkB,EAAE;wBAACpR,CAAC,EAAE,CAAJ;wBAAOC,CAAC,EAAE;qBAHrB;oBAITud,SAAS,EAAE;wBAACxd,CAAC,EAAE,CAAJ;wBAAOC,CAAC,EAAE;;;aANzB;QAUF,KAAKZ,MAAM,CAAC0e,iBAAZ;YAA+B;gBAC7B,MAAM,EAAC/X,OAAAA,KAAW2X,MAAlB;gBACA,MAAM,EAAC/f,EAAAA,KAAMoI,OAAb;gBACA,MAAMuQ,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;gBACAA,UAAU,CAACgB,GAAX,CAAe3Z,EAAf,EAAmBoI,OAAnB;gBAEA,OAAO;oBACL,GAAG0X,KADE;oBAELrB,SAAS,EAAE;wBACT,GAAGqB,KAAK,CAACrB,SADA;wBAET9F;;iBAJJ;;QASF,KAAKlX,MAAM,CAAC2e,oBAAZ;YAAkC;gBAChC,MAAM,EAACpgB,EAAD,EAAK0N,GAAL,EAAU+I,QAAAA,KAAYsJ,MAA5B;gBACA,MAAM3X,OAAO,GAAG0X,KAAK,CAACrB,SAAN,CAAgB9F,UAAhB,CAA2BpU,GAA3B,CAA+BvE,EAA/B,CAAhB;gBAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;oBACnC,OAAOoS,KAAP;;gBAGF,MAAMnH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;gBACAA,UAAU,CAACgB,GAAX,CAAe3Z,EAAf,EAAmB;oBACjB,GAAGoI,OADc;oBAEjBqO;iBAFF;gBAKA,OAAO;oBACL,GAAGqJ,KADE;oBAELrB,SAAS,EAAE;wBACT,GAAGqB,KAAK,CAACrB,SADA;wBAET9F;;iBAJJ;;QASF,KAAKlX,MAAM,CAAC4e,mBAAZ;YAAiC;gBAC/B,MAAM,EAACrgB,EAAD,EAAK0N,GAAAA,KAAOqS,MAAlB;gBACA,MAAM3X,OAAO,GAAG0X,KAAK,CAACrB,SAAN,CAAgB9F,UAAhB,CAA2BpU,GAA3B,CAA+BvE,EAA/B,CAAhB;gBAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;oBACnC,OAAOoS,KAAP;;gBAGF,MAAMnH,UAAU,GAAG,IAAIkG,sBAAJ,CAA2BiB,KAAK,CAACrB,SAAN,CAAgB9F,UAA3C,CAAnB;gBACAA,UAAU,CAACrZ,MAAX,CAAkBU,EAAlB;gBAEA,OAAO;oBACL,GAAG8f,KADE;oBAELrB,SAAS,EAAE;wBACT,GAAGqB,KAAK,CAACrB,SADA;wBAET9F;;iBAJJ;;QASF;YAAS;gBACP,OAAOmH,KAAP;;;AAGL;SCzGeQ,aAAAA,IAAAA;QAAa,EAAC7J,QAAAA;IAC5B,MAAM,EAAC1W,MAAD,EAASmf,cAAT,EAAyBpH,cAAAA,+MAAkBlZ,aAAAA,AAAU,EAAC4gB,eAAD,CAA3D;IACA,MAAMe,sBAAsB,6KAAG9I,cAAAA,AAAW,EAACyH,cAAD,CAA1C;IACA,MAAMsB,gBAAgB,IAAG/I,uLAAAA,AAAW,EAAC1X,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAEC,EAAT,CAApC,EAAA,+CAAA;6MAGAnB,aAAS,AAATA,EAAU;QACR,IAAI4X,QAAJ,EAAc;YACZ;;QAGF,IAAI,CAACyI,cAAD,IAAmBqB,sBAAnB,IAA6CC,gBAAgB,IAAI,IAArE,EAA2E;YACzE,IAAI,2KAACvP,kBAAAA,AAAe,EAACsP,sBAAD,CAApB,EAA8C;gBAC5C;;YAGF,IAAIvV,QAAQ,CAACyV,aAAT,KAA2BF,sBAAsB,CAAC/a,MAAtD,EAA8D;;gBAE5D;;YAGF,MAAMuS,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBic,gBAAnB,CAAtB;YAEA,IAAI,CAACzI,aAAL,EAAoB;gBAClB;;YAGF,MAAM,EAAChF,aAAD,EAAgBlK,IAAAA,KAAQkP,aAA9B;YAEA,IAAI,CAAChF,aAAa,CAAC/B,OAAf,IAA0B,CAACnI,IAAI,CAACmI,OAApC,EAA6C;gBAC3C;;YAGF0P,qBAAqB,CAAC;gBACpB,KAAK,MAAMtY,OAAX,IAAsB;oBAAC2K,aAAa,CAAC/B,OAAf;oBAAwBnI,IAAI,CAACmI,OAA7B;iBAAtB,CAA6D;oBAC3D,IAAI,CAAC5I,OAAL,EAAc;wBACZ;;oBAGF,MAAMuY,aAAa,6KAAGC,yBAAAA,AAAsB,EAACxY,OAAD,CAA5C;oBAEA,IAAIuY,aAAJ,EAAmB;wBACjBA,aAAa,CAACE,KAAd;wBACA;;;aAVe,CAArB;;KA3BK,EA0CN;QACD3B,cADC;QAEDzI,QAFC;QAGDqB,cAHC;QAID0I,gBAJC;QAKDD,sBALC;KA1CM,CAAT;IAkDA,OAAO,IAAP;AACD;SClEeO,eACdC,SAAAA,EAAAA,IAAAA;QACA,EAACxa,SAAD,EAAY,GAAGya;IAEf,OAAOD,SAAS,IAAA,IAAT,IAAAA,SAAS,CAAEjd,MAAX,GACHid,SAAS,CAAChc,MAAV,CAA4B,CAACC,WAAD,EAAc8B,QAAd;QAC1B,OAAOA,QAAQ,CAAC;YACdP,SAAS,EAAEvB,WADG;YAEd,GAAGgc,IAAAA;SAFU,CAAf;KADF,EAKGza,SALH,CADG,GAOHA,SAPJ;AAQD;SCVe0a,0BACdnI,MAAAA;IAEA,OAAO7X,oNAAAA,AAAO,EACZ,IAAA,CAAO;YACLrB,SAAS,EAAE;gBACT,GAAG4e,6BAA6B,CAAC5e,SADxB;gBAET,GAAGkZ,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAElZ,SAAX;aAHG;YAKL6e,SAAS,EAAE;gBACT,GAAGD,6BAA6B,CAACC,SADxB;gBAET,GAAG3F,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE2F,SAAX;aAPG;YASLG,WAAW,EAAE;gBACX,GAAGJ,6BAA6B,CAACI,WADtB;gBAEX,GAAG9F,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE8F,WAAX;;SAXJ,CADY,EAgBZ;QAAC9F,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAElZ,SAAT;QAAoBkZ,MAApB,IAAA,OAAA,KAAA,IAAoBA,MAAM,CAAE2F,SAA5B;QAAuC3F,MAAvC,IAAA,OAAA,KAAA,IAAuCA,MAAM,CAAE8F,WAA/C;KAhBY,CAAd;AAkBD;SCXesC,iCAAAA,IAAAA;QAAiC,EAC/CpQ,UAD+C,EAE/C9D,OAF+C,EAG/C+O,WAH+C,EAI/CjD,MAAM,GAAG,IAAA;IAET,MAAMqI,WAAW,OAAGrK,+MAAAA,AAAM,EAAC,KAAD,CAA1B;IACA,MAAM,EAAC1U,CAAD,EAAIC,CAAAA,KAAK,OAAOyW,MAAP,KAAkB,SAAlB,GAA8B;QAAC1W,CAAC,EAAE0W,MAAJ;QAAYzW,CAAC,EAAEyW;KAA7C,GAAuDA,MAAtE;8KAEA2C,4BAAAA,AAAyB,EAAC;QACxB,MAAMhF,QAAQ,GAAG,CAACrU,CAAD,IAAM,CAACC,CAAxB;QAEA,IAAIoU,QAAQ,IAAI,CAAC3F,UAAjB,EAA6B;YAC3BqQ,WAAW,CAACnQ,OAAZ,GAAsB,KAAtB;YACA;;QAGF,IAAImQ,WAAW,CAACnQ,OAAZ,IAAuB,CAAC+K,WAA5B,EAAyC;;;YAGvC;;QAIF,MAAMlT,IAAI,GAAGiI,UAAH,IAAA,OAAA,KAAA,IAAGA,UAAU,CAAEjI,IAAZ,CAAiBmI,OAA9B;QAEA,IAAI,CAACnI,IAAD,IAASA,IAAI,CAACsS,WAAL,KAAqB,KAAlC,EAAyC;;;YAGvC;;QAGF,MAAMtY,IAAI,GAAGmK,OAAO,CAACnE,IAAD,CAApB;QACA,MAAMuY,SAAS,GAAGxa,YAAY,CAAC/D,IAAD,EAAOkZ,WAAP,CAA9B;QAEA,IAAI,CAAC3Z,CAAL,EAAQ;YACNgf,SAAS,CAAChf,CAAV,GAAc,CAAd;;QAGF,IAAI,CAACC,CAAL,EAAQ;YACN+e,SAAS,CAAC/e,CAAV,GAAc,CAAd;;QAIF8e,WAAW,CAACnQ,OAAZ,GAAsB,IAAtB;QAEA,IAAIvO,IAAI,CAAC+J,GAAL,CAAS4U,SAAS,CAAChf,CAAnB,IAAwB,CAAxB,IAA6BK,IAAI,CAAC+J,GAAL,CAAS4U,SAAS,CAAC/e,CAAnB,IAAwB,CAAzD,EAA4D;YAC1D,MAAM2H,uBAAuB,GAAGD,0BAA0B,CAAClB,IAAD,CAA1D;YAEA,IAAImB,uBAAJ,EAA6B;gBAC3BA,uBAAuB,CAACmI,QAAxB,CAAiC;oBAC/BhP,GAAG,EAAEie,SAAS,CAAC/e,CADgB;oBAE/BY,IAAI,EAAEme,SAAS,CAAChf,CAAAA;iBAFlB;;;KAzCmB,EA+CtB;QAAC0O,UAAD;QAAa1O,CAAb;QAAgBC,CAAhB;QAAmB0Z,WAAnB;QAAgC/O,OAAhC;KA/CsB,CAAzB;AAgDD;ACoDM,MAAMqU,sBAAsB,GAAA,WAAA,6MAAG7iB,gBAAAA,AAAa,EAAY;IAC7D,GAAGyD,kBAD0D;IAE7DyE,MAAM,EAAE,CAFqD;IAG7DC,MAAM,EAAE;AAHqD,CAAZ,CAA5C;AAMP,IAAK2a,MAAL;AAAA,CAAA,SAAKA,MAAAA;IACHA,MAAAA,CAAAA,MAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;IACAA,MAAAA,CAAAA,MAAAA,CAAAA,eAAAA,GAAAA,EAAA,GAAA,cAAA;IACAA,MAAAA,CAAAA,MAAAA,CAAAA,cAAAA,GAAAA,EAAA,GAAA,aAAA;AACD,CAJD,EAAKA,MAAM,IAAA,CAANA,MAAM,GAAA,CAAA,CAAA,CAAX;AAMA,MAAaC,UAAU,GAAA,WAAA,6MAAGC,OAAAA,AAAI,EAAC,SAASD,UAAT,CAAA,IAAA;;QAAoB,EACjDvhB,EADiD,EAEjDyhB,aAFiD,EAGjDvK,UAAU,GAAG,IAHoC,EAIjD6G,QAJiD,EAKjDhc,OAAO,GAAGuc,cALuC,EAMjDoD,kBAAkB,GAAGzb,gBAN4B,EAOjD0b,SAPiD,EAQjDZ,SARiD,EASjD,GAAG9Q;IAEH,MAAM2R,KAAK,6MAAGC,aAAAA,AAAU,EAAChC,OAAD,EAAU9P,SAAV,EAAqB2P,eAArB,CAAxB;IACA,MAAM,CAACI,KAAD,EAAQvgB,QAAR,CAAA,GAAoBqiB,KAA1B;IACA,MAAM,CAACE,oBAAD,EAAuBC,uBAAvB,CAAA,GACJ/iB,qBAAqB,EADvB;IAEA,MAAM,CAACgjB,MAAD,EAASC,SAAT,CAAA,OAAsB/iB,iNAAAA,AAAQ,EAASoiB,MAAM,CAACY,aAAhB,CAApC;IACA,MAAMC,aAAa,GAAGH,MAAM,KAAKV,MAAM,CAACc,WAAxC;IACA,MAAM,EACJxiB,SAAS,EAAE,EAACG,MAAM,EAAEsiB,QAAT,EAAmB1C,KAAK,EAAE7H,cAA1B,EAA0C8H,SAAAA,EADjD,EAEJnB,SAAS,EAAE,EAAC9F,UAAU,EAAEvU,mBAAAA,OACtB0b,KAHJ;IAIA,MAAMjX,IAAI,GAAGwZ,QAAQ,IAAI,IAAZ,GAAmBvK,cAAc,CAACvT,GAAf,CAAmB8d,QAAnB,CAAnB,GAAkD,IAA/D;IACA,MAAMC,WAAW,GAAGxL,mNAAAA,AAAM,EAA4B;QACpDyL,OAAO,EAAE,IAD2C;QAEpDC,UAAU,EAAE;KAFY,CAA1B;IAIA,MAAMziB,MAAM,6MAAGkB,UAAO,AAAPA,EACb;QAAA,IAAA;QAAA,OACEohB,QAAQ,IAAI,IAAZ,GACI;YACEriB,EAAE,EAAEqiB,QADN;;YAGE/e,IAAI,EAAA,CAAA,aAAEuF,IAAF,IAAA,OAAA,KAAA,IAAEA,IAAI,CAAEvF,IAAR,KAAA,OAAA,aAAgBib,WAHtB;YAIE1b,IAAI,EAAEyf;SALZ,GAOI,IARN;KADoB,EAUpB;QAACD,QAAD;QAAWxZ,IAAX;KAVoB,CAAtB;IAYA,MAAM4Z,SAAS,GAAG3L,mNAAAA,AAAM,EAA0B,IAA1B,CAAxB;IACA,MAAM,CAAC4L,YAAD,EAAeC,eAAf,CAAA,6MAAkCzjB,WAAAA,AAAQ,EAAwB,IAAxB,CAAhD;IACA,MAAM,CAACggB,cAAD,EAAiB0D,iBAAjB,CAAA,4MAAsC1jB,YAAAA,AAAQ,EAAe,IAAf,CAApD;IACA,MAAM2jB,WAAW,6KAAGvJ,iBAAAA,AAAc,EAACrJ,KAAD,EAAQ/N,MAAM,CAAC6a,MAAP,CAAc9M,KAAd,CAAR,CAAlC;IACA,MAAM6S,sBAAsB,6KAAGhiB,cAAAA,AAAW,EAAA,kBAAmBd,EAAnB,CAA1C;IACA,MAAM+iB,0BAA0B,6MAAG9hB,UAAAA,AAAO,EACxC,IAAMmD,mBAAmB,CAAC2a,UAApB,EADkC,EAExC;QAAC3a,mBAAD;KAFwC,CAA1C;IAIA,MAAMib,sBAAsB,GAAG4B,yBAAyB,CAACU,SAAD,CAAxD;IACA,MAAM,EAACxd,cAAD,EAAiBoV,0BAAjB,EAA6CK,kBAAAA,KACjDlB,qBAAqB,CAACqK,0BAAD,EAA6B;QAChDnK,QAAQ,EAAEuJ,aADsC;QAEhDtJ,YAAY,EAAE;YAAC+G,SAAS,CAACxd,CAAX;YAAcwd,SAAS,CAACvd,CAAxB;SAFkC;QAGhDyW,MAAM,EAAEuG,sBAAsB,CAACZ,SAAAA;KAHZ,CADvB;IAMA,MAAM3N,UAAU,GAAG+G,aAAa,CAACC,cAAD,EAAiBuK,QAAjB,CAAhC;IACA,MAAMW,qBAAqB,6MAAG/hB,UAAAA,AAAO,EACnC,IAAOie,cAAc,6KAAGnc,sBAAAA,AAAmB,EAACmc,cAAD,CAAtB,GAAyC,IAD3B,EAEnC;QAACA,cAAD;KAFmC,CAArC;IAIA,MAAM+D,iBAAiB,GAAGC,sBAAsB,EAAhD;IACA,MAAMC,qBAAqB,GAAGlJ,cAAc,CAC1CnJ,UAD0C,EAE1CuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFS,CAA5C;IAKAkU,gCAAgC,CAAC;QAC/BpQ,UAAU,EAAEuR,QAAQ,IAAI,IAAZ,GAAmBvK,cAAc,CAACvT,GAAf,CAAmB8d,QAAnB,CAAnB,GAAkD,IAD/B;QAE/BvJ,MAAM,EAAEmK,iBAAiB,CAACG,uBAFK;QAG/BrH,WAAW,EAAEoH,qBAHkB;QAI/BnW,OAAO,EAAEqS,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAAAA;KAJZ,CAAhC;IAOA,MAAMmS,cAAc,GAAGrE,OAAO,CAC5BhK,UAD4B,EAE5BuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFL,EAG5BmW,qBAH4B,CAA9B;IAKA,MAAM/D,iBAAiB,GAAGtE,OAAO,CAC/BhK,UAAU,GAAGA,UAAU,CAACuS,aAAd,GAA8B,IADT,CAAjC;IAGA,MAAMC,aAAa,6MAAGxM,SAAAA,AAAM,EAAgB;QAC1CoI,cAAc,EAAE,IAD0B;QAE1Cnf,MAAM,EAAE,IAFkC;QAG1C+Q,UAH0C;QAI1C5M,aAAa,EAAE,IAJ2B;QAK1CN,UAAU,EAAE,IAL8B;QAM1CO,cAN0C;QAO1C2T,cAP0C;QAQ1CyL,YAAY,EAAE,IAR4B;QAS1CC,gBAAgB,EAAE,IATwB;QAU1Cpf,mBAV0C;QAW1ClE,IAAI,EAAE,IAXoC;QAY1C0M,mBAAmB,EAAE,EAZqB;QAa1C6W,uBAAuB,EAAE;KAbC,CAA5B;IAeA,MAAMC,QAAQ,GAAGtf,mBAAmB,CAAC4a,UAApB,CAAA,CAAA,wBACfsE,aAAa,CAACtS,OAAd,CAAsB9Q,IADP,KAAA,OAAA,KAAA,IACf,sBAA4BF,EADb,CAAjB;IAGA,MAAM4e,WAAW,GAAGX,uBAAuB,CAAC;QAC1CjR,OAAO,EAAEqS,sBAAsB,CAACT,WAAvB,CAAmC5R,OAAAA;KADH,CAA3C,EAAA,oDAAA;IAKA,MAAMuW,YAAY,GAAA,CAAA,wBAAG3E,WAAW,CAACT,OAAZ,CAAoBnN,OAAvB,KAAA,OAAA,wBAAkCF,UAApD;IACA,MAAM0S,gBAAgB,GAAGrB,aAAa,GAAA,CAAA,oBAClCvD,WAAW,CAAC/b,IADsB,KAAA,OAAA,oBACdsc,cADc,GAElC,IAFJ;IAGA,MAAMwE,eAAe,GAAGzQ,OAAO,CAC7B0L,WAAW,CAACT,OAAZ,CAAoBnN,OAApB,IAA+B4N,WAAW,CAAC/b,IADd,CAA/B,EAAA,wEAAA;;IAKA,MAAM+gB,aAAa,GAAG9H,YAAY,CAAC6H,eAAe,GAAG,IAAH,GAAUxE,cAA1B,CAAlC,EAAA,2CAAA;IAGA,MAAMzB,UAAU,GAAGH,aAAa,CAC9BgG,YAAY,6KAAGjb,YAAAA,AAAS,EAACib,YAAD,CAAZ,GAA6B,IADX,CAAhC,EAAA,gDAAA;IAKA,MAAM3W,mBAAmB,GAAGoP,sBAAsB,CAChDmG,aAAa,GAAGuB,QAAH,IAAA,OAAGA,QAAH,GAAe5S,UAAf,GAA4B,IADO,CAAlD;IAGA,MAAMwF,uBAAuB,GAAGkH,QAAQ,CAAC5Q,mBAAD,CAAxC,EAAA,kBAAA;IAGA,MAAMiX,iBAAiB,GAAG/C,cAAc,CAACC,SAAD,EAAY;QAClDxa,SAAS,EAAE;YACTnE,CAAC,EAAEwd,SAAS,CAACxd,CAAV,GAAcwhB,aAAa,CAACxhB,CADtB;YAETC,CAAC,EAAEud,SAAS,CAACvd,CAAV,GAAcuhB,aAAa,CAACvhB,CAFtB;YAGTqE,MAAM,EAAE,CAHC;YAITC,MAAM,EAAE;SALwC;QAOlDuY,cAPkD;QAQlDnf,MARkD;QASlDof,cATkD;QAUlDC,iBAVkD;QAWlDoE,gBAXkD;QAYlDtjB,IAAI,EAAEojB,aAAa,CAACtS,OAAd,CAAsB9Q,IAZsB;QAalD4jB,eAAe,EAAElF,WAAW,CAAC/b,IAbqB;QAclD+J,mBAdkD;QAelD0J,uBAfkD;QAgBlDoH;KAhBsC,CAAxC;IAmBA,MAAMrX,kBAAkB,GAAG2c,qBAAqB,OAC5C3jB,4KAAAA,AAAG,EAAC2jB,qBAAD,EAAwBpD,SAAxB,CADyC,GAE5C,IAFJ;IAIA,MAAMtS,aAAa,GAAG6O,gBAAgB,CAACvP,mBAAD,CAAtC,EAAA,2DAAA;IAEA,MAAMmX,gBAAgB,GAAG/G,qBAAqB,CAAC1P,aAAD,CAA9C,EAAA,oFAAA;IAEA,MAAM0W,qBAAqB,GAAGhH,qBAAqB,CAAC1P,aAAD,EAAgB;QACjE6R,cADiE;KAAhB,CAAnD;IAIA,MAAMsE,uBAAuB,6KAAGpkB,MAAAA,AAAG,EAACwkB,iBAAD,EAAoBE,gBAApB,CAAnC;IAEA,MAAM7f,aAAa,GAAGsf,gBAAgB,GAClCrc,eAAe,CAACqc,gBAAD,EAAmBK,iBAAnB,CADmB,GAElC,IAFJ;IAIA,MAAMjgB,UAAU,GACd7D,MAAM,IAAImE,aAAV,GACIwd,kBAAkB,CAAC;QACjB3hB,MADiB;QAEjBmE,aAFiB;QAGjBC,cAHiB;QAIjBC,mBAAmB,EAAE2e,0BAJJ;QAKjB1c;KALgB,CADtB,GAQI,IATN;IAUA,MAAM4d,MAAM,GAAGtgB,iBAAiB,CAACC,UAAD,EAAa,IAAb,CAAhC;IACA,MAAM,CAAC1D,IAAD,EAAOgkB,OAAP,CAAA,6MAAkBhlB,WAAAA,AAAQ,EAAc,IAAd,CAAhC,EAAA,iEAAA;;IAIA,MAAMilB,gBAAgB,GAAGR,eAAe,GACpCE,iBADoC,6KAEpCxkB,MAAAA,AAAG,EAACwkB,iBAAD,EAAoBG,qBAApB,CAFP;IAIA,MAAMzd,SAAS,GAAGD,WAAW,CAC3B6d,gBAD2B,EAAA,CAAA,aAE3BjkB,IAF2B,IAAA,OAAA,KAAA,IAE3BA,IAAI,CAAE2C,IAFqB,KAAA,OAAA,aAEb,IAFa,EAG3Bsc,cAH2B,CAA7B;IAMA,MAAMiF,eAAe,6MAAGtN,SAAAA,AAAM,EAAwB,IAAxB,CAA9B;IACA,MAAMuN,iBAAiB,OAAGjlB,oNAAAA,AAAW,EACnC,CACEK,KADF,EAAA;YAEE,EAACmC,MAAM,EAAEuW,MAAT,EAAiBtW,OAAAA;QAEjB,IAAI4gB,SAAS,CAACzR,OAAV,IAAqB,IAAzB,EAA+B;YAC7B;;QAGF,MAAMF,UAAU,GAAGgH,cAAc,CAACvT,GAAf,CAAmBke,SAAS,CAACzR,OAA7B,CAAnB;QAEA,IAAI,CAACF,UAAL,EAAiB;YACf;;QAGF,MAAMoO,cAAc,GAAGzf,KAAK,CAACoT,WAA7B;QAEA,MAAMyR,cAAc,GAAG,IAAInM,MAAJ,CAAW;YAChCpY,MAAM,EAAE0iB,SAAS,CAACzR,OADc;YAEhCF,UAFgC;YAGhCrR,KAAK,EAAEyf,cAHyB;YAIhCrd,OAJgC;;;YAOhCqP,OAAO,EAAEoS,aAPuB;YAQhCvO,OAAO,EAAC/U,EAAD;gBACL,MAAM+X,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;gBAEA,IAAI,CAAC+X,aAAL,EAAoB;oBAClB;;gBAGF,MAAM,EAACwM,WAAAA,KAAe1B,WAAW,CAAC7R,OAAlC;gBACA,MAAMvR,KAAK,GAAmB;oBAACO;iBAA/B;gBACAukB,WAAW,IAAA,IAAX,GAAA,KAAA,IAAAA,WAAW,CAAG9kB,KAAH,CAAX;gBACAqiB,oBAAoB,CAAC;oBAACtiB,IAAI,EAAE,aAAP;oBAAsBC;iBAAvB,CAApB;aAlB8B;YAoBhC+U,SAAS,EAACxU,EAAD,EAAKiT,UAAL,EAAiBO,kBAAjB,EAAqCe,MAArC;gBACP,MAAMwD,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;gBAEA,IAAI,CAAC+X,aAAL,EAAoB;oBAClB;;gBAGF,MAAM,EAACyM,aAAAA,KAAiB3B,WAAW,CAAC7R,OAApC;gBACA,MAAMvR,KAAK,GAAqB;oBAC9BO,EAD8B;oBAE9BiT,UAF8B;oBAG9BO,kBAH8B;oBAI9Be;iBAJF;gBAOAiQ,aAAa,IAAA,IAAb,GAAA,KAAA,IAAAA,aAAa,CAAG/kB,KAAH,CAAb;gBACAqiB,oBAAoB,CAAC;oBAACtiB,IAAI,EAAE,eAAP;oBAAwBC;iBAAzB,CAApB;aApC8B;YAsChCsR,OAAO,EAACyC,kBAAD;gBACL,MAAMxT,EAAE,GAAGyiB,SAAS,CAACzR,OAArB;gBAEA,IAAIhR,EAAE,IAAI,IAAV,EAAgB;oBACd;;gBAGF,MAAM+X,aAAa,GAAGD,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAtB;gBAEA,IAAI,CAAC+X,aAAL,EAAoB;oBAClB;;gBAGF,MAAM,EAACjY,WAAAA,KAAe+iB,WAAW,CAAC7R,OAAlC;gBACA,MAAMvR,KAAK,GAAmB;oBAC5Byf,cAD4B;oBAE5Bnf,MAAM,EAAE;wBAACC,EAAD;wBAAKsD,IAAI,EAAEyU,aAAa,CAACzU,IAAzB;wBAA+BT,IAAI,EAAEyf;;iBAF/C;iOAKAmC,0BAAAA,AAAuB,EAAC;oBACtB3kB,WAAW,IAAA,IAAX,GAAA,KAAA,IAAAA,WAAW,CAAGL,KAAH,CAAX;oBACAwiB,SAAS,CAACX,MAAM,CAACoD,YAAR,CAAT;oBACAnlB,QAAQ,CAAC;wBACPC,IAAI,EAAEiC,MAAM,CAACyS,SADN;wBAEPV,kBAFO;wBAGPzT,MAAM,EAAEC;qBAHF,CAAR;oBAKA8hB,oBAAoB,CAAC;wBAACtiB,IAAI,EAAE,aAAP;wBAAsBC;qBAAvB,CAApB;oBACAkjB,eAAe,CAACyB,eAAe,CAACpT,OAAjB,CAAf;oBACA4R,iBAAiB,CAAC1D,cAAD,CAAjB;iBAVqB,CAAvB;aAzD8B;YAsEhC3M,MAAM,EAACD,WAAD;gBACJ/S,QAAQ,CAAC;oBACPC,IAAI,EAAEiC,MAAM,CAACue,QADN;oBAEP1N;iBAFM,CAAR;aAvE8B;YA4EhCE,KAAK,EAAEmS,aAAa,CAACljB,MAAM,CAACwe,OAAR,CA5EY;YA6EhCvN,QAAQ,EAAEiS,aAAa,CAACljB,MAAM,CAACye,UAAR;SA7EF,CAAvB;QAgFAkE,eAAe,CAACpT,OAAhB,GAA0BsT,cAA1B;QAEA,SAASK,aAAT,CAAuBnlB,IAAvB;YACE,OAAO,eAAe2O,OAAf;gBACL,MAAM,EAACpO,MAAD,EAAS6D,UAAT,EAAqB1D,IAArB,EAA2BujB,uBAAAA,KAC/BH,aAAa,CAACtS,OADhB;gBAEA,IAAIvR,KAAK,GAAwB,IAAjC;gBAEA,IAAIM,MAAM,IAAI0jB,uBAAd,EAAuC;oBACrC,MAAM,EAACmB,UAAAA,KAAc/B,WAAW,CAAC7R,OAAjC;oBAEAvR,KAAK,GAAG;wBACNyf,cADM;wBAENnf,MAAM,EAAEA,MAFF;wBAGN6D,UAHM;wBAIN4K,KAAK,EAAEiV,uBAJD;wBAKNvjB;qBALF;oBAQA,IAAIV,IAAI,KAAKiC,MAAM,CAACwe,OAAhB,IAA2B,OAAO2E,UAAP,KAAsB,UAArD,EAAiE;wBAC/D,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,OAAR,CAAgBH,UAAU,CAACnlB,KAAD,CAA1B,CAA3B;wBAEA,IAAIolB,YAAJ,EAAkB;4BAChBrlB,IAAI,GAAGiC,MAAM,CAACye,UAAd;;;;gBAKNuC,SAAS,CAACzR,OAAV,GAAoB,IAApB;iOAEAyT,0BAAAA,AAAuB,EAAC;oBACtBllB,QAAQ,CAAC;wBAACC;qBAAF,CAAR;oBACAyiB,SAAS,CAACX,MAAM,CAACY,aAAR,CAAT;oBACAgC,OAAO,CAAC,IAAD,CAAP;oBACAvB,eAAe,CAAC,IAAD,CAAf;oBACAC,iBAAiB,CAAC,IAAD,CAAjB;oBACAwB,eAAe,CAACpT,OAAhB,GAA0B,IAA1B;oBAEA,MAAM9C,SAAS,GACb1O,IAAI,KAAKiC,MAAM,CAACwe,OAAhB,GAA0B,WAA1B,GAAwC,cAD1C;oBAGA,IAAIxgB,KAAJ,EAAW;wBACT,MAAM0O,OAAO,GAAG0U,WAAW,CAAC7R,OAAZ,CAAoB9C,SAApB,CAAhB;wBAEAC,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO,CAAG1O,KAAH,CAAP;wBACAqiB,oBAAoB,CAAC;4BAACtiB,IAAI,EAAE0O,SAAP;4BAAkBzO;yBAAnB,CAApB;;iBAfmB,CAAvB;aA3BF;;KApG+B,EAqJnC;QAACqY,cAAD;KArJmC,CAArC;IAwJA,MAAMkN,iCAAiC,IAAG5lB,uNAAAA,AAAW,EACnD,CACE+O,OADF,EAEEvM,MAFF;QAIE,OAAO,CAACnC,KAAD,EAAQM,MAAR;YACL,MAAM8S,WAAW,GAAGpT,KAAK,CAACoT,WAA1B;YACA,MAAMoS,mBAAmB,GAAGnN,cAAc,CAACvT,GAAf,CAAmBxE,MAAnB,CAA5B;YAEA,IAEE0iB,SAAS,CAACzR,OAAV,KAAsB,IAAtB,IAAA,sBAAA;YAEA,CAACiU,mBAFD,IAAA,kCAAA;YAIApS,WAAW,CAACqS,MAJZ,IAKArS,WAAW,CAACsS,gBAPd,EAQE;gBACA;;YAGF,MAAMC,iBAAiB,GAAG;gBACxBrlB,MAAM,EAAEklB;aADV;YAGA,MAAMI,cAAc,GAAGlX,OAAO,CAC5B1O,KAD4B,EAE5BmC,MAAM,CAACC,OAFqB,EAG5BujB,iBAH4B,CAA9B;YAMA,IAAIC,cAAc,KAAK,IAAvB,EAA6B;gBAC3BxS,WAAW,CAACqS,MAAZ,GAAqB;oBACnBI,UAAU,EAAE1jB,MAAM,CAACA,MAAAA;iBADrB;gBAIA6gB,SAAS,CAACzR,OAAV,GAAoBjR,MAApB;gBACAskB,iBAAiB,CAAC5kB,KAAD,EAAQmC,MAAR,CAAjB;;SA/BJ;KALiD,EAwCnD;QAACkW,cAAD;QAAiBuM,iBAAjB;KAxCmD,CAArD;IA2CA,MAAM1R,UAAU,GAAGsF,oBAAoB,CACrClW,OADqC,EAErCijB,iCAFqC,CAAvC;IAKA5H,cAAc,CAACrb,OAAD,CAAd;8KAEA0Z,4BAAAA,AAAyB,EAAC;QACxB,IAAI0D,cAAc,IAAI6C,MAAM,KAAKV,MAAM,CAACoD,YAAxC,EAAsD;YACpDzC,SAAS,CAACX,MAAM,CAACc,WAAR,CAAT;;KAFqB,EAItB;QAACjD,cAAD;QAAiB6C,MAAjB;KAJsB,CAAzB;KAMAnjB,qNAAS,AAATA,EACE;QACE,MAAM,EAACqC,UAAAA,KAAc2hB,WAAW,CAAC7R,OAAjC;QACA,MAAM,EAACjR,MAAD,EAASmf,cAAT,EAAyBtb,UAAzB,EAAqC1D,IAAAA,KAAQojB,aAAa,CAACtS,OAAjE;QAEA,IAAI,CAACjR,MAAD,IAAW,CAACmf,cAAhB,EAAgC;YAC9B;;QAGF,MAAMzf,KAAK,GAAkB;YAC3BM,MAD2B;YAE3Bmf,cAF2B;YAG3Btb,UAH2B;YAI3B4K,KAAK,EAAE;gBACLpM,CAAC,EAAEqhB,uBAAuB,CAACrhB,CADtB;gBAELC,CAAC,EAAEohB,uBAAuB,CAACphB,CAAAA;aANF;YAQ3BnC;SARF;QAWAukB,2OAAAA,AAAuB,EAAC;YACtBvjB,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,CAAGzB,KAAH,CAAV;YACAqiB,oBAAoB,CAAC;gBAACtiB,IAAI,EAAE,YAAP;gBAAqBC;aAAtB,CAApB;SAFqB,CAAvB;KApBK,EA0BP;QAACgkB,uBAAuB,CAACrhB,CAAzB;QAA4BqhB,uBAAuB,CAACphB,CAApD;KA1BO,CAAT;IA6BAxD,sNAAAA,AAAS,EACP;QACE,MAAM,EACJkB,MADI,EAEJmf,cAFI,EAGJtb,UAHI,EAIJQ,mBAJI,EAKJqf,uBAAAA,KACEH,aAAa,CAACtS,OANlB;QAQA,IACE,CAACjR,MAAD,IACA0iB,SAAS,CAACzR,OAAV,IAAqB,IADrB,IAEA,CAACkO,cAFD,IAGA,CAACuE,uBAJH,EAKE;YACA;;QAGF,MAAM,EAACxjB,UAAAA,KAAc4iB,WAAW,CAAC7R,OAAjC;QACA,MAAMuU,aAAa,GAAGnhB,mBAAmB,CAACG,GAApB,CAAwB0f,MAAxB,CAAtB;QACA,MAAM/jB,IAAI,GACRqlB,aAAa,IAAIA,aAAa,CAAC1iB,IAAd,CAAmBmO,OAApC,GACI;YACEhR,EAAE,EAAEulB,aAAa,CAACvlB,EADpB;YAEE6C,IAAI,EAAE0iB,aAAa,CAAC1iB,IAAd,CAAmBmO,OAF3B;YAGE1N,IAAI,EAAEiiB,aAAa,CAACjiB,IAHtB;YAIEmT,QAAQ,EAAE8O,aAAa,CAAC9O,QAAAA;SAL9B,GAOI,IARN;QASA,MAAMhX,KAAK,GAAkB;YAC3BM,MAD2B;YAE3Bmf,cAF2B;YAG3Btb,UAH2B;YAI3B4K,KAAK,EAAE;gBACLpM,CAAC,EAAEqhB,uBAAuB,CAACrhB,CADtB;gBAELC,CAAC,EAAEohB,uBAAuB,CAACphB,CAAAA;aANF;YAQ3BnC;SARF;yNAWAukB,0BAAAA,AAAuB,EAAC;YACtBP,OAAO,CAAChkB,IAAD,CAAP;YACAD,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,CAAGR,KAAH,CAAV;YACAqiB,oBAAoB,CAAC;gBAACtiB,IAAI,EAAE,YAAP;gBAAqBC;aAAtB,CAApB;SAHqB,CAAvB;KAzCK,EAgDP;QAACwkB,MAAD;KAhDO,CAAT;8KAmDAxI,4BAAAA,AAAyB,EAAC;QACxB6H,aAAa,CAACtS,OAAd,GAAwB;YACtBkO,cADsB;YAEtBnf,MAFsB;YAGtB+Q,UAHsB;YAItB5M,aAJsB;YAKtBN,UALsB;YAMtBO,cANsB;YAOtB2T,cAPsB;YAQtByL,YARsB;YAStBC,gBATsB;YAUtBpf,mBAVsB;YAWtBlE,IAXsB;YAYtB0M,mBAZsB;YAatB6W;SAbF;QAgBAnB,WAAW,CAACtR,OAAZ,GAAsB;YACpBuR,OAAO,EAAEiB,gBADW;YAEpBhB,UAAU,EAAEte;SAFd;KAjBuB,EAqBtB;QACDnE,MADC;QAED+Q,UAFC;QAGDlN,UAHC;QAIDM,aAJC;QAKD4T,cALC;QAMDyL,YANC;QAODC,gBAPC;QAQDrf,cARC;QASDC,mBATC;QAUDlE,IAVC;QAWD0M,mBAXC;QAYD6W,uBAZC;KArBsB,CAAzB;IAoCA3N,eAAe,CAAC;QACd,GAAGmN,iBADW;QAEdzU,KAAK,EAAEoR,SAFO;QAGd3J,YAAY,EAAE/R,aAHA;QAIdmC,kBAJc;QAKduG,mBALc;QAMd0J;KANa,CAAf;IASA,MAAMkP,aAAa,6MAAGvkB,UAAAA,AAAO,EAAC;QAC5B,MAAMiQ,OAAO,GAA4B;YACvCnR,MADuC;YAEvC+Q,UAFuC;YAGvCqO,cAHuC;YAIvCD,cAJuC;YAKvCtb,UALuC;YAMvCwb,iBANuC;YAOvCR,WAPuC;YAQvC9G,cARuC;YASvC1T,mBATuC;YAUvCD,cAVuC;YAWvCjE,IAXuC;YAYvCqZ,0BAZuC;YAavC3M,mBAbuC;YAcvC0J,uBAduC;YAevC+I,sBAfuC;YAgBvCzF,kBAhBuC;YAiBvC8D;SAjBF;QAoBA,OAAOxM,OAAP;KArB2B,EAsB1B;QACDnR,MADC;QAED+Q,UAFC;QAGDqO,cAHC;QAIDD,cAJC;QAKDtb,UALC;QAMDwb,iBANC;QAODR,WAPC;QAQD9G,cARC;QASD1T,mBATC;QAUDD,cAVC;QAWDjE,IAXC;QAYDqZ,0BAZC;QAaD3M,mBAbC;QAcD0J,uBAdC;QAeD+I,sBAfC;QAgBDzF,kBAhBC;QAiBD8D,UAjBC;KAtB0B,CAA7B;IA0CA,MAAM+H,eAAe,6MAAGxkB,UAAAA,AAAO,EAAC;QAC9B,MAAMiQ,OAAO,GAA8B;YACzCgO,cADyC;YAEzCvM,UAFyC;YAGzC5S,MAHyC;YAIzCof,cAJyC;YAKzCI,iBAAiB,EAAE;gBACjB3f,SAAS,EAAEkjB;aAN4B;YAQzCvjB,QARyC;YASzCuY,cATyC;YAUzC5X,IAVyC;YAWzCqZ;SAXF;QAcA,OAAOrI,OAAP;KAf6B,EAgB5B;QACDgO,cADC;QAEDvM,UAFC;QAGD5S,MAHC;QAIDof,cAJC;QAKD5f,QALC;QAMDujB,sBANC;QAODhL,cAPC;QAQD5X,IARC;QASDqZ,0BATC;KAhB4B,CAA/B;IA4BA,6MACEnY,UAAAA,CAAAA,aAAA,CAAC7C,iBAAiB,CAACmnB,QAAnB,EAAA;QAA4BpkB,KAAK,EAAEygB;KAAnC,EACE3gB,gNAAAA,CAAAA,aAAA,CAACoe,eAAe,CAACkG,QAAjB,EAAA;QAA0BpkB,KAAK,EAAEmkB;KAAjC,wMACErkB,UAAAA,CAAAA,aAAA,CAACqe,aAAa,CAACiG,QAAf,EAAA;QAAwBpkB,KAAK,EAAEkkB;KAA/B,wMACEpkB,UAAAA,CAAAA,aAAA,CAACigB,sBAAsB,CAACqE,QAAxB,EAAA;QAAiCpkB,KAAK,EAAEiF;KAAxC,EACGwX,QADH,CADF,CADF,wMAME3c,UAAAA,CAAAA,aAAA,CAACkf,YAAD,EAAA;QAAc7J,QAAQ,EAAE,CAAAgL,aAAa,IAAA,IAAb,GAAA,KAAA,IAAAA,aAAa,CAAEkE,YAAf,MAAgC;KAAxD,CANF,CADF,EASEvkB,gNAAAA,CAAAA,aAAA,CAACf,aAAD,EAAA;QAAA,GACMohB,aAAAA;QACJjhB,uBAAuB,EAAEsiB;KAF3B,CATF,CADF;;IAiBA,SAASI,sBAAT;QACE,MAAM0C,8BAA8B,GAClC,CAAAlD,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAExS,iBAAd,MAAoC,KADtC;QAEA,MAAM2V,0BAA0B,GAC9B,OAAO3O,UAAP,KAAsB,QAAtB,GACIA,UAAU,CAAChB,OAAX,KAAuB,KAD3B,GAEIgB,UAAU,KAAK,KAHrB;QAIA,MAAMhB,OAAO,GACXiM,aAAa,IACb,CAACyD,8BADD,IAEA,CAACC,0BAHH;QAKA,IAAI,OAAO3O,UAAP,KAAsB,QAA1B,EAAoC;YAClC,OAAO;gBACL,GAAGA,UADE;gBAELhB;aAFF;;QAMF,OAAO;YAACA;SAAR;;AAEH,CAtnB6B,CAAvB;ACrGP,MAAM4P,WAAW,GAAA,WAAA,6MAAGtnB,gBAAAA,AAAa,EAAM,IAAN,CAAjC;AAEA,MAAMunB,WAAW,GAAG,QAApB;AAEA,MAAMC,SAAS,GAAG,WAAlB;AAEA,SAAgBC,aAAAA,IAAAA;QAAa,EAC3BjmB,EAD2B,EAE3BsD,IAF2B,EAG3BmT,QAAQ,GAAG,KAHgB,EAI3ByP,UAAAA;IAEA,MAAMxY,GAAG,6KAAG5M,cAAAA,AAAW,EAACklB,SAAD,CAAvB;IACA,MAAM,EACJrT,UADI,EAEJuM,cAFI,EAGJnf,MAHI,EAIJof,cAJI,EAKJI,iBALI,EAMJzH,cANI,EAOJ5X,IAAAA,+MACEtB,aAAAA,AAAU,EAAC4gB,eAAD,CARd;IASA,MAAM,EACJ2G,IAAI,GAAGJ,WADH,EAEJK,eAAe,GAAG,WAFd,EAGJC,QAAQ,GAAG,CAAA,KACTH,UAJE,IAAA,OAIFA,UAJE,GAIY,CAAA,CAJlB;IAKA,MAAMI,UAAU,GAAG,CAAAvmB,MAAM,IAAA,IAAN,GAAA,KAAA,IAAAA,MAAM,CAAEC,EAAR,MAAeA,EAAlC;IACA,MAAMuG,SAAS,OAAqB3H,mNAAAA,AAAU,EAC5C0nB,UAAU,GAAGjF,sBAAH,GAA4ByE,WADM,CAA9C;IAGA,MAAM,CAACjd,IAAD,EAAO0d,UAAP,CAAA,OAAqBlI,mLAAAA,AAAU,EAArC;IACA,MAAM,CAACtL,aAAD,EAAgByT,mBAAhB,CAAA,6KAAuCnI,aAAAA,AAAU,EAAvD;IACA,MAAMpf,SAAS,GAAGqe,qBAAqB,CAAC3K,UAAD,EAAa3S,EAAb,CAAvC;IACA,MAAMymB,OAAO,4KAAGnN,kBAAAA,AAAc,EAAChW,IAAD,CAA9B;8KAEAmY,4BAAAA,AAAyB,EACvB;QACE3D,cAAc,CAAC6B,GAAf,CAAmB3Z,EAAnB,EAAuB;YAACA,EAAD;YAAK0N,GAAL;YAAU7E,IAAV;YAAgBkK,aAAhB;YAA+BzP,IAAI,EAAEmjB;SAA5D;QAEA,OAAO;YACL,MAAM5d,IAAI,GAAGiP,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAb;YAEA,IAAI6I,IAAI,IAAIA,IAAI,CAAC6E,GAAL,KAAaA,GAAzB,EAA8B;gBAC5BoK,cAAc,CAACxY,MAAf,CAAsBU,EAAtB;;SAJJ;KAJqB,EAavB;QAAC8X,cAAD;QAAiB9X,EAAjB;KAbuB,CAAzB;IAgBA,MAAM0mB,kBAAkB,OAAwBzlB,gNAAAA,AAAO,EACrD,IAAA,CAAO;YACLklB,IADK;YAELE,QAFK;YAGL,iBAAiB5P,QAHZ;YAIL,gBAAgB6P,UAAU,IAAIH,IAAI,KAAKJ,WAAvB,GAAqC,IAArC,GAA4ChW,SAJvD;YAKL,wBAAwBqW,eALnB;YAML,oBAAoB7G,iBAAiB,CAAC3f,SAAAA;SANxC,CADqD,EASrD;QACE6W,QADF;QAEE0P,IAFF;QAGEE,QAHF;QAIEC,UAJF;QAKEF,eALF;QAME7G,iBAAiB,CAAC3f,SANpB;KATqD,CAAvD;IAmBA,OAAO;QACLG,MADK;QAELmf,cAFK;QAGLC,cAHK;QAIL+G,UAAU,EAAEQ,kBAJP;QAKLJ,UALK;QAMLrnB,SAAS,EAAEwX,QAAQ,GAAG1G,SAAH,GAAe9Q,SAN7B;QAOL4J,IAPK;QAQL3I,IARK;QASLqmB,UATK;QAULC,mBAVK;QAWLjgB;KAXF;AAaD;SCrHeogB;IACd,iNAAO/nB,aAAAA,AAAU,EAAC6gB,aAAD,CAAjB;AACD;ACsBD,MAAMuG,WAAS,GAAG,WAAlB;AAEA,MAAMY,2BAA2B,GAAG;IAClCC,OAAO,EAAE;AADyB,CAApC;AAIA,SAAgBC,aAAAA,IAAAA;QAAa,EAC3BxjB,IAD2B,EAE3BmT,QAAQ,GAAG,KAFgB,EAG3BzW,EAH2B,EAI3B+mB,oBAAAA;IAEA,MAAMrZ,GAAG,6KAAG5M,cAAAA,AAAW,EAACklB,WAAD,CAAvB;IACA,MAAM,EAACjmB,MAAD,EAASR,QAAT,EAAmBW,IAAnB,EAAyBqZ,0BAAAA,+MAC7B3a,aAAAA,AAAU,EAAC4gB,eAAD,CADZ;IAEA,MAAMwH,QAAQ,6MAAGlQ,SAAAA,AAAM,EAAC;QAACL;KAAF,CAAvB;IACA,MAAMwQ,uBAAuB,6MAAGnQ,SAAAA,AAAM,EAAC,KAAD,CAAtC;IACA,MAAMjU,IAAI,6MAAGiU,SAAAA,AAAM,EAAoB,IAApB,CAAnB;IACA,MAAMoQ,UAAU,6MAAGpQ,SAAAA,AAAM,EAAwB,IAAxB,CAAzB;IACA,MAAM,EACJL,QAAQ,EAAE0Q,sBADN,EAEJC,qBAFI,EAGJP,OAAO,EAAEQ,qBAAAA,KACP;QACF,GAAGT,2BADD;QAEF,GAAGG,oBAAAA;KANL;IAQA,MAAMvN,GAAG,6KAAGF,iBAAAA,AAAc,EAAC8N,qBAAD,IAAA,OAACA,qBAAD,GAA0BpnB,EAA1B,CAA1B;IACA,MAAM0a,YAAY,OAAGtb,oNAAAA,AAAW,EAC9B;QACE,IAAI,CAAC6nB,uBAAuB,CAACjW,OAA7B,EAAsC;;;YAGpCiW,uBAAuB,CAACjW,OAAxB,GAAkC,IAAlC;YACA;;QAGF,IAAIkW,UAAU,CAAClW,OAAX,IAAsB,IAA1B,EAAgC;YAC9BsD,YAAY,CAAC4S,UAAU,CAAClW,OAAZ,CAAZ;;QAGFkW,UAAU,CAAClW,OAAX,GAAqBJ,UAAU,CAAC;YAC9B2I,0BAA0B,CACxBsD,KAAK,CAACyK,OAAN,CAAc9N,GAAG,CAACxI,OAAlB,IAA6BwI,GAAG,CAACxI,OAAjC,GAA2C;gBAACwI,GAAG,CAACxI,OAAL;aADnB,CAA1B;YAGAkW,UAAU,CAAClW,OAAX,GAAqB,IAArB;SAJ6B,EAK5BqW,qBAL4B,CAA/B;KAb4B,EAqB9B;QAACA,qBAAD;KArB8B,CAAhC;IAuBA,MAAM1M,cAAc,GAAGF,iBAAiB,CAAC;QACvCN,QAAQ,EAAEO,YAD6B;QAEvCjE,QAAQ,EAAE0Q,sBAAsB,IAAI,CAACpnB;KAFC,CAAxC;IAIA,MAAMme,gBAAgB,6MAAG9e,cAAAA,AAAW,EAClC,CAACmoB,UAAD,EAAiCC,eAAjC;QACE,IAAI,CAAC7M,cAAL,EAAqB;YACnB;;QAGF,IAAI6M,eAAJ,EAAqB;YACnB7M,cAAc,CAAC8M,SAAf,CAAyBD,eAAzB;YACAP,uBAAuB,CAACjW,OAAxB,GAAkC,KAAlC;;QAGF,IAAIuW,UAAJ,EAAgB;YACd5M,cAAc,CAACe,OAAf,CAAuB6L,UAAvB;;KAZ8B,EAelC;QAAC5M,cAAD;KAfkC,CAApC;IAiBA,MAAM,CAACwD,OAAD,EAAUoI,UAAV,CAAA,4KAAwBlI,cAAAA,AAAU,EAACH,gBAAD,CAAxC;IACA,MAAMuI,OAAO,6KAAGnN,iBAAc,AAAdA,EAAehW,IAAD,CAA9B;IAEAzE,sNAAAA,AAAS,EAAC;QACR,IAAI,CAAC8b,cAAD,IAAmB,CAACwD,OAAO,CAACnN,OAAhC,EAAyC;YACvC;;QAGF2J,cAAc,CAACH,UAAf;QACAyM,uBAAuB,CAACjW,OAAxB,GAAkC,KAAlC;QACA2J,cAAc,CAACe,OAAf,CAAuByC,OAAO,CAACnN,OAA/B;KAPO,EAQN;QAACmN,OAAD;QAAUxD,cAAV;KARM,CAAT;QAUA9b,kNAAS,AAATA,EACE;QACEU,QAAQ,CAAC;YACPC,IAAI,EAAEiC,MAAM,CAAC0e,iBADN;YAEP/X,OAAO,EAAE;gBACPpI,EADO;gBAEP0N,GAFO;gBAGP+I,QAHO;gBAIP5N,IAAI,EAAEsV,OAJC;gBAKPtb,IALO;gBAMPS,IAAI,EAAEmjB;;SARF,CAAR;QAYA,OAAO,IACLlnB,QAAQ,CAAC;gBACPC,IAAI,EAAEiC,MAAM,CAAC4e,mBADN;gBAEP3S,GAFO;gBAGP1N;aAHM,CADV;KAdK,EAsBP;QAACA,EAAD;KAtBO,CAAT;8MAyBAnB,YAAAA,AAAS,EAAC;QACR,IAAI4X,QAAQ,KAAKuQ,QAAQ,CAAChW,OAAT,CAAiByF,QAAlC,EAA4C;YAC1ClX,QAAQ,CAAC;gBACPC,IAAI,EAAEiC,MAAM,CAAC2e,oBADN;gBAEPpgB,EAFO;gBAGP0N,GAHO;gBAIP+I;aAJM,CAAR;YAOAuQ,QAAQ,CAAChW,OAAT,CAAiByF,QAAjB,GAA4BA,QAA5B;;KATK,EAWN;QAACzW,EAAD;QAAK0N,GAAL;QAAU+I,QAAV;QAAoBlX,QAApB;KAXM,CAAT;IAaA,OAAO;QACLQ,MADK;QAEL8C,IAFK;QAGL6kB,MAAM,EAAE,CAAAxnB,IAAI,IAAA,IAAJ,GAAA,KAAA,IAAAA,IAAI,CAAEF,EAAN,MAAaA,EAHhB;QAIL6I,IAAI,EAAEsV,OAJD;QAKLje,IALK;QAMLqmB;KANF;AAQD;SC/IeoB,iBAAAA,IAAAA;QAAiB,EAACC,SAAD,EAAY7J,QAAAA;IAC3C,MAAM,CACJ8J,cADI,EAEJC,iBAFI,CAAA,6MAGF5oB,WAAAA,AAAQ,EAA4B,IAA5B,CAHZ;IAIA,MAAM,CAACkJ,OAAD,EAAU2f,UAAV,CAAA,6MAAwB7oB,WAAAA,AAAQ,EAAqB,IAArB,CAAtC;IACA,MAAM8oB,gBAAgB,6KAAGvQ,cAAW,AAAXA,EAAYsG,QAAD,CAApC;IAEA,IAAI,CAACA,QAAD,IAAa,CAAC8J,cAAd,IAAgCG,gBAApC,EAAsD;QACpDF,iBAAiB,CAACE,gBAAD,CAAjB;;8KAGFvM,4BAAAA,AAAyB,EAAC;QACxB,IAAI,CAACrT,OAAL,EAAc;YACZ;;QAGF,MAAMsF,GAAG,GAAGma,cAAH,IAAA,OAAA,KAAA,IAAGA,cAAc,CAAEna,GAA5B;QACA,MAAM1N,EAAE,GAAG6nB,cAAH,IAAA,OAAA,KAAA,IAAGA,cAAc,CAAE5X,KAAhB,CAAsBjQ,EAAjC;QAEA,IAAI0N,GAAG,IAAI,IAAP,IAAe1N,EAAE,IAAI,IAAzB,EAA+B;YAC7B8nB,iBAAiB,CAAC,IAAD,CAAjB;YACA;;QAGFhD,OAAO,CAACC,OAAR,CAAgB6C,SAAS,CAAC5nB,EAAD,EAAKoI,OAAL,CAAzB,EAAwC6f,IAAxC,CAA6C;YAC3CH,iBAAiB,CAAC,IAAD,CAAjB;SADF;KAbuB,EAgBtB;QAACF,SAAD;QAAYC,cAAZ;QAA4Bzf,OAA5B;KAhBsB,CAAzB;IAkBA,6MACEhH,UAAAA,CAAAA,aAAA,CAAA,qMAAA,CAAA,UAAA,CAAA,QAAA,EAAA,IAAA,EACG2c,QADH,EAEG8J,cAAc,6MAAGK,eAAAA,AAAY,EAACL,cAAD,EAAiB;QAACM,GAAG,EAAEJ;KAAvB,CAAf,GAAqD,IAFtE,CADF;AAMD;ACzCD,MAAMK,gBAAgB,GAAc;IAClChmB,CAAC,EAAE,CAD+B;IAElCC,CAAC,EAAE,CAF+B;IAGlCqE,MAAM,EAAE,CAH0B;IAIlCC,MAAM,EAAE;AAJ0B,CAApC;AAOA,SAAgB0hB,yBAAAA,IAAAA;QAAyB,EAACtK,QAAAA;IACxC,6MACE3c,UAAAA,CAAAA,aAAA,CAACoe,eAAe,CAACkG,QAAjB,EAAA;QAA0BpkB,KAAK,EAAEge;KAAjC,wMACEle,UAAAA,CAAAA,aAAA,CAACigB,sBAAsB,CAACqE,QAAxB,EAAA;QAAiCpkB,KAAK,EAAE8mB;KAAxC,EACGrK,QADH,CADF,CADF;AAOD;ACAD,MAAMuK,UAAU,GAAwB;IACtCvf,QAAQ,EAAE,OAD4B;IAEtCwf,WAAW,EAAE;AAFyB,CAAxC;AAKA,MAAMC,iBAAiB,IAAsBtJ,cAAD;IAC1C,MAAMuJ,mBAAmB,6KAAGxX,kBAAe,AAAfA,EAAgBiO,cAAD,CAA3C;IAEA,OAAOuJ,mBAAmB,GAAG,sBAAH,GAA4B1Y,SAAtD;AACD,CAJD;AAMO,MAAM2Y,iBAAiB,GAAA,WAAA,6MAAGC,aAAAA,AAAU,EACzC,CAAA,MAYER,GAZF;QACE,EACES,EADF,EAEE1J,cAFF,EAGE5Y,WAHF,EAIEyX,QAJF,EAKE8K,SALF,EAMEhmB,IANF,EAOEimB,KAPF,EAQEviB,SARF,EASEwiB,UAAU,GAAGP,iBAAAA;IAIf,IAAI,CAAC3lB,IAAL,EAAW;QACT,OAAO,IAAP;;IAGF,MAAMmmB,sBAAsB,GAAG1iB,WAAW,GACtCC,SADsC,GAEtC;QACE,GAAGA,SADL;QAEEG,MAAM,EAAE,CAFV;QAGEC,MAAM,EAAE;KALd;IAOA,MAAMsiB,MAAM,GAAoC;QAC9C,GAAGX,UAD2C;QAE9CplB,KAAK,EAAEL,IAAI,CAACK,KAFkC;QAG9CE,MAAM,EAAEP,IAAI,CAACO,MAHiC;QAI9CD,GAAG,EAAEN,IAAI,CAACM,GAJoC;QAK9CF,IAAI,EAAEJ,IAAI,CAACI,IALmC;QAM9CsD,SAAS,wKAAE2iB,MAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBJ,sBAAvB,CANmC;QAO9ChmB,eAAe,EACbsD,WAAW,IAAI4Y,cAAf,GACItc,0BAA0B,CACxBsc,cADwB,EAExBrc,IAFwB,CAD9B,GAKIkN,SAbwC;QAc9CgZ,UAAU,EACR,OAAOA,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAAC7J,cAAD,CADd,GAEI6J,UAjBwC;QAkB9C,GAAGD,KAAAA;KAlBL;IAqBA,6MAAO1nB,UAAK,CAACioB,aAAN,CACLT,EADK,EAEL;QACEC,SADF;QAEEC,KAAK,EAAEG,MAFT;QAGEd;KALG,EAOLpK,QAPK,CAAP;AASD,CAxDwC,CAApC;MCwDMuL,+BAA+B,GAC1CznB,OAD6C,KAEhB;YAAC,EAAC9B,MAAD,EAAS6e,WAAAA;QACvC,MAAM2K,cAAc,GAA2B,CAAA,CAA/C;QACA,MAAM,EAACN,MAAD,EAASJ,SAAAA,KAAahnB,OAA5B;QAEA,IAAIonB,MAAJ,IAAA,QAAIA,MAAM,CAAElpB,MAAZ,EAAoB;YAClB,KAAK,MAAM,CAAC2N,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAesM,MAAM,CAAClpB,MAAtB,CAA3B,CAA0D;gBACxD,IAAIuB,KAAK,KAAKyO,SAAd,EAAyB;oBACvB;;gBAGFwZ,cAAc,CAAC7b,GAAD,CAAd,GAAsB3N,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBU,gBAAlB,CAAmC9b,GAAnC,CAAtB;gBACA3N,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBW,WAAlB,CAA8B/b,GAA9B,EAAmCpM,KAAnC;;;QAIJ,IAAI2nB,MAAJ,IAAA,QAAIA,MAAM,CAAErK,WAAZ,EAAyB;YACvB,KAAK,MAAM,CAAClR,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAesM,MAAM,CAACrK,WAAtB,CAA3B,CAA+D;gBAC7D,IAAItd,KAAK,KAAKyO,SAAd,EAAyB;oBACvB;;gBAGF6O,WAAW,CAAC/V,IAAZ,CAAiBigB,KAAjB,CAAuBW,WAAvB,CAAmC/b,GAAnC,EAAwCpM,KAAxC;;;QAIJ,IAAIunB,SAAJ,IAAA,QAAIA,SAAS,CAAE9oB,MAAf,EAAuB;YACrBA,MAAM,CAAC8I,IAAP,CAAY6gB,SAAZ,CAAsBrqB,GAAtB,CAA0BwpB,SAAS,CAAC9oB,MAApC;;QAGF,IAAI8oB,SAAJ,IAAA,QAAIA,SAAS,CAAEjK,WAAf,EAA4B;YAC1BA,WAAW,CAAC/V,IAAZ,CAAiB6gB,SAAjB,CAA2BrqB,GAA3B,CAA+BwpB,SAAS,CAACjK,WAAzC;;QAGF,OAAO,SAASlC,OAAT;YACL,KAAK,MAAM,CAAChP,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACya,OAAP,CAAe4M,cAAf,CAA3B,CAA2D;gBACzDxpB,MAAM,CAAC8I,IAAP,CAAYigB,KAAZ,CAAkBW,WAAlB,CAA8B/b,GAA9B,EAAmCpM,KAAnC;;YAGF,IAAIunB,SAAJ,IAAA,QAAIA,SAAS,CAAE9oB,MAAf,EAAuB;gBACrBA,MAAM,CAAC8I,IAAP,CAAY6gB,SAAZ,CAAsBC,MAAtB,CAA6Bd,SAAS,CAAC9oB,MAAvC;;SANJ;IASD,CA5CM;AA8CP,MAAM6pB,uBAAuB,IAAqB;IAAA,IAAC,EACjDrjB,SAAS,EAAE,EAACgc,OAAD,EAAUsH,KAAAA,IAD2B,GAAA;IAAA,OAE5C;QACJ;YACEtjB,SAAS,wKAAE2iB,MAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuB7G,OAAvB;SAFT;QAIJ;YACEhc,SAAS,EAAE2iB,4KAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBS,KAAvB;SALT;KAF4C;AAAA,CAAlD;AAWA,MAAaC,iCAAiC,GAAmC;IAC/EC,QAAQ,EAAE,GADqE;IAE/EC,MAAM,EAAE,MAFuE;IAG/EC,SAAS,EAAEL,uBAHoE;IAI/EM,WAAW,EAAA,WAAA,GAAEZ,+BAA+B,CAAC;QAC3CL,MAAM,EAAE;YACNlpB,MAAM,EAAE;gBACNoqB,OAAO,EAAE;;;KAH6B;AAJmC,CAA1E;AAaP,SAAgBC,iBAAAA,KAAAA;QAAiB,EAC/BtR,MAD+B,EAE/BhB,cAF+B,EAG/B1T,mBAH+B,EAI/Bib,sBAAAA;IAEA,iLAAOhF,WAAAA,AAAQ,EAAY,CAACra,EAAD,EAAK6I,IAAL;QACzB,IAAIiQ,MAAM,KAAK,IAAf,EAAqB;YACnB;;QAGF,MAAMuR,eAAe,GAA8BvS,cAAc,CAACvT,GAAf,CAAmBvE,EAAnB,CAAnD;QAEA,IAAI,CAACqqB,eAAL,EAAsB;YACpB;;QAGF,MAAMvZ,UAAU,GAAGuZ,eAAe,CAACxhB,IAAhB,CAAqBmI,OAAxC;QAEA,IAAI,CAACF,UAAL,EAAiB;YACf;;QAGF,MAAMwZ,cAAc,GAAGxM,iBAAiB,CAACjV,IAAD,CAAxC;QAEA,IAAI,CAACyhB,cAAL,EAAqB;YACnB;;QAEF,MAAM,EAAC/jB,SAAAA,KAAa+B,sLAAAA,AAAS,EAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC,CAApB;QACA,MAAMnB,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;QAEA,IAAI,CAACmB,eAAL,EAAsB;YACpB;;QAGF,MAAMkgB,SAAS,GACb,OAAO9O,MAAP,KAAkB,UAAlB,GACIA,MADJ,GAEIyR,0BAA0B,CAACzR,MAAD,CAHhC;QAKA/L,sBAAsB,CACpB+D,UADoB,EAEpBuO,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAFb,CAAtB;QAKA,OAAO4a,SAAS,CAAC;YACf7nB,MAAM,EAAE;gBACNC,EADM;gBAENsD,IAAI,EAAE+mB,eAAe,CAAC/mB,IAFhB;gBAGNuF,IAAI,EAAEiI,UAHA;gBAINjO,IAAI,EAAEwc,sBAAsB,CAACzf,SAAvB,CAAiCoN,OAAjC,CAAyC8D,UAAzC;aALO;YAOfgH,cAPe;YAQf8G,WAAW,EAAE;gBACX/V,IADW;gBAEXhG,IAAI,EAAEwc,sBAAsB,CAACT,WAAvB,CAAmC5R,OAAnC,CAA2Csd,cAA3C;aAVO;YAYflmB,mBAZe;YAafib,sBAbe;YAcf9Y,SAAS,EAAEmB;SAdG,CAAhB;KAvCa,CAAf;AAwDD;AAED,SAAS6iB,0BAAT,CACE1oB,OADF;IAGE,MAAM,EAACkoB,QAAD,EAAWC,MAAX,EAAmBE,WAAnB,EAAgCD,SAAAA,KAAa;QACjD,GAAGH,iCAD8C;QAEjD,GAAGjoB,OAAAA;KAFL;IAKA,QAAO;YAAC,EAAC9B,MAAD,EAAS6e,WAAT,EAAsBrY,SAAtB,EAAiC,GAAGikB;QAC1C,IAAI,CAACT,QAAL,EAAe;;YAEb;;QAGF,MAAMvb,KAAK,GAAG;YACZpM,CAAC,EAAEwc,WAAW,CAAC/b,IAAZ,CAAiBI,IAAjB,GAAwBlD,MAAM,CAAC8C,IAAP,CAAYI,IAD3B;YAEZZ,CAAC,EAAEuc,WAAW,CAAC/b,IAAZ,CAAiBM,GAAjB,GAAuBpD,MAAM,CAAC8C,IAAP,CAAYM,GAAAA;SAFxC;QAKA,MAAMsnB,KAAK,GAAG;YACZ/jB,MAAM,EACJH,SAAS,CAACG,MAAV,KAAqB,CAArB,GACK3G,MAAM,CAAC8C,IAAP,CAAYK,KAAZ,GAAoBqD,SAAS,CAACG,MAA/B,GAAyCkY,WAAW,CAAC/b,IAAZ,CAAiBK,KAD9D,GAEI,CAJM;YAKZyD,MAAM,EACJJ,SAAS,CAACI,MAAV,KAAqB,CAArB,GACK5G,MAAM,CAAC8C,IAAP,CAAYO,MAAZ,GAAqBmD,SAAS,CAACI,MAAhC,GAA0CiY,WAAW,CAAC/b,IAAZ,CAAiBO,MAD/D,GAEI;SARR;QAUA,MAAMsnB,cAAc,GAAG;YACrBtoB,CAAC,EAAEmE,SAAS,CAACnE,CAAV,GAAcoM,KAAK,CAACpM,CADF;YAErBC,CAAC,EAAEkE,SAAS,CAAClE,CAAV,GAAcmM,KAAK,CAACnM,CAFF;YAGrB,GAAGooB,KAAAA;SAHL;QAMA,MAAME,kBAAkB,GAAGV,SAAS,CAAC;YACnC,GAAGO,IADgC;YAEnCzqB,MAFmC;YAGnC6e,WAHmC;YAInCrY,SAAS,EAAE;gBAACgc,OAAO,EAAEhc,SAAV;gBAAqBsjB,KAAK,EAAEa;;SAJL,CAApC;QAOA,MAAM,CAACE,aAAD,CAAA,GAAkBD,kBAAxB;QACA,MAAME,YAAY,GAAGF,kBAAkB,CAACA,kBAAkB,CAAC7mB,MAAnB,GAA4B,CAA7B,CAAvC;QAEA,IAAIuT,IAAI,CAACC,SAAL,CAAesT,aAAf,MAAkCvT,IAAI,CAACC,SAAL,CAAeuT,YAAf,CAAtC,EAAoE;;YAElE;;QAGF,MAAMnO,OAAO,GAAGwN,WAAH,IAAA,OAAA,KAAA,IAAGA,WAAW,CAAG;YAACnqB,MAAD;YAAS6e,WAAT;YAAsB,GAAG4L,IAAAA;SAA5B,CAA3B;QACA,MAAM5C,SAAS,GAAGhJ,WAAW,CAAC/V,IAAZ,CAAiBiiB,OAAjB,CAAyBH,kBAAzB,EAA6C;YAC7DZ,QAD6D;YAE7DC,MAF6D;YAG7De,IAAI,EAAE;SAHU,CAAlB;QAMA,OAAO,IAAIjG,OAAJ,EAAaC,OAAD;YACjB6C,SAAS,CAACoD,QAAV,GAAqB;gBACnBtO,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO;gBACPqI,OAAO;aAFT;SADK,CAAP;KAjDF;AAwDD;AC9RD,IAAIrX,GAAG,GAAG,CAAV;AAEA,SAAgBud,OAAOjrB,EAAAA;IACrB,iNAAOiB,UAAAA,AAAO,EAAC;QACb,IAAIjB,EAAE,IAAI,IAAV,EAAgB;YACd;;QAGF0N,GAAG;QACH,OAAOA,GAAP;KANY,EAOX;QAAC1N,EAAD;KAPW,CAAd;AAQD;MCaYkrB,WAAW,GAAA,WAAA,yMAAG9pB,UAAK,CAACogB,IAAN,EACzB;QAAC,EACClb,WAAW,GAAG,KADf,EAECyX,QAFD,EAGCoN,aAAa,EAAEC,mBAHhB,EAICtC,KAJD,EAKCC,UALD,EAMChI,SAND,EAOCsK,cAAc,GAAG,KAPlB,EAQCxC,SARD,EASCyC,MAAM,GAAG,GAAA;IAET,MAAM,EACJpM,cADI,EAEJnf,MAFI,EAGJof,cAHI,EAIJC,iBAJI,EAKJtH,cALI,EAMJ1T,mBANI,EAOJwa,WAPI,EAQJ1e,IARI,EASJmf,sBATI,EAUJzS,mBAVI,EAWJ0J,uBAXI,EAYJoH,UAAAA,KACEiJ,aAAa,EAbjB;IAcA,MAAMpgB,SAAS,6MAAG3H,aAAAA,AAAU,EAACyiB,sBAAD,CAA5B;IACA,MAAM3T,GAAG,GAAGud,MAAM,CAAClrB,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAEC,EAAT,CAAlB;IACA,MAAMurB,iBAAiB,GAAGzK,cAAc,CAACC,SAAD,EAAY;QAClD7B,cADkD;QAElDnf,MAFkD;QAGlDof,cAHkD;QAIlDC,iBAJkD;QAKlDoE,gBAAgB,EAAE5E,WAAW,CAAC/b,IALoB;QAMlD3C,IANkD;QAOlD4jB,eAAe,EAAElF,WAAW,CAAC/b,IAPqB;QAQlD+J,mBARkD;QASlD0J,uBATkD;QAUlD/P,SAVkD;QAWlDmX;KAXsC,CAAxC;IAaA,MAAM3B,WAAW,GAAGhC,eAAe,CAACoF,cAAD,CAAnC;IACA,MAAMgM,aAAa,GAAGf,gBAAgB,CAAC;QACrCtR,MAAM,EAAEsS,mBAD6B;QAErCtT,cAFqC;QAGrC1T,mBAHqC;QAIrCib;KAJoC,CAAtC,EAAA,4FAAA;;IAQA,MAAM8I,GAAG,GAAGpM,WAAW,GAAG6C,WAAW,CAACR,MAAf,GAAwBrO,SAA/C;IAEA,OACE3O,gNAAAA,CAAAA,aAAA,CAACinB,wBAAD,EAAA,IAAA,wMACEjnB,UAAAA,CAAAA,aAAA,CAACumB,gBAAD,EAAA;QAAkBC,SAAS,EAAEuD;KAA7B,EACGprB,MAAM,IAAI2N,GAAV,yMACCtM,UAAAA,CAAAA,aAAA,CAACsnB,iBAAD,EAAA;QACEhb,GAAG,EAAEA;QACL1N,EAAE,EAAED,MAAM,CAACC,EAAAA;QACXmoB,GAAG,EAAEA;QACLS,EAAE,EAAEyC;QACJnM,cAAc,EAAEA;QAChB5Y,WAAW,EAAEA;QACbuiB,SAAS,EAAEA;QACXE,UAAU,EAAEA;QACZlmB,IAAI,EAAEkZ;QACN+M,KAAK,EAAE;YACLwC,MADK;YAEL,GAAGxC,KAAAA;;QAELviB,SAAS,EAAEglB;KAdb,EAgBGxN,QAhBH,CADD,GAmBG,IApBN,CADF,CADF;AA0BD,CA9EwB,CAApB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84], "debugId": null}}]}