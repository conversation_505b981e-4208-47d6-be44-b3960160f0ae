module.exports = {

"[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "checkDatabaseHealth": (()=>checkDatabaseHealth),
    "closePool": (()=>closePool),
    "default": (()=>__TURBOPACK__default__export__),
    "getConnection": (()=>getConnection),
    "initializeDatabase": (()=>initializeDatabase),
    "query": (()=>query),
    "transaction": (()=>transaction)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
// إعداد اتصال PostgreSQL
const pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"]({
    user: process.env.POSTGRES_USER || 'postgres',
    host: process.env.POSTGRES_HOST || 'localhost',
    database: process.env.POSTGRES_DB || 'graduation_platform',
    password: process.env.POSTGRES_PASSWORD || 'password',
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
    ssl: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false
});
async function getConnection() {
    try {
        const client = await pool.connect();
        return client;
    } catch (error) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', error);
        throw new Error('فشل في الاتصال بقاعدة البيانات');
    }
}
async function query(text, params) {
    const client = await getConnection();
    try {
        const start = Date.now();
        const result = await client.query(text, params);
        const duration = Date.now() - start;
        // تسجيل الاستعلامات في وضع التطوير
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Executed query:', {
                text,
                duration,
                rows: result.rowCount
            });
        }
        return result;
    } catch (error) {
        console.error('خطأ في تنفيذ الاستعلام:', error);
        throw error;
    } finally{
        client.release();
    }
}
async function transaction(callback) {
    const client = await getConnection();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    } catch (error) {
        await client.query('ROLLBACK');
        throw error;
    } finally{
        client.release();
    }
}
async function closePool() {
    await pool.end();
}
async function checkDatabaseHealth() {
    try {
        const result = await query('SELECT NOW() as current_time');
        return result.rows.length > 0;
    } catch (error) {
        console.error('فشل في فحص حالة قاعدة البيانات:', error);
        return false;
    }
}
async function initializeDatabase() {
    try {
        console.log('بدء تهيئة قاعدة البيانات...');
        // إنشاء جدول المستخدمين
        await query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء جدول الفئات
        await query(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء جدول المنتجات
        await query(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء جدول المدارس
        await query(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء جدول الطلبات
        await query(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء جدول عناصر الطلب
        await query(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء جدول التقييمات
        await query(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `);
        // إنشاء جدول عناصر القائمة
        await query(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
        // إنشاء الفهارس لتحسين الأداء
        await query('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)');
        await query('CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)');
        await query('CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)');
        await query('CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)');
        await query('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)');
        await query('CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)');
        await query('CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)');
        await query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
        await query('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');
        console.log('تم تهيئة قاعدة البيانات بنجاح!');
    } catch (error) {
        console.error('خطأ في تهيئة قاعدة البيانات:', error);
        throw error;
    }
}
const __TURBOPACK__default__export__ = pool;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/lib/models/Product.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "ProductModel": (()=>ProductModel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
class ProductModel {
    // جلب جميع المنتجات مع الفلترة
    static async getAll(filters = {}) {
        let whereConditions = [];
        let params = [];
        let paramIndex = 1;
        // بناء شروط WHERE
        if (filters.category) {
            whereConditions.push(`category_id = $${paramIndex}`);
            params.push(filters.category);
            paramIndex++;
        }
        if (filters.available !== undefined) {
            whereConditions.push(`is_available = $${paramIndex}`);
            params.push(filters.available);
            paramIndex++;
        }
        if (filters.published !== undefined) {
            whereConditions.push(`is_published = $${paramIndex}`);
            params.push(filters.published);
            paramIndex++;
        }
        if (filters.search) {
            whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
            params.push(`%${filters.search}%`);
            paramIndex++;
        }
        if (filters.minPrice !== undefined) {
            whereConditions.push(`price >= $${paramIndex}`);
            params.push(filters.minPrice);
            paramIndex++;
        }
        if (filters.maxPrice !== undefined) {
            whereConditions.push(`price <= $${paramIndex}`);
            params.push(filters.maxPrice);
            paramIndex++;
        }
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        // ترتيب النتائج
        const sortBy = filters.sortBy || 'created_at';
        const sortOrder = filters.sortOrder || 'DESC';
        const orderClause = `ORDER BY ${sortBy} ${sortOrder}`;
        // حد النتائج والإزاحة
        const limit = filters.limit || 50;
        const offset = filters.offset || 0;
        const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
        params.push(limit, offset);
        // استعلام العد الكلي
        const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`;
        const countResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(countQuery, params.slice(0, -2));
        const total = parseInt(countResult.rows[0].total);
        // استعلام البيانات
        const dataQuery = `
      SELECT 
        id, name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count, created_at, updated_at
      FROM products 
      ${whereClause} 
      ${orderClause} 
      ${limitClause}
    `;
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(dataQuery, params);
        return {
            products: result.rows,
            total
        };
    }
    // جلب منتج واحد بالمعرف
    static async getById(id) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])('SELECT * FROM products WHERE id = $1', [
            id
        ]);
        return result.rows[0] || null;
    }
    // إنشاء منتج جديد
    static async create(productData) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      INSERT INTO products (
        name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
      ) RETURNING *
    `, [
            productData.name,
            productData.description,
            productData.category_id,
            productData.price,
            productData.rental_price,
            productData.colors,
            productData.sizes,
            productData.images,
            productData.stock_quantity,
            productData.is_available,
            productData.is_published,
            productData.features,
            JSON.stringify(productData.specifications),
            productData.rating || 0,
            productData.reviews_count || 0
        ]);
        return result.rows[0];
    }
    // تحديث منتج
    static async update(id, updates) {
        const setClause = [];
        const params = [];
        let paramIndex = 1;
        // بناء جملة SET ديناميكياً
        Object.entries(updates).forEach(([key, value])=>{
            if (key !== 'id' && key !== 'created_at' && value !== undefined) {
                if (key === 'specifications') {
                    setClause.push(`${key} = $${paramIndex}`);
                    params.push(JSON.stringify(value));
                } else {
                    setClause.push(`${key} = $${paramIndex}`);
                    params.push(value);
                }
                paramIndex++;
            }
        });
        if (setClause.length === 0) {
            throw new Error('لا توجد حقول للتحديث');
        }
        // إضافة updated_at
        setClause.push(`updated_at = NOW()`);
        params.push(id);
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      UPDATE products 
      SET ${setClause.join(', ')} 
      WHERE id = $${paramIndex} 
      RETURNING *
    `, params);
        return result.rows[0] || null;
    }
    // حذف منتج
    static async delete(id) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])('DELETE FROM products WHERE id = $1', [
            id
        ]);
        return result.rowCount > 0;
    }
    // تحديث تقييم المنتج
    static async updateRating(productId) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      UPDATE products 
      SET 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE product_id = $1
        ),
        reviews_count = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE product_id = $1
        ),
        updated_at = NOW()
      WHERE id = $1
    `, [
            productId
        ]);
    }
    // تحديث المخزون
    static async updateStock(productId, quantity) {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transaction"])(async (client)=>{
            // التحقق من المخزون الحالي
            const stockResult = await client.query('SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE', [
                productId
            ]);
            if (stockResult.rows.length === 0) {
                throw new Error('المنتج غير موجود');
            }
            const currentStock = stockResult.rows[0].stock_quantity;
            const newStock = currentStock + quantity;
            if (newStock < 0) {
                throw new Error('المخزون غير كافي');
            }
            // تحديث المخزون
            const updateResult = await client.query('UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2', [
                newStock,
                productId
            ]);
            return updateResult.rowCount > 0;
        });
    }
    // البحث في المنتجات
    static async search(searchTerm, limit = 20) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT * FROM products 
      WHERE 
        is_published = true 
        AND is_available = true 
        AND (
          name ILIKE $1 
          OR description ILIKE $1 
          OR $2 = ANY(features)
        )
      ORDER BY 
        CASE 
          WHEN name ILIKE $1 THEN 1
          WHEN description ILIKE $1 THEN 2
          ELSE 3
        END,
        rating DESC
      LIMIT $3
    `, [
            `%${searchTerm}%`,
            searchTerm,
            limit
        ]);
        return result.rows;
    }
    // جلب المنتجات الأكثر مبيعاً
    static async getBestSellers(limit = 10) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.is_published = true AND p.is_available = true
        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))
      GROUP BY p.id
      ORDER BY total_sold DESC, p.rating DESC
      LIMIT $1
    `, [
            limit
        ]);
        return result.rows;
    }
    // جلب المنتجات الجديدة
    static async getNewProducts(limit = 10) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true
      ORDER BY created_at DESC
      LIMIT $1
    `, [
            limit
        ]);
        return result.rows;
    }
    // جلب المنتجات ذات التقييم العالي
    static async getTopRated(limit = 10) {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true AND rating > 0
      ORDER BY rating DESC, reviews_count DESC
      LIMIT $1
    `, [
            limit
        ]);
        return result.rows;
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/app/api/products/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2f$Product$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/models/Product.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2f$Product$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2f$Product$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
async function GET(request) {
    try {
        // التحقق من حالة قاعدة البيانات
        const isHealthy = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkDatabaseHealth"])();
        if (!isHealthy) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'قاعدة البيانات غير متاحة'
            }, {
                status: 503
            });
        }
        const { searchParams } = new URL(request.url);
        // استخراج معاملات الفلترة
        const filters = {
            category: searchParams.get('category') || undefined,
            available: searchParams.get('available') === 'true' ? true : searchParams.get('available') === 'false' ? false : undefined,
            published: searchParams.get('published') === 'true' ? true : searchParams.get('published') === 'false' ? false : undefined,
            search: searchParams.get('search') || undefined,
            minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')) : undefined,
            maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')) : undefined,
            limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')) : 50,
            offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')) : 0,
            sortBy: searchParams.get('sortBy') || 'created_at',
            sortOrder: searchParams.get('sortOrder') || 'DESC'
        };
        // جلب المنتجات من قاعدة البيانات
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2f$Product$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ProductModel"].getAll(filters);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            products: result.products,
            total: result.total,
            page: Math.floor(filters.offset / filters.limit) + 1,
            totalPages: Math.ceil(result.total / filters.limit),
            filters: filters,
            source: 'postgresql'
        });
    } catch (error) {
        console.error('Error fetching products from PostgreSQL:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'فشل في جلب المنتجات من قاعدة البيانات'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        // التحقق من البيانات المطلوبة
        if (!body.name || !body.price) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'اسم المنتج والسعر مطلوبان'
            }, {
                status: 400
            });
        }
        // إنشاء المنتج
        const product = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2f$Product$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ProductModel"].create({
            name: body.name,
            description: body.description || '',
            category_id: body.category_id,
            price: parseFloat(body.price),
            rental_price: body.rental_price ? parseFloat(body.rental_price) : undefined,
            colors: Array.isArray(body.colors) ? body.colors : [],
            sizes: Array.isArray(body.sizes) ? body.sizes : [],
            images: Array.isArray(body.images) ? body.images : [],
            stock_quantity: parseInt(body.stock_quantity) || 0,
            is_available: body.is_available ?? true,
            is_published: body.is_published ?? true,
            features: Array.isArray(body.features) ? body.features : [],
            specifications: body.specifications || {},
            rating: 0,
            reviews_count: 0
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'تم إنشاء المنتج بنجاح',
            product: product
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating product:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'فشل في إنشاء المنتج'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { name, description, category, price, rental_price, colors, sizes, images, stock_quantity, is_available, is_published, features, specifications } = body;
        // التحقق من البيانات المطلوبة
        if (!name || !description || !category || !price) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'البيانات المطلوبة مفقودة'
            }, {
                status: 400
            });
        }
        // جلب المنتجات الحالية
        const products = MockDataManager.getProducts();
        // إنشاء المنتج الجديد
        const newProduct = {
            id: MockDataManager.generateId(),
            name,
            description,
            category,
            price: parseFloat(price),
            rental_price: rental_price ? parseFloat(rental_price) : undefined,
            colors: colors || [],
            sizes: sizes || [],
            images: images || [],
            stock_quantity: parseInt(stock_quantity) || 0,
            is_available: is_available ?? true,
            is_published: is_published ?? true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            features: features || [],
            specifications: specifications || {}
        };
        // حفظ المنتج
        products.push(newProduct);
        MockDataManager.saveProducts(products);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'تم إضافة المنتج بنجاح',
            product: newProduct
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Unexpected error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ غير متوقع'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8630c591._.js.map